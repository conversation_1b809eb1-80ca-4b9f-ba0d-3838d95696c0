package performance

import (
	"context"
	"fmt"
	"sync"
	"testing"
	"time"

	"github.com/redis/go-redis/v9"
	"github.com/stretchr/testify/require"

	"pxpat-backend/pkg/cache"
)

// BenchmarkCacheOptimizer_Get 缓存获取性能测试
func BenchmarkCacheOptimizer_Get(b *testing.B) {
	// 创建Redis客户端
	rdb := redis.NewClient(&redis.Options{
		Addr: "localhost:6379",
		DB:   15,
	})
	
	// 测试Redis连接
	ctx := context.Background()
	if err := rdb.Ping(ctx).Err(); err != nil {
		b.<PERSON>pf("Redis不可用，跳过性能测试: %v", err)
	}
	
	// 清空测试数据
	rdb.FlushDB(ctx)
	
	cacheManager := cache.NewRedisManager(rdb)
	optimizer := NewCacheOptimizer(rdb, cacheManager)
	
	// 预填充缓存数据
	for i := 0; i < 1000; i++ {
		key := fmt.Sprintf("test_key_%d", i)
		value := fmt.Sprintf("test_value_%d", i)
		optimizer.OptimizedSet(ctx, key, value, "content")
	}
	
	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		i := 0
		for pb.Next() {
			key := fmt.Sprintf("test_key_%d", i%1000)
			var result string
			optimizer.OptimizedGet(ctx, key, "content", &result)
			i++
		}
	})
	
	// 清理
	rdb.FlushDB(ctx)
	rdb.Close()
}

// BenchmarkCacheOptimizer_Set 缓存设置性能测试
func BenchmarkCacheOptimizer_Set(b *testing.B) {
	rdb := redis.NewClient(&redis.Options{
		Addr: "localhost:6379",
		DB:   15,
	})
	
	ctx := context.Background()
	if err := rdb.Ping(ctx).Err(); err != nil {
		b.Skipf("Redis不可用，跳过性能测试: %v", err)
	}
	
	rdb.FlushDB(ctx)
	
	cacheManager := cache.NewRedisManager(rdb)
	optimizer := NewCacheOptimizer(rdb, cacheManager)
	
	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		i := 0
		for pb.Next() {
			key := fmt.Sprintf("bench_key_%d", i)
			value := fmt.Sprintf("bench_value_%d", i)
			optimizer.OptimizedSet(ctx, key, value, "content")
			i++
		}
	})
	
	rdb.FlushDB(ctx)
	rdb.Close()
}

// BenchmarkCacheOptimizer_BatchGet 批量获取性能测试
func BenchmarkCacheOptimizer_BatchGet(b *testing.B) {
	rdb := redis.NewClient(&redis.Options{
		Addr: "localhost:6379",
		DB:   15,
	})
	
	ctx := context.Background()
	if err := rdb.Ping(ctx).Err(); err != nil {
		b.Skipf("Redis不可用，跳过性能测试: %v", err)
	}
	
	rdb.FlushDB(ctx)
	
	cacheManager := cache.NewRedisManager(rdb)
	optimizer := NewCacheOptimizer(rdb, cacheManager)
	
	// 预填充数据
	batchData := make(map[string]interface{})
	for i := 0; i < 100; i++ {
		key := fmt.Sprintf("batch_key_%d", i)
		value := fmt.Sprintf("batch_value_%d", i)
		batchData[key] = value
	}
	optimizer.BatchSet(ctx, batchData, "content")
	
	keys := make([]string, 100)
	for i := 0; i < 100; i++ {
		keys[i] = fmt.Sprintf("batch_key_%d", i)
	}
	
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		optimizer.BatchGet(ctx, keys, "content")
	}
	
	rdb.FlushDB(ctx)
	rdb.Close()
}

// BenchmarkConcurrentProcessor_Submit 并发处理器提交任务性能测试
func BenchmarkConcurrentProcessor_Submit(b *testing.B) {
	processor := NewConcurrentProcessor(10, 10000)
	defer processor.Stop()
	
	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		i := 0
		for pb.Next() {
			job := &TestJob{
				ID:       fmt.Sprintf("job_%d", i),
				Priority: 1,
				Duration: 1 * time.Millisecond,
			}
			processor.Submit(job)
			i++
		}
	})
}

// BenchmarkConcurrentProcessor_ProcessBatch 批量处理性能测试
func BenchmarkConcurrentProcessor_ProcessBatch(b *testing.B) {
	processor := NewConcurrentProcessor(10, 10000)
	defer processor.Stop()
	
	// 准备测试数据
	items := make([]interface{}, 1000)
	for i := 0; i < 1000; i++ {
		items[i] = fmt.Sprintf("item_%d", i)
	}
	
	processorFunc := func(ctx context.Context, item interface{}) error {
		// 模拟处理时间
		time.Sleep(100 * time.Microsecond)
		return nil
	}
	
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		processor.ProcessBatch(context.Background(), items, processorFunc)
	}
}

// BenchmarkConcurrentProcessor_ProcessConcurrently 并发处理性能测试
func BenchmarkConcurrentProcessor_ProcessConcurrently(b *testing.B) {
	processor := NewConcurrentProcessor(10, 10000)
	defer processor.Stop()
	
	// 准备测试任务
	tasks := make([]func() error, 100)
	for i := 0; i < 100; i++ {
		tasks[i] = func() error {
			time.Sleep(100 * time.Microsecond)
			return nil
		}
	}
	
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		processor.ProcessConcurrently(context.Background(), tasks, 10)
	}
}

// TestJob 测试任务
type TestJob struct {
	ID       string
	Priority int
	Duration time.Duration
}

func (tj *TestJob) Process(ctx context.Context) error {
	time.Sleep(tj.Duration)
	return nil
}

func (tj *TestJob) GetID() string {
	return tj.ID
}

func (tj *TestJob) GetPriority() int {
	return tj.Priority
}

// BenchmarkConcurrentAccess 并发访问性能测试
func BenchmarkConcurrentAccess(b *testing.B) {
	rdb := redis.NewClient(&redis.Options{
		Addr: "localhost:6379",
		DB:   15,
	})
	
	ctx := context.Background()
	if err := rdb.Ping(ctx).Err(); err != nil {
		b.Skipf("Redis不可用，跳过性能测试: %v", err)
	}
	
	rdb.FlushDB(ctx)
	
	cacheManager := cache.NewRedisManager(rdb)
	optimizer := NewCacheOptimizer(rdb, cacheManager)
	
	// 预填充数据
	for i := 0; i < 1000; i++ {
		key := fmt.Sprintf("concurrent_key_%d", i)
		value := fmt.Sprintf("concurrent_value_%d", i)
		optimizer.OptimizedSet(ctx, key, value, "content")
	}
	
	b.ResetTimer()
	
	// 测试并发读写
	var wg sync.WaitGroup
	
	// 启动读协程
	for i := 0; i < 10; i++ {
		wg.Add(1)
		go func(workerID int) {
			defer wg.Done()
			for j := 0; j < b.N/10; j++ {
				key := fmt.Sprintf("concurrent_key_%d", j%1000)
				var result string
				optimizer.OptimizedGet(ctx, key, "content", &result)
			}
		}(i)
	}
	
	// 启动写协程
	for i := 0; i < 5; i++ {
		wg.Add(1)
		go func(workerID int) {
			defer wg.Done()
			for j := 0; j < b.N/20; j++ {
				key := fmt.Sprintf("concurrent_write_key_%d_%d", workerID, j)
				value := fmt.Sprintf("concurrent_write_value_%d_%d", workerID, j)
				optimizer.OptimizedSet(ctx, key, value, "content")
			}
		}(i)
	}
	
	wg.Wait()
	
	rdb.FlushDB(ctx)
	rdb.Close()
}

// BenchmarkMemoryUsage 内存使用性能测试
func BenchmarkMemoryUsage(b *testing.B) {
	processor := NewConcurrentProcessor(5, 1000)
	defer processor.Stop()
	
	b.ResetTimer()
	
	for i := 0; i < b.N; i++ {
		// 创建大量任务测试内存使用
		jobs := make([]Job, 100)
		for j := 0; j < 100; j++ {
			jobs[j] = &TestJob{
				ID:       fmt.Sprintf("memory_job_%d_%d", i, j),
				Priority: 1,
				Duration: 1 * time.Microsecond,
			}
		}
		
		// 提交任务
		for _, job := range jobs {
			processor.Submit(job)
		}
		
		// 等待一段时间让任务处理完成
		time.Sleep(10 * time.Millisecond)
	}
}

// BenchmarkCacheHitRate 缓存命中率性能测试
func BenchmarkCacheHitRate(b *testing.B) {
	rdb := redis.NewClient(&redis.Options{
		Addr: "localhost:6379",
		DB:   15,
	})
	
	ctx := context.Background()
	if err := rdb.Ping(ctx).Err(); err != nil {
		b.Skipf("Redis不可用，跳过性能测试: %v", err)
	}
	
	rdb.FlushDB(ctx)
	
	cacheManager := cache.NewRedisManager(rdb)
	optimizer := NewCacheOptimizer(rdb, cacheManager)
	
	// 预填充50%的数据
	for i := 0; i < 500; i++ {
		key := fmt.Sprintf("hitrate_key_%d", i)
		value := fmt.Sprintf("hitrate_value_%d", i)
		optimizer.OptimizedSet(ctx, key, value, "content")
	}
	
	b.ResetTimer()
	
	// 测试缓存命中率（50%命中，50%未命中）
	for i := 0; i < b.N; i++ {
		key := fmt.Sprintf("hitrate_key_%d", i%1000)
		var result string
		optimizer.OptimizedGet(ctx, key, "content", &result)
	}
	
	// 输出命中率
	hitRate := optimizer.GetHitRate()
	b.Logf("缓存命中率: %.2f%%", hitRate*100)
	
	rdb.FlushDB(ctx)
	rdb.Close()
}

// 性能测试辅助函数
func init() {
	// 设置性能测试的默认参数
	testing.Init()
}
