package main

import (
	"context"
	"fmt"
	"pxpat-backend/cmd"
	client2 "pxpat-backend/internal/content-cluster/interaction-service/client"
	externalHandler "pxpat-backend/internal/content-cluster/interaction-service/external/handler"
	externalService "pxpat-backend/internal/content-cluster/interaction-service/external/service"
	intraHandler "pxpat-backend/internal/content-cluster/interaction-service/intra/handler"
	intraService "pxpat-backend/internal/content-cluster/interaction-service/intra/service"
	"pxpat-backend/internal/content-cluster/interaction-service/migrations"
	repository2 "pxpat-backend/internal/content-cluster/interaction-service/repository"
	repositoryImpl "pxpat-backend/internal/content-cluster/interaction-service/repository/impl"
	"pxpat-backend/internal/content-cluster/interaction-service/routes"
	"pxpat-backend/internal/content-cluster/interaction-service/types"
	"pxpat-backend/pkg/auth"
	configLoader "pxpat-backend/pkg/config"
	"pxpat-backend/pkg/consul"
	"pxpat-backend/pkg/consul/health"
	DBLoader "pxpat-backend/pkg/database"
	"pxpat-backend/pkg/logger"
	"pxpat-backend/pkg/middleware/cors"
	"pxpat-backend/pkg/middleware/metrics"
	"pxpat-backend/pkg/middleware/tracing"
	"pxpat-backend/pkg/opentelemetry"

	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog"
	"go.uber.org/fx"
	"gorm.io/gorm"

	"github.com/rs/zerolog/log"
)

// 提供配置
func provideConfig() *types.Config {
	clusterName := "content"
	serviceName := "interaction"

	return configLoader.Load[types.Config](configLoader.LoaderConfig{
		TypeName:    configLoader.Service,
		ClusterName: clusterName,
		ServiceName: serviceName,
		UseRedis:    false,
	})
}

// 提供日志器
func provideLogger(cfg *types.Config) zerolog.Logger {
	serviceName := "interaction"

	if err := logger.InitLogger(cfg.Log, serviceName); err != nil {
		log.Fatal().Err(err).Msg("Failed to initialize logger")
	}

	log.Info().Msg("Interaction service starting...")
	return log.Logger
}

// 提供数据库连接
func provideDatabase(cfg *types.Config) (*gorm.DB, error) {
	db, err := DBLoader.ConnectDB(cfg.Database)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to connect to database")
		return nil, err
	}
	log.Info().Msg("Database connected successfully")

	// 自动创建数据库
	migrations.AutoMigrate(db)
	log.Info().Msg("Database migration completed")

	return db, nil
}

// 提供JWT管理器
func provideJWTManager(cfg *types.Config) *auth.Manager {
	jwtManager := auth.NewJWTManager(cfg.JWT.SecretKey, cfg.JWT.Expiration, cfg.JWT.Issuer)
	log.Info().Msg("JWT manager initialized")
	return &jwtManager
}

// 提供OpenTelemetry提供者
func provideOTLPProvider(cfg *types.Config) (*opentelemetry.Provider, error) {
	if !cfg.OTLP.Tracing.Enabled && !cfg.OTLP.Metrics.Enabled {
		log.Info().Msg("OpenTelemetry disabled")
		return nil, nil
	}

	provider, err := opentelemetry.NewProvider(cfg.OTLP)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to initialize OpenTelemetry provider")
		return nil, err
	}

	log.Info().
		Bool("tracing_enabled", provider.IsTracingEnabled()).
		Bool("metrics_enabled", provider.IsMetricsEnabled()).
		Msg("OpenTelemetry provider initialized")

	return provider, nil
}

// 提供Repository
func provideAlbumRepository(db *gorm.DB) *repository2.AlbumRepository {
	albumRepository := repository2.NewAlbumRepository(db)
	log.Info().Msg("Album repository initialized")
	return albumRepository
}

// 提供合集内容Repository层
func provideAlbumContentRepository(db *gorm.DB) *repository2.AlbumContentRepository {
	albumContentRepo := repository2.NewAlbumContentRepository(db)
	log.Info().Msg("Album content repository initialized")
	return albumContentRepo
}

// 提供User服务客户端
func provideUserServiceClient(cfg *types.Config) client2.UserServiceClient {
	userServiceClient := client2.NewUserServiceClient(client2.UserServiceConfig{
		BaseURL: fmt.Sprintf("http://%s:%d", cfg.Server.AllServiceList.UserService.Host, cfg.Server.AllServiceList.UserService.Port),
		Timeout: cfg.Server.AllServiceList.UserService.Timeout,
	})
	log.Info().Msg("User service client initialized")
	return userServiceClient
}

// 提供Video服务客户端
func provideVideoServiceClient(cfg *types.Config) client2.VideoServiceClient {
	videoServiceClient := client2.NewVideoServiceClient(client2.VideoServiceConfig{
		BaseURL: fmt.Sprintf("http://%s:%d", cfg.Server.AllServiceList.VideoService.Host, cfg.Server.AllServiceList.VideoService.Port),
		Timeout: cfg.Server.AllServiceList.VideoService.Timeout,
	})
	log.Info().Msg("Video service client initialized")
	return videoServiceClient
}

// 提供Novel服务客户端
func provideNovelServiceClient(cfg *types.Config) client2.NovelServiceClient {
	novelServiceClient := client2.NewNovelServiceClient(client2.NovelServiceConfig{
		BaseURL: fmt.Sprintf("http://%s:%d", cfg.Server.AllServiceList.NovelService.Host, cfg.Server.AllServiceList.NovelService.Port),
		Timeout: cfg.Server.AllServiceList.NovelService.Timeout,
	})
	log.Info().Msg("Novel service client initialized")
	return novelServiceClient
}

// 提供Music服务客户端
func provideMusicServiceClient(cfg *types.Config) client2.MusicServiceClient {
	musicServiceClient := client2.NewMusicServiceClient(client2.MusicServiceConfig{
		BaseURL: fmt.Sprintf("http://%s:%d", cfg.Server.AllServiceList.MusicService.Host, cfg.Server.AllServiceList.MusicService.Port),
		Timeout: cfg.Server.AllServiceList.MusicService.Timeout,
	})
	log.Info().Msg("Music service client initialized")
	return musicServiceClient
}

// 提供Favorite相关Repository
func provideFavoriteFolderRepository(db *gorm.DB) repository2.FavoriteFolderRepository {
	repo := repositoryImpl.NewFavoriteFolderRepository(db)
	log.Info().Msg("Favorite folder repository initialized")
	return repo
}

func provideFavoriteItemRepository(db *gorm.DB) repository2.FavoriteItemRepository {
	repo := repositoryImpl.NewFavoriteItemRepository(db)
	log.Info().Msg("Favorite item repository initialized")
	return repo
}

func provideFavoriteStatsRepository(db *gorm.DB) repository2.FavoriteStatsRepository {
	repo := repositoryImpl.NewFavoriteStatsRepository(db)
	log.Info().Msg("Favorite stats repository initialized")
	return repo
}

// 提供Like相关Repository
func provideLikeStatsRepository(db *gorm.DB) repository2.LikeStatsRepository {
	repo := repositoryImpl.NewLikeStatsRepository(db)
	log.Info().Msg("Like stats repository initialized")
	return repo
}

func provideLikeRepository(db *gorm.DB, likeStatsRepo repository2.LikeStatsRepository) repository2.LikeRepository {
	repo := repositoryImpl.NewLikeRepository(db, likeStatsRepo)
	log.Info().Msg("Like repository initialized")
	return repo
}

// 提供PlayHistory相关Repository
func providePlayHistoryRepository(db *gorm.DB) repository2.PlayHistoryRepository {
	repo := repositoryImpl.NewPlayHistoryRepository(db)
	log.Info().Msg("Play history repository initialized")
	return repo
}

// 提供Favorite相关Service
func provideFavoriteFolderService(
	folderRepo repository2.FavoriteFolderRepository,
	itemRepo repository2.FavoriteItemRepository,
	favoriteStatsRepo repository2.FavoriteStatsRepository,
	userClient client2.UserServiceClient,
) *externalService.FavoriteFolderService {
	svc := externalService.NewFavoriteFolderService(folderRepo, itemRepo, favoriteStatsRepo, userClient)
	log.Info().Msg("Favorite folder service initialized")
	return svc
}

func provideFavoriteItemService(
	folderRepo repository2.FavoriteFolderRepository,
	itemRepo repository2.FavoriteItemRepository,
	favoriteStatsRepo repository2.FavoriteStatsRepository,
	likeRepo repository2.LikeRepository,
	userClient client2.UserServiceClient,
	videoClient client2.VideoServiceClient,
	novelClient client2.NovelServiceClient,
	musicClient client2.MusicServiceClient,
) *externalService.FavoriteItemService {
	svc := externalService.NewFavoriteItemService(folderRepo, itemRepo, favoriteStatsRepo, likeRepo, userClient, videoClient, novelClient, musicClient)
	log.Info().Msg("Favorite item service initialized")
	return svc
}

func provideInternalFavoriteService(
	folderRepo repository2.FavoriteFolderRepository,
	itemRepo repository2.FavoriteItemRepository,
	db *gorm.DB,
) *intraService.InternalFavoriteService {
	svc := intraService.NewInternalFavoriteService(folderRepo, itemRepo, db)
	log.Info().Msg("Internal favorite service initialized")
	return svc
}

// 提供Like相关Service
func provideLikeService(
	likeRepo repository2.LikeRepository,
	userClient client2.UserServiceClient,
) *externalService.LikeService {
	svc := externalService.NewLikeService(likeRepo, userClient)
	log.Info().Msg("Like service initialized")
	return svc
}

func provideInternalLikeService(
	likeRepo repository2.LikeRepository,
) *intraService.InternalLikeService {
	svc := intraService.NewInternalLikeService(likeRepo)
	log.Info().Msg("Internal like service initialized")
	return svc
}

// 提供PlayHistory相关Service
func providePlayHistoryService(
	playHistoryRepo repository2.PlayHistoryRepository,
	userClient client2.UserServiceClient,
	videoClient client2.VideoServiceClient,
	musicClient client2.MusicServiceClient,
	novelClient client2.NovelServiceClient,
) *externalService.PlayHistoryService {
	svc := externalService.NewPlayHistoryService(playHistoryRepo, userClient, videoClient, musicClient, novelClient)
	log.Info().Msg("Play history service initialized")
	return svc
}

func provideInternalPlayHistoryService(
	playHistoryRepo repository2.PlayHistoryRepository,
) *intraService.InternalPlayHistoryService {
	svc := intraService.NewInternalPlayHistoryService(playHistoryRepo)
	log.Info().Msg("Internal play history service initialized")
	return svc
}

// 提供Favorite相关Handler
func provideFavoriteFolderHandler(folderService *externalService.FavoriteFolderService) *externalHandler.FavoriteFolderHandler {
	handler := externalHandler.NewFavoriteFolderHandler(folderService)
	log.Info().Msg("Favorite folder handler initialized")
	return handler
}

func provideFavoriteItemHandler(itemService *externalService.FavoriteItemService) *externalHandler.FavoriteItemHandler {
	handler := externalHandler.NewFavoriteItemHandler(itemService)
	log.Info().Msg("Favorite item handler initialized")
	return handler
}

func provideInternalFavoriteHandler(internalService *intraService.InternalFavoriteService) *intraHandler.InternalFavoriteHandler {
	handler := intraHandler.NewInternalFavoriteHandler(internalService)
	log.Info().Msg("Internal favorite handler initialized")
	return handler
}

// 提供Like相关Handler
func provideLikeHandler(likeService *externalService.LikeService) *externalHandler.LikeHandler {
	handler := externalHandler.NewLikeHandler(likeService)
	log.Info().Msg("Like handler initialized")
	return handler
}

func provideInternalLikeHandler(internalLikeService *intraService.InternalLikeService) *intraHandler.InternalLikeHandler {
	handler := intraHandler.NewInternalLikeHandler(internalLikeService)
	log.Info().Msg("Internal like handler initialized")
	return handler
}

// 提供PlayHistory相关Handler
func providePlayHistoryHandler(playHistoryService *externalService.PlayHistoryService) *externalHandler.PlayHistoryHandler {
	handler := externalHandler.NewPlayHistoryHandler(playHistoryService)
	log.Info().Msg("Play history handler initialized")
	return handler
}

func provideInternalPlayHistoryHandler(internalPlayHistoryService *intraService.InternalPlayHistoryService) *intraHandler.InternalPlayHistoryHandler {
	handler := intraHandler.NewInternalPlayHistoryHandler(internalPlayHistoryService)
	log.Info().Msg("Internal play history handler initialized")
	return handler
}

// 提供Album服务层
func provideAlbumService(
	albumRepository *repository2.AlbumRepository,
	albumContentRepository *repository2.AlbumContentRepository,
	db *gorm.DB,
) *externalService.AlbumService {
	albumService := externalService.NewAlbumService(albumRepository, albumContentRepository, db)
	log.Info().Msg("Album service initialized successfully")
	return albumService
}

// 提供Album处理器层
func provideAlbumHandler(
	albumService *externalService.AlbumService,
) *externalHandler.AlbumHandler {
	albumHandler := externalHandler.NewAlbumHandler(albumService)
	log.Info().Msg("Album handler initialized successfully")
	return albumHandler
}

// 提供Consul管理器
func provideConsulManager(cfg *types.Config) (*consul.Manager, error) {
	consulManager, err := consul.NewManager(&cfg.Consul)
	if err != nil {
		log.Fatal().Err(err).Msg("Consul管理器初始化失败")
		return nil, err
	}
	return consulManager, nil
}

// 提供健康检查处理器
func provideHealthHandler(consulManager *consul.Manager, db *gorm.DB) *consul.HealthHandler {
	healthHandler := consul.NewHealthHandler(consulManager, "1.0.0")

	// 添加数据库健康检查
	healthHandler.AddChecker(health.NewDatabaseHealthChecker("database", func() error {
		sqlDB, err := db.DB()
		if err != nil {
			return err
		}
		return sqlDB.Ping()
	}))

	return healthHandler
}

// 提供Gin引擎
func provideGinEngine(
	cfg *types.Config,
	jwtManager *auth.Manager,
	albumHandler *externalHandler.AlbumHandler,
	favoriteFolderHandler *externalHandler.FavoriteFolderHandler,
	favoriteItemHandler *externalHandler.FavoriteItemHandler,
	internalFavoriteHandler *intraHandler.InternalFavoriteHandler,
	externalLikeHandler *externalHandler.LikeHandler,
	internalLikeHandler *intraHandler.InternalLikeHandler,
	playHistoryHandler *externalHandler.PlayHistoryHandler,
	internalPlayHistoryHandler *intraHandler.InternalPlayHistoryHandler,
	healthHandler *consul.HealthHandler,
	otlpProvider *opentelemetry.Provider,
) *gin.Engine {
	// 设置Gin模式
	gin.SetMode(cfg.Server.Mode)
	log.Info().Str("mode", cfg.Server.Mode).Msg("Gin mode set")

	// 创建服务
	router := gin.Default()

	// 添加CORS中间件
	router.Use(cors.CORSMiddleware(cfg.Security.Cors))

	// 添加链路追踪中间件
	if otlpProvider != nil && otlpProvider.IsTracingEnabled() {
		router.Use(tracing.Middleware(otlpProvider.TracingProvider(), "interaction-service"))
		log.Info().Msg("Tracing middleware enabled")
	}

	// 添加指标中间件
	if otlpProvider != nil && otlpProvider.IsMetricsEnabled() {
		router.Use(metrics.Middleware(otlpProvider.MetricsProvider(), "interaction-service"))
		log.Info().Msg("Metrics middleware enabled")
	}

	log.Info().Msg("Middleware configured")

	routes.RegisterRoutes(
		router,
		jwtManager,
		albumHandler,
		favoriteFolderHandler,
		favoriteItemHandler,
		internalFavoriteHandler,
		externalLikeHandler,
		internalLikeHandler,
		playHistoryHandler,
		internalPlayHistoryHandler,
	)
	log.Info().Msg("Routes registered")

	// 注册健康检查路由
	healthHandler.RegisterHealthRoutes(router)

	return router
}

// 应用生命周期管理
func manageLifecycle(
	lc fx.Lifecycle,
	cfg *types.Config,
	consulManager *consul.Manager,
	ginEngine *gin.Engine,
	otlpProvider *opentelemetry.Provider,
) {
	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) error {
			// OpenTelemetry提供者在创建时已经启动，无需额外启动
			if otlpProvider != nil {
				log.Info().Msg("OpenTelemetry提供者已启动")
			}

			// 启动Consul管理器
			if err := consulManager.Start(ctx); err != nil {
				log.Fatal().Err(err).Msg("Consul管理器启动失败")
				return err
			}
			log.Info().Msg("Consul管理器启动成功")

			// 启动HTTP服务器（在goroutine中）
			go func() {
				log.Info().Int("port", cfg.Server.Port).Msg("Starting album service server")
				cmd.GraceStartAndClose(cfg.Server, ginEngine)
			}()

			return nil
		},
		OnStop: func(ctx context.Context) error {
			// 停止Consul管理器
			if err := consulManager.Stop(); err != nil {
				log.Error().Err(err).Msg("停止Consul管理器失败")
			} else {
				log.Info().Msg("Consul管理器停止成功")
			}

			// 关闭OpenTelemetry提供者
			if otlpProvider != nil {
				if err := otlpProvider.Shutdown(ctx); err != nil {
					log.Error().Err(err).Msg("关闭OpenTelemetry提供者失败")
				} else {
					log.Info().Msg("OpenTelemetry提供者关闭成功")
				}
			}

			return nil
		},
	})
}

func main() {
	app := fx.New(
		// 提供依赖
		fx.Provide(
			provideConfig,
			provideDatabase,
			provideJWTManager,
			provideOTLPProvider,
			// Album相关
			provideAlbumRepository,
			provideAlbumContentRepository,
			// 客户端
			provideUserServiceClient,
			provideVideoServiceClient,
			provideNovelServiceClient,
			provideMusicServiceClient,
			// Favorite Repository
			provideFavoriteFolderRepository,
			provideFavoriteItemRepository,
			provideFavoriteStatsRepository,
			// Like Repository
			provideLikeStatsRepository,
			provideLikeRepository,
			// PlayHistory Repository
			providePlayHistoryRepository,
			// Favorite Service
			provideFavoriteFolderService,
			provideFavoriteItemService,
			provideInternalFavoriteService,
			// Like Service
			provideLikeService,
			provideInternalLikeService,
			// PlayHistory Service
			providePlayHistoryService,
			provideInternalPlayHistoryService,
			// Favorite Handler
			provideFavoriteFolderHandler,
			provideFavoriteItemHandler,
			provideInternalFavoriteHandler,
			// Like Handler
			provideLikeHandler,
			provideInternalLikeHandler,
			// PlayHistory Handler
			providePlayHistoryHandler,
			provideInternalPlayHistoryHandler,
			// Album相关
			provideAlbumService,
			provideAlbumHandler,
			// 基础设施
			provideConsulManager,
			provideHealthHandler,
			provideGinEngine,
		),
		// 调用生命周期管理
		fx.Invoke(
			provideLogger,
			manageLifecycle,
		),
	)

	// 运行应用
	app.Run()
}
