package main

import (
	"context"
	"go.uber.org/fx"
	"pxpat-backend/cmd"
	handler2 "pxpat-backend/internal/storage-cluster/content-storage-service/external/handler"
	service2 "pxpat-backend/internal/storage-cluster/content-storage-service/external/service"
	internalHandler "pxpat-backend/internal/storage-cluster/content-storage-service/intra/handler"
	internalService "pxpat-backend/internal/storage-cluster/content-storage-service/intra/service"
	"pxpat-backend/internal/storage-cluster/content-storage-service/migrations"
	"pxpat-backend/internal/storage-cluster/content-storage-service/repository"
	"pxpat-backend/internal/storage-cluster/content-storage-service/routes"
	"pxpat-backend/internal/storage-cluster/content-storage-service/types"
	configLoader "pxpat-backend/pkg/config"
	"pxpat-backend/pkg/consul"
	"pxpat-backend/pkg/consul/health"
	DBLoader "pxpat-backend/pkg/database"
	"pxpat-backend/pkg/logger"
	"pxpat-backend/pkg/middleware/cors"
	metricsMiddleware "pxpat-backend/pkg/middleware/metrics"
	tracingMiddleware "pxpat-backend/pkg/middleware/tracing"
	"pxpat-backend/pkg/opentelemetry"
	"pxpat-backend/pkg/storage"

	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog/log"
	"gorm.io/gorm"
)

// 提供配置
func provideConfig() *types.Config {
	clusterName := "storage"
	serviceName := "content-storage"

	return configLoader.Load[types.Config](configLoader.LoaderConfig{
		TypeName:    configLoader.Service,
		ClusterName: clusterName,
		ServiceName: serviceName,
		UseRedis:    false,
	})
}

// 提供日志器
func provideLogger(cfg *types.Config) error {
	serviceName := "content-storage"

	// 初始化日志系统
	if err := logger.InitLogger(cfg.Log, serviceName); err != nil {
		log.Fatal().Msgf("日志系统初始化失败: %v", err)
		return err
	}

	return nil
}

// 提供OpenTelemetry Provider
func provideOtelProvider(cfg *types.Config) (*opentelemetry.Provider, error) {
	otelProvider, err := opentelemetry.NewProvider(cfg.Otlp)
	if err != nil {
		log.Fatal().Err(err).Msg("OpenTelemetry 初始化失败")
		return nil, err
	}

	if otelProvider.IsTracingEnabled() {
		log.Info().Str("service_name", cfg.Otlp.Tracing.ServiceName).
			Str("exporter_type", cfg.Otlp.Tracing.ExporterType).
			Msg("链路追踪已启用")
	} else {
		log.Info().Msg("链路追踪已禁用")
	}

	if otelProvider.IsMetricsEnabled() {
		log.Info().Str("service_name", cfg.Otlp.Metrics.ServiceName).
			Str("exporter_type", cfg.Otlp.Metrics.ExporterType).
			Msg("指标收集已启用")
	} else {
		log.Info().Msg("指标收集已禁用")
	}

	return otelProvider, nil
}

// 提供数据库连接
func provideDatabase(cfg *types.Config) (*gorm.DB, error) {
	log.Info().Msg("Attempting to connect to database...")
	db, err := DBLoader.ConnectDB(cfg.Database)
	if err != nil {
		log.Fatal().Msgf("数据库连接失败: %v", err)
		return nil, err
	}
	log.Info().Msg("Database connected successfully")

	// 数据库迁移
	if err := migrations.AutoMigrate(db); err != nil {
		log.Fatal().Msgf("数据库迁移失败: %v", err)
		return nil, err
	}
	log.Info().Msg("Database migration completed")

	return db, nil
}

// ContentStorageClient 内容存储客户端类型
type ContentStorageClient struct {
	storage.StorageClient
}

// CoverStorageClient 封面存储客户端类型
type CoverStorageClient struct {
	storage.StorageClient
}

// 提供内容存储客户端
func provideContentStorageClient(cfg *types.Config) (ContentStorageClient, error) {
	contentStorageClient, err := storage.NewStorageClient(cfg.ContentStorage)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to initialize content storage provider")
		return ContentStorageClient{}, err
	}
	log.Info().Str("provider", cfg.ContentStorage.Provider).Str("bucket", cfg.ContentStorage.Bucket).Msg("Content storage provider initialized successfully")
	return ContentStorageClient{contentStorageClient}, nil
}

// 提供封面存储客户端
func provideCoverStorageClient(cfg *types.Config) (CoverStorageClient, error) {
	coverStorageClient, err := storage.NewStorageClient(cfg.CoverStorage)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to initialize cover storage provider")
		return CoverStorageClient{}, err
	}
	log.Info().Str("provider", cfg.CoverStorage.Provider).Str("bucket", cfg.CoverStorage.Bucket).Msg("CoverURL storage provider initialized successfully")
	return CoverStorageClient{coverStorageClient}, nil
}

// 提供Repository
func provideVideoRepository(db *gorm.DB) repository.VideoRepository {
	return repository.NewVideoRepository(db)
}

func provideCoverRepository(db *gorm.DB) repository.CoverRepository {
	return repository.NewCoverRepository(db)
}

// 提供Service
func provideVideoService(cfg *types.Config, videoRepo repository.VideoRepository, contentStorageClient ContentStorageClient) *service2.VideoService {
	return service2.NewVideoService(cfg, videoRepo, contentStorageClient.StorageClient)
}

func provideCoverService(cfg *types.Config, coverRepo repository.CoverRepository, coverStorageClient CoverStorageClient) *service2.CoverService {
	return service2.NewCoverService(cfg, coverRepo, coverStorageClient.StorageClient)
}

func provideInternalCoverService(coverRepo repository.CoverRepository, coverStorageClient CoverStorageClient) *internalService.InternalCoverService {
	return internalService.NewInternalCoverService(coverRepo, coverStorageClient.StorageClient)
}

func provideInternalVideoService(videoRepo repository.VideoRepository, contentStorageClient ContentStorageClient) *internalService.InternalVideoService {
	return internalService.NewInternalVideoService(videoRepo, contentStorageClient.StorageClient)
}

// 提供Handler
func provideVideoHandler(cfg *types.Config, videoService *service2.VideoService) *handler2.VideoHandler {
	return handler2.NewVideoHandler(cfg, videoService)
}

func provideCoverHandler(cfg *types.Config, coverService *service2.CoverService) *handler2.CoverHandler {
	return handler2.NewCoverHandler(cfg, coverService)
}

func provideInternalCoverHandler(internalCoverService *internalService.InternalCoverService) *internalHandler.InternalCoverHandler {
	return internalHandler.NewInternalCoverHandler(internalCoverService)
}

func provideInternalVideoHandler(internalVideoService *internalService.InternalVideoService) *internalHandler.InternalVideoHandler {
	return internalHandler.NewInternalVideoHandler(internalVideoService)
}

// 提供Consul管理器
func provideConsulManager(cfg *types.Config) (*consul.Manager, error) {
	consulManager, err := consul.NewManager(&cfg.Consul)
	if err != nil {
		log.Fatal().Err(err).Msg("Consul管理器初始化失败")
		return nil, err
	}
	return consulManager, nil
}

// 提供健康检查处理器
func provideHealthHandler(consulManager *consul.Manager, db *gorm.DB) *consul.HealthHandler {
	healthHandler := consul.NewHealthHandler(consulManager, "1.0.0")

	// 添加数据库健康检查
	healthHandler.AddChecker(health.NewDatabaseHealthChecker("database", func() error {
		sqlDB, err := db.DB()
		if err != nil {
			return err
		}
		return sqlDB.Ping()
	}))

	return healthHandler
}

// 提供Gin引擎
func provideGinEngine(
	cfg *types.Config,
	otelProvider *opentelemetry.Provider,
	healthHandler *consul.HealthHandler,
	videoHandler *handler2.VideoHandler,
	coverHandler *handler2.CoverHandler,
	internalCoverHandler *internalHandler.InternalCoverHandler,
	internalVideoHandler *internalHandler.InternalVideoHandler,
) *gin.Engine {
	// 设置Gin模式
	gin.SetMode(cfg.Server.Mode)
	log.Info().Str("mode", cfg.Server.Mode).Msg("Gin mode set")

	r := gin.Default()

	// 添加中间件
	r.Use(cors.CORSMiddleware(cfg.Security.Cors))

	// 添加指标收集中间件
	if otelProvider.IsMetricsEnabled() {
		r.Use(metricsMiddleware.Middleware(otelProvider.MetricsProvider(), cfg.Otlp.Metrics.ServiceName))
	}

	// 添加链路追踪中间件
	if otelProvider.IsTracingEnabled() {
		r.Use(tracingMiddleware.Middleware(otelProvider.TracingProvider(), cfg.Otlp.Tracing.ServiceName))
	}

	// 注册健康检查路由
	healthHandler.RegisterHealthRoutes(r)

	// 注册业务路由
	routes.RegisterRoutes(cfg, r, videoHandler, coverHandler, internalCoverHandler, internalVideoHandler)
	log.Info().Msg("Routes registered")

	return r
}

// 应用生命周期管理
func manageLifecycle(
	lc fx.Lifecycle,
	cfg *types.Config,
	otelProvider *opentelemetry.Provider,
	consulManager *consul.Manager,
	ginEngine *gin.Engine,
) {
	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) error {
			// 启动Consul管理器
			if err := consulManager.Start(context.Background()); err != nil {
				log.Fatal().Err(err).Msg("Consul管理器启动失败")
				return err
			}
			log.Info().Msg("Consul管理器启动成功")

			// 启动HTTP服务器（在goroutine中）
			go func() {
				log.Info().Msg("内容存储服务启动中...")
				cmd.GraceStartAndClose(cfg.Server, ginEngine)
			}()

			return nil
		},
		OnStop: func(ctx context.Context) error {
			// 停止Consul管理器
			if err := consulManager.Stop(); err != nil {
				log.Error().Err(err).Msg("停止Consul管理器失败")
			} else {
				log.Info().Msg("Consul管理器停止成功")
			}

			// 关闭OpenTelemetry
			if err := otelProvider.Shutdown(ctx); err != nil {
				log.Error().Err(err).Msg("关闭 OpenTelemetry 失败")
			} else {
				log.Info().Msg("OpenTelemetry 关闭成功")
			}

			return nil
		},
	})
}

func main() {
	app := fx.New(
		// 提供依赖
		fx.Provide(
			provideConfig,
			provideOtelProvider,
			provideDatabase,
			provideContentStorageClient,
			provideCoverStorageClient,
			provideVideoRepository,
			provideCoverRepository,
			provideVideoService,
			provideCoverService,
			provideInternalCoverService,
			provideInternalVideoService,
			provideVideoHandler,
			provideCoverHandler,
			provideInternalCoverHandler,
			provideInternalVideoHandler,
			provideConsulManager,
			provideHealthHandler,
			provideGinEngine,
		),
		// 调用生命周期管理
		fx.Invoke(
			provideLogger,
			manageLifecycle,
		),
	)

	// 运行应用
	app.Run()
}
