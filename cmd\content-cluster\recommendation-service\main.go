package main

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"

	"pxpat-backend/internal/content-cluster/recommendation-service/cache"
	"pxpat-backend/internal/content-cluster/recommendation-service/config"
	"pxpat-backend/internal/content-cluster/recommendation-service/handler"
	"pxpat-backend/internal/content-cluster/recommendation-service/migrations"
	"pxpat-backend/internal/content-cluster/recommendation-service/repository/impl"
	serviceImpl "pxpat-backend/internal/content-cluster/recommendation-service/service/impl"
	pkgCache "pxpat-backend/pkg/cache"
	"pxpat-backend/pkg/consul"
	"pxpat-backend/pkg/consul/health"
	"pxpat-backend/pkg/middleware/cors"
)

func main() {
	// 加载配置
	env := os.Getenv("PXPAT_ENV")
	var envDir string
	switch env {
	case "dev", "development":
		envDir = "dev"
	case "prod", "production":
		envDir = "prod"
	default:
		envDir = "prod"
	}
	configPath := fmt.Sprintf("configs/%s/content-cluster/recommendation-service/config.yaml", envDir)
	cfg, err := config.LoadConfig(configPath)
	if err != nil {
		log.Fatalf("加载配置失败: %v", err)
	}

	// 初始化数据库连接
	db, err := initDatabase(cfg)
	if err != nil {
		log.Fatalf("初始化数据库失败: %v", err)
	}

	// 运行数据库迁移
	if err := migrations.RunMigrations(db); err != nil {
		log.Fatalf("数据库迁移失败: %v", err)
	}

	// 初始化Redis缓存
	redisClient, err := pkgCache.NewRedisClient(&pkgCache.RedisConfig{
		Host:         cfg.Redis.Host,
		Port:         cfg.Redis.Port,
		Password:     cfg.Redis.Password,
		DB:           cfg.Redis.DB,
		PoolSize:     cfg.Redis.PoolSize,
		MinIdleConns: cfg.Redis.MinIdleConns,
		DialTimeout:  cfg.Redis.DialTimeout,
		ReadTimeout:  cfg.Redis.ReadTimeout,
		WriteTimeout: cfg.Redis.WriteTimeout,
		PoolTimeout:  cfg.Redis.PoolTimeout,
		IdleTimeout:  cfg.Redis.IdleTimeout,
	})
	if err != nil {
		log.Fatalf("初始化Redis失败: %v", err)
	}

	// 初始化缓存管理器
	cacheManager := cache.NewCacheManager(redisClient, &cfg.Cache)

	// 初始化仓储层
	userBehaviorRepo := impl.NewUserBehaviorRepository(db, redisClient)
	userProfileRepo := impl.NewUserProfileRepository(db, redisClient)
	contentFeatureRepo := impl.NewContentFeatureRepository(db, redisClient)
	recommendationRepo := impl.NewRecommendationRepository(db, redisClient)

	// 初始化服务层
	algorithmService := serviceImpl.NewAlgorithmService(cfg, userBehaviorRepo, userProfileRepo, contentFeatureRepo)
	userProfileService := serviceImpl.NewUserProfileService(userProfileRepo, userBehaviorRepo, cfg)
	contentFeatureService := serviceImpl.NewContentFeatureService(contentFeatureRepo, cfg)
	recommendationService := serviceImpl.NewRecommendationService(
		cfg,
		algorithmService,
		cacheManager,
		recommendationRepo,
		userBehaviorRepo,
		userProfileRepo,
		contentFeatureRepo,
	)

	// 初始化处理器
	recommendationHandler := handler.NewRecommendationHandler(recommendationService)
	userProfileHandler := handler.NewUserProfileHandler(userProfileService)
	contentFeatureHandler := handler.NewContentFeatureHandler(contentFeatureService)

	// 设置Gin模式
	gin.SetMode(cfg.Server.Mode)

	// 创建路由
	router := gin.New()
	router.Use(gin.Logger())
	router.Use(gin.Recovery())

	// 使用CORS中间件
	corsConfig := map[string]string{
		"Origin":      "*",
		"Methods":     "GET,POST,PUT,DELETE,OPTIONS",
		"Headers":     "*",
		"Credentials": "true",
	}
	router.Use(cors.Middleware(corsConfig["Origin"], corsConfig["Methods"], corsConfig["Headers"], corsConfig["Credentials"]))

	// 初始化Consul管理器
	consulManager, err := consul.NewManager(&cfg.Consul)
	if err != nil {
		log.Fatalf("Consul管理器初始化失败: %v", err)
	}

	// 启动Consul管理器
	ctx := context.Background()
	if err := consulManager.Start(ctx); err != nil {
		log.Fatalf("Consul管理器启动失败: %v", err)
	}

	// 初始化健康检查处理器
	healthHandler := consul.NewHealthHandler(consulManager, "1.0.0")

	// 添加数据库健康检查
	healthHandler.AddChecker(health.NewDatabaseHealthChecker("database", func() error {
		sqlDB, err := db.DB()
		if err != nil {
			return err
		}
		return sqlDB.Ping()
	}))

	// 注册健康检查路由
	healthHandler.RegisterHealthRoutes(router)

	// 健康检查
	router.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"status":    "healthy",
			"intra":     "recommendation-intra",
			"timestamp": time.Now().Unix(),
		})
	})

	// API路由组
	v1 := router.Group("/api/v1")
	{
		// 推荐相关路由
		recommendations := v1.Group("/recommendations")
		{
			recommendations.GET("/user/:userKsUID", recommendationHandler.GetUserRecommendations)
			recommendations.GET("/trending", recommendationHandler.GetTrendingContent)
			recommendations.GET("/similar/:contentId", recommendationHandler.GetSimilarContent)
			recommendations.POST("/feedback", recommendationHandler.RecordFeedback)
			recommendations.GET("/stats", recommendationHandler.GetRecommendationStats)
		}

		// 用户画像相关路由
		profiles := v1.Group("/profiles")
		{
			profiles.GET("/user/:userKsUID", userProfileHandler.GetUserProfile)
			profiles.PUT("/user/:userKsUID", userProfileHandler.UpdateUserProfile)
			profiles.POST("/behavior", userProfileHandler.RecordUserBehavior)
			profiles.GET("/interests/:userKsUID", userProfileHandler.GetUserInterests)
		}

		// 内容特征相关路由
		features := v1.Group("/features")
		{
			features.GET("/content/:contentId", contentFeatureHandler.GetContentFeatures)
			features.PUT("/content/:contentId", contentFeatureHandler.UpdateContentFeatures)
			features.POST("/extract", contentFeatureHandler.ExtractContentFeatures)
			features.GET("/similar/:contentId", contentFeatureHandler.GetSimilarContents)
		}
	}

	// 创建HTTP服务器
	server := &http.Server{
		Addr:           fmt.Sprintf("%s:%d", cfg.Server.Host, cfg.Server.Port),
		Handler:        router,
		ReadTimeout:    cfg.Server.ReadTimeout,
		WriteTimeout:   cfg.Server.WriteTimeout,
		MaxHeaderBytes: cfg.Server.MaxHeaderBytes,
	}

	// 启动服务器
	go func() {
		log.Printf("推荐服务启动在 %s:%d", cfg.Server.Host, cfg.Server.Port)
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("服务器启动失败: %v", err)
		}
	}()

	// 等待中断信号以优雅地关闭服务器
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit
	log.Println("正在关闭推荐服务...")

	// 设置5秒的超时时间来关闭服务器
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := server.Shutdown(ctx); err != nil {
		log.Fatal("推荐服务强制关闭:", err)
	}

	// 停止Consul管理器
	if err := consulManager.Stop(); err != nil {
		log.Printf("停止Consul管理器失败: %v", err)
	}

	log.Println("推荐服务已退出")
}

// initDatabase 初始化数据库连接
func initDatabase(cfg *config.Config) (*gorm.DB, error) {
	dsn := fmt.Sprintf("host=%s user=%s password=%s dbname=%s port=%d sslmode=%s TimeZone=%s",
		cfg.Database.Host,
		cfg.Database.User,
		cfg.Database.Password,
		cfg.Database.DBName,
		cfg.Database.Port,
		cfg.Database.SSLMode,
		cfg.Database.Timezone,
	)

	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{})
	if err != nil {
		return nil, fmt.Errorf("连接数据库失败: %w", err)
	}

	// 获取底层的sql.DB对象来设置连接池参数
	sqlDB, err := db.DB()
	if err != nil {
		return nil, fmt.Errorf("获取数据库实例失败: %w", err)
	}

	// 设置连接池参数
	sqlDB.SetMaxIdleConns(cfg.Database.MaxIdleConns)
	sqlDB.SetMaxOpenConns(cfg.Database.MaxOpenConns)
	sqlDB.SetConnMaxLifetime(cfg.Database.ConnMaxLifetime)

	return db, nil
}
