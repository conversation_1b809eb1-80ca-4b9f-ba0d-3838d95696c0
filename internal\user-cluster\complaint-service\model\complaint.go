package model

import (
	"time"
)

// ComplaintStatus 投诉状态
type ComplaintStatus string

const (
	ComplaintStatusPending   ComplaintStatus = "pending"   // 进行中
	ComplaintStatusApproved  ComplaintStatus = "approved"  // 已通过
	ComplaintStatusRejected  ComplaintStatus = "rejected"  // 已拒绝
	ComplaintStatusCompleted ComplaintStatus = "completed" // 已完成
	ComplaintStatusCancelled ComplaintStatus = "cancelled" // 已取消
)

// ComplaintType 投诉类型
type ComplaintType string

const (
	ComplaintTypePiracy  ComplaintType = "piracy"  // 盗版稿件投诉
	ComplaintTypeContent ComplaintType = "content" // 稿件投诉
	ComplaintTypeRights  ComplaintType = "rights"  // 侵权申诉
)

// UserRole 用户角色
type UserRole string

const (
	UserRoleOriginalCreator UserRole = "original_creator" // 原创者
	UserRolePasserby        UserRole = "passerby"         // 路人
)

// Complaint 投诉记录模型
type Complaint struct {
	ID          uint      `gorm:"primaryKey;autoIncrement" json:"id"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
	DeletedAt   *time.Time `gorm:"index" json:"-"`

	// 基础字段
	ComplaintKSUID string          `gorm:"size:27;uniqueIndex;not null" json:"complaint_ksuid"` // 投诉唯一标识
	UserKSUID      string          `gorm:"size:27;not null;index" json:"user_ksuid"`            // 投诉人用户KSUID
	AccusedKSUID   string          `gorm:"size:27;not null;index" json:"accused_ksuid"`         // 被投诉人用户KSUID
	ContentKSUID   string          `gorm:"size:27;not null;index" json:"content_ksuid"`         // 被投诉内容的唯一KSUID
	ContentType    string          `gorm:"size:50;not null" json:"content_type"`                // 被投诉内容的类型
	Type           ComplaintType   `gorm:"size:20;not null" json:"type"`                        // 投诉类型
	Status         ComplaintStatus `gorm:"size:20;default:'pending'" json:"status"`             // 投诉状态

	// 投诉详情
	Title       string `gorm:"size:255;not null" json:"title"`       // 投诉标题
	Description string `gorm:"type:text" json:"description"`         // 详细描述
	UserRole    UserRole `gorm:"size:20" json:"user_role"`           // 用户角色(仅盗版投诉使用)

	// 盗版投诉特有字段
	OriginalURL string `gorm:"size:1024" json:"original_url"` // 原视频出处
	WorkURL     string `gorm:"size:1024" json:"work_url"`     // 本站作品地址

	// 稿件投诉特有字段
	ViolationType    string `gorm:"size:100" json:"violation_type"`     // 违规类型
	ViolationSubType string `gorm:"size:100" json:"violation_sub_type"` // 违规子类型

	// 证据文件
	EvidenceFiles []ComplaintEvidence `gorm:"foreignKey:ComplaintKSUID;references:ComplaintKSUID" json:"evidence_files,omitempty"`

	// 处理信息
	ProcessorKSUID string     `gorm:"size:27" json:"processor_ksuid"`     // 处理人KSUID
	ProcessedAt    *time.Time `json:"processed_at"`                       // 处理时间
	ProcessNote    string     `gorm:"type:text" json:"process_note"`      // 处理备注
	Resolution     string     `gorm:"type:text" json:"resolution"`        // 处理结果
}

// TableName 指定表名
func (Complaint) TableName() string {
	return "complaint_complaints"
}

// ComplaintEvidence 投诉证据文件模型
type ComplaintEvidence struct {
	ID          uint      `gorm:"primaryKey;autoIncrement" json:"id"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
	DeletedAt   *time.Time `gorm:"index" json:"-"`

	ComplaintKSUID string `gorm:"size:27;not null;index" json:"complaint_ksuid"` // 关联的投诉KSUID
	FileName       string `gorm:"size:255;not null" json:"file_name"`            // 文件名
	FileURL        string `gorm:"size:1024;not null" json:"file_url"`            // 文件URL
	FileSize       int64  `json:"file_size"`                                     // 文件大小
	ContentType    string `gorm:"size:100" json:"content_type"`                  // 文件类型
	BucketName     string `gorm:"size:100" json:"bucket_name"`                   // 存储桶名称
	ObjectName     string `gorm:"size:512" json:"object_name"`                   // 对象名称
}

// TableName 指定表名
func (ComplaintEvidence) TableName() string {
	return "complaint_evidences"
}

// ViolationCategory 违规类别模型
type ViolationCategory struct {
	ID          uint      `gorm:"primaryKey;autoIncrement" json:"id"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
	DeletedAt   *time.Time `gorm:"index" json:"-"`

	Code        string `gorm:"size:50;uniqueIndex;not null" json:"code"`        // 类别代码
	Name        string `gorm:"size:100;not null" json:"name"`                   // 类别名称
	Description string `gorm:"type:text" json:"description"`                    // 类别描述
	ParentCode  string `gorm:"size:50;index" json:"parent_code"`                // 父类别代码
	SortOrder   int    `gorm:"default:0" json:"sort_order"`                     // 排序
	IsActive    bool   `gorm:"default:true" json:"is_active"`                   // 是否启用

	// 关联关系
	Children []*ViolationCategory `gorm:"foreignKey:ParentCode;references:Code" json:"children,omitempty"`
}

// TableName 指定表名
func (ViolationCategory) TableName() string {
	return "complaint_violation_categories"
}
