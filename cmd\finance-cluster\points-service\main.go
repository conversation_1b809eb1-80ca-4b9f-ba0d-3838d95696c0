package main

import (
	"context"
	"os"
	"os/signal"
	"syscall"
	"time"

	"pxpat-backend/cmd"
	"pxpat-backend/internal/finance-cluster/points-service/messaging"
	packagemodel "pxpat-backend/internal/finance-cluster/points-service/model/package"
	patmodel "pxpat-backend/internal/finance-cluster/points-service/model/pat"
	rechargemodel "pxpat-backend/internal/finance-cluster/points-service/model/recharge"
	transactionmodel "pxpat-backend/internal/finance-cluster/points-service/model/transaction"
	usermodel "pxpat-backend/internal/finance-cluster/points-service/model/user"
	"pxpat-backend/internal/finance-cluster/points-service/routes"
	"pxpat-backend/internal/finance-cluster/points-service/service"
	"pxpat-backend/internal/finance-cluster/points-service/types"
	configLoader "pxpat-backend/pkg/config"
	"pxpat-backend/pkg/consul"
	"pxpat-backend/pkg/consul/health"
	databaseLoader "pxpat-backend/pkg/database"
	"pxpat-backend/pkg/logger"
	"pxpat-backend/pkg/middleware/cors"

	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog/log"
)

func main() {
	clusterName := "finance"
	serviceName := "points"

	// 加载配置
	cfg := configLoader.Load[types.Config](configLoader.LoaderConfig{
		TypeName:    configLoader.Service,
		ClusterName: clusterName,
		ServiceName: serviceName,
		UseRedis:    false,
	})

	// 初始化日志系统
	if err := logger.InitLogger(cfg.Log, serviceName); err != nil {
		log.Fatal().Err(err).Msg("Failed to initialize logger")
	}

	// 获取服务专用日志器
	log.Info().Msg("Points service starting...")

	// 初始化数据库
	db, err := databaseLoader.ConnectDB(cfg.Database)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to initialize database")
	}
	log.Info().Msg("Database connected successfully")

	// 自动迁移数据库表
	if err := db.AutoMigrate(
		&usermodel.PointAccount{},
		&rechargemodel.RechargeRecord{},
		&packagemodel.ServicePackage{},
		&packagemodel.UserServicePackage{},
		&patmodel.PATPointsRecord{},
		&transactionmodel.PointsTransaction{},
		&usermodel.DailyLimit{},
	); err != nil {
		log.Fatal().Err(err).Msg("Database migration failed")
	}
	log.Info().Msg("Database migration completed")

	// 初始化服务包数据
	if err := packagemodel.InitializeServicePackages(db); err != nil {
		log.Fatal().Err(err).Msg("Failed to initialize service packages")
	}
	log.Info().Msg("Service packages initialized")

	// 初始化服务
	pointsService := service.NewPointsService(db, cfg)

	// 初始化消息多消费者
	multiConsumer, err := messaging.CreatePointsServiceMultiConsumer(cfg.RabbitMQ.URL, pointsService)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to initialize message multi consumer")
	}
	log.Info().Msg("Message multi consumer initialized successfully")
	defer multiConsumer.Close()

	// 初始化JWT管理器
	//jwtManager := auth.NewJWTManager(cfg.JWT.SecretKey, cfg.JWT.Expiration, cfg.JWT.Issuer)
	log.Info().Msg("JWT manager initialized")

	// 设置Gin模式
	gin.SetMode(cfg.Server.Mode)
	log.Info().Str("mode", cfg.Server.Mode).Msg("Gin mode set")

	// 创建路由
	router := gin.Default()

	// 添加中间件
	router.Use(cors.CORSMiddleware(cfg.Security.CORS))
	log.Info().Msg("Middleware configured")

	// 注册路由
	routes.RegisterRoutes(router, pointsService)
	log.Info().Msg("Routes registered")

	// 初始化Consul管理器
	consulManager, err := consul.NewManager(&cfg.Consul)
	if err != nil {
		log.Fatal().Err(err).Msg("Consul管理器初始化失败")
	}

	// 启动Consul管理器
	consulCtx := context.Background()
	if err := consulManager.Start(consulCtx); err != nil {
		log.Fatal().Err(err).Msg("Consul管理器启动失败")
	}

	// 初始化健康检查处理器
	healthHandler := consul.NewHealthHandler(consulManager, "1.0.0")

	// 添加数据库健康检查
	healthHandler.AddChecker(health.NewDatabaseHealthChecker("database", func() error {
		sqlDB, err := db.DB()
		if err != nil {
			return err
		}
		return sqlDB.Ping()
	}))

	// 注册健康检查路由
	healthHandler.RegisterHealthRoutes(router)

	// 创建上下文用于优雅关闭
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// 启动消息多消费者
	go func() {
		log.Info().Msg("Starting message multi consumer")
		if err := multiConsumer.StartConsuming(ctx); err != nil {
			log.Error().Err(err).Msg("Message multi consumer failed")
		}
	}()

	// 设置信号处理
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	// 启动HTTP服务器
	go func() {
		log.Info().Int("port", cfg.Server.Port).Msg("Starting points service server")
		cmd.GraceStartAndClose(cfg.Server, router)
	}()

	// 等待关闭信号
	<-sigChan
	log.Info().Msg("Received shutdown signal, starting graceful shutdown")

	// 取消上下文，停止消息消费者
	cancel()

	// 等待消息消费者关闭
	time.Sleep(2 * time.Second)

	// 关闭消息多消费者
	if err := multiConsumer.Close(); err != nil {
		log.Error().Err(err).Msg("Failed to close message multi consumer")
	} else {
		log.Info().Msg("Message multi consumer closed successfully")
	}

	// 停止Consul管理器
	if err := consulManager.Stop(); err != nil {
		log.Error().Err(err).Msg("停止Consul管理器失败")
	}

	log.Info().Msg("Points service shutdown completed")
}
