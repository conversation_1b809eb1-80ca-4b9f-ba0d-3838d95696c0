package admin

import (
	"github.com/gin-gonic/gin"

	"pxpat-backend/internal/user-cluster/complaint-service/external/handler"
	"pxpat-backend/internal/user-cluster/complaint-service/middleware"
	"pxpat-backend/pkg/auth"
	pkgauth "pxpat-backend/pkg/middleware/auth"
)

// RegisterAdminRoutes 注册管理员路由
func RegisterAdminRoutes(
	router *gin.RouterGroup,
	adminHandler *handler.AdminHandler,
	jwtManager *auth.Manager,
) {
	// 管理员路由组，需要管理员权限
	admin := router.Group("/admin")
	if jwtManager != nil {
		admin.Use(pkgauth.AdminAuthMiddleware(*jwtManager))
	}
	admin.Use(middleware.AdminRequired()) // 管理员权限中间件

	// 投诉管理
	complaints := admin.Group("/complaints")
	{
		complaints.GET("", adminHandler.GetComplaintsWithFilters)                        // 获取投诉列表
		complaints.POST("/:complaint_ksuid/process", adminHandler.ProcessComplaint)      // 处理投诉
	}

	// 身份认证管理
	identity := admin.Group("/identity")
	{
		identity.GET("", adminHandler.GetIdentityVerificationsWithFilters)                       // 获取身份认证列表
		identity.POST("/:verification_ksuid/review", adminHandler.ReviewIdentityVerification)    // 审核身份认证
	}

	// 权益认证管理
	rights := admin.Group("/rights")
	{
		rights.GET("", adminHandler.GetRightsVerificationsWithFilters)                   // 获取权益认证列表
		rights.POST("/:rights_ksuid/review", adminHandler.ReviewRightsVerification)      // 审核权益认证
	}
}


