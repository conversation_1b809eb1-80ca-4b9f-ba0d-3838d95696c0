package handler

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog"

	"pxpat-backend/internal/finance-cluster/token-service/service"
)

// ContentHandler 内容处理器
type ContentHandler struct {
	contentService *service.ContentService
	logger         zerolog.Logger
}

// NewContentHandler 创建内容处理器
func NewContentHandler(contentService *service.ContentService, logger zerolog.Logger) *ContentHandler {
	return &ContentHandler{
		contentService: contentService,
		logger:         logger.With().Str("component", "content_handler").Logger(),
	}
}

// PublishContent 发布内容到区块链
func (h *ContentHandler) PublishContent(c *gin.Context) {
	var req service.ContentPublishRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error().Err(err).Msg("Failed to bind request")
		c.<PERSON>(http.StatusBadRequest, NewErrorResponse(http.StatusBadRequest, "Invalid request format", err.Error()))
		return
	}

	// 验证必填字段
	if req.ID == "" || req.Title == "" || req.ContentType == "" || req.PublisherKSUID == "" || req.OnChainID == "" {
		c.JSON(http.StatusBadRequest, NewErrorResponse(http.StatusBadRequest, "Missing required fields", "id, title, content_type, publisher_ksuid, on_chain_id are required"))
		return
	}

	// 验证审核员数量
	if len(req.Reviewers) == 0 {
		c.JSON(http.StatusBadRequest, NewErrorResponse(http.StatusBadRequest, "Invalid reviewers", "At least one reviewer is required"))
		return
	}

	// 发布内容
	record, err := h.contentService.PublishContent(c.Request.Context(), &req)
	if err != nil {
		h.logger.Error().Err(err).Str("content_id", req.ID).Msg("Failed to publish content")
		c.JSON(http.StatusInternalServerError, NewErrorResponse(http.StatusInternalServerError, "Failed to publish content", err.Error()))
		return
	}

	h.logger.Info().
		Str("content_id", req.ID).
		Str("on_chain_id", req.OnChainID).
		Uint("record_id", record.ID).
		Msg("Content publish initiated")

	c.JSON(http.StatusOK, NewSuccessResponse(record))
}

// GetContentRecord 获取内容记录
func (h *ContentHandler) GetContentRecord(c *gin.Context) {
	onChainID := c.Param("on_chain_id")
	if onChainID == "" {
		c.JSON(http.StatusBadRequest, NewErrorResponse(http.StatusBadRequest, "Invalid parameter", "on_chain_id is required"))
		return
	}

	record, err := h.contentService.GetContentRecord(c.Request.Context(), onChainID)
	if err != nil {
		h.logger.Error().Err(err).Str("on_chain_id", onChainID).Msg("Failed to get content record")
		c.JSON(http.StatusNotFound, NewErrorResponse(http.StatusNotFound, "Content not found", err.Error()))
		return
	}

	c.JSON(http.StatusOK, NewSuccessResponse(record))
}

// GetContentDetailByOnChainID 根据上链ID获取完整内容详情
func (h *ContentHandler) GetContentDetailByOnChainID(c *gin.Context) {
	onChainID := c.Param("on_chain_id")
	if onChainID == "" {
		c.JSON(http.StatusBadRequest, NewErrorResponse(http.StatusBadRequest, "Invalid parameter", "on_chain_id is required"))
		return
	}

	detail, err := h.contentService.GetContentDetailByOnChainID(c.Request.Context(), onChainID)
	if err != nil {
		h.logger.Error().Err(err).Str("on_chain_id", onChainID).Msg("Failed to get content detail")
		c.JSON(http.StatusNotFound, NewErrorResponse(http.StatusNotFound, "Content detail not found", err.Error()))
		return
	}

	c.JSON(http.StatusOK, NewSuccessResponse(detail))
}

// GetContentRecords 分页获取内容记录
func (h *ContentHandler) GetContentRecords(c *gin.Context) {
	// 解析分页参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))
	contentType := c.Query("content_type")
	status := c.Query("status")

	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 20
	}

	records, total, err := h.contentService.GetContentRecords(c.Request.Context(), page, limit, contentType, status)
	if err != nil {
		h.logger.Error().Err(err).Msg("Failed to get content records")
		c.JSON(http.StatusInternalServerError, NewErrorResponse(http.StatusInternalServerError, "Failed to get content records", err.Error()))
		return
	}

	totalPages := (total + int64(limit) - 1) / int64(limit)

	response := map[string]interface{}{
		"records": records,
		"pagination": map[string]interface{}{
			"page":        page,
			"limit":       limit,
			"total":       total,
			"total_pages": totalPages,
		},
	}

	c.JSON(http.StatusOK, NewSuccessResponse(response))
}

// GetContentStats 获取内容统计
func (h *ContentHandler) GetContentStats(c *gin.Context) {
	stats, err := h.contentService.GetContentStats(c.Request.Context())
	if err != nil {
		h.logger.Error().Err(err).Msg("Failed to get content stats")
		c.JSON(http.StatusInternalServerError, NewErrorResponse(http.StatusInternalServerError, "Failed to get content stats", err.Error()))
		return
	}

	c.JSON(http.StatusOK, NewSuccessResponse(stats))
}

// GetContentTypeStats 获取内容类型统计
func (h *ContentHandler) GetContentTypeStats(c *gin.Context) {
	stats, err := h.contentService.GetContentTypeStats(c.Request.Context())
	if err != nil {
		h.logger.Error().Err(err).Msg("Failed to get content type stats")
		c.JSON(http.StatusInternalServerError, NewErrorResponse(http.StatusInternalServerError, "Failed to get content type stats", err.Error()))
		return
	}

	c.JSON(http.StatusOK, NewSuccessResponse(stats))
}

// GetContentFees 获取内容类型费用
func (h *ContentHandler) GetContentFees(c *gin.Context) {
	fees := h.contentService.GetSupportedContentTypes()
	c.JSON(http.StatusOK, NewSuccessResponse(fees))
}

// GetContentFee 获取特定内容类型费用
func (h *ContentHandler) GetContentFee(c *gin.Context) {
	contentType := c.Param("content_type")
	if contentType == "" {
		c.JSON(http.StatusBadRequest, NewErrorResponse(http.StatusBadRequest, "Invalid parameter", "content_type is required"))
		return
	}

	fee, err := h.contentService.GetContentFee(contentType)
	if err != nil {
		c.JSON(http.StatusNotFound, NewErrorResponse(http.StatusNotFound, "Content type not supported", err.Error()))
		return
	}

	response := map[string]interface{}{
		"content_type": contentType,
		"fee":          fee,
		"unit":         "PAT",
	}

	c.JSON(http.StatusOK, NewSuccessResponse(response))
}

// RealUpload 真实上传内容到区块链（审核完成后调用）
func (h *ContentHandler) RealUpload(c *gin.Context) {
	var req RealUploadRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error().Err(err).Msg("Failed to bind real upload request")
		c.JSON(http.StatusBadRequest, NewErrorResponse(http.StatusBadRequest, "Invalid request format", err.Error()))
		return
	}

	// 验证必填字段
	if req.ContentKSUID == "" || req.CreatorKSUID == "" || req.Title == "" || req.ContentType == "" {
		c.JSON(http.StatusBadRequest, NewErrorResponse(http.StatusBadRequest, "Missing required fields", "content_ksuid, creator_ksuid, title, content_type are required"))
		return
	}

	h.logger.Info().
		Str("content_ksuid", req.ContentKSUID).
		Str("creator_ksuid", req.CreatorKSUID).
		Str("title", req.Title).
		Str("content_type", req.ContentType).
		Msg("Processing real upload request")

	// 构建内容发布请求
	publishReq := &service.ContentPublishRequest{
		ID:             req.ContentKSUID,
		OnChainID:      req.ContentKSUID, // 使用ContentKSUID作为OnChainID
		Title:          req.Title,
		Description:    req.Description,
		ContentType:    req.ContentType,
		PublisherKSUID: req.CreatorKSUID,
		// 从metadata中提取其他信息
		Cast:      []string{}, // TODO: 从metadata中提取
		Director:  "",         // TODO: 从metadata中提取
		Publisher: "PXPAC",    // 默认发布商
		Reviewers: []string{req.CreatorKSUID}, // 使用创建者作为审核员
		FileKSUID: req.FileName,
		FileMD5:   "", // TODO: 计算文件MD5
	}

	// 发布内容到区块链
	record, err := h.contentService.PublishContent(c.Request.Context(), publishReq)
	if err != nil {
		h.logger.Error().Err(err).
			Str("content_ksuid", req.ContentKSUID).
			Msg("Failed to publish content to blockchain")
		c.JSON(http.StatusInternalServerError, NewErrorResponse(http.StatusInternalServerError, "Failed to publish content", err.Error()))
		return
	}

	h.logger.Info().
		Str("content_ksuid", req.ContentKSUID).
		Uint("record_id", record.ID).
		Msg("Content published to blockchain successfully")

	response := RealUploadResponse{
		Success:   true,
		Message:   "Content uploaded to blockchain successfully",
		OnChainID: record.OnChainID,
		TxHash:    "", // TODO: 从区块链交易中获取
	}

	c.JSON(http.StatusOK, NewSuccessResponse(response))
}

// RegisterContentRoutes 注册内容相关路由
func RegisterContentRoutes(r *gin.RouterGroup, handler *ContentHandler) {
	content := r.Group("/content")
	{
		content.POST("/publish", handler.PublishContent)
		content.POST("/real-upload", handler.RealUpload) // 新增真实上传接口
		content.GET("/records", handler.GetContentRecords)
		content.GET("/stats", handler.GetContentStats)
		content.GET("/stats/types", handler.GetContentTypeStats)
		content.GET("/fees", handler.GetContentFees)
		content.GET("/fees/:content_type", handler.GetContentFee)
		content.GET("/:on_chain_id/detail", handler.GetContentDetailByOnChainID)
		content.GET("/:on_chain_id", handler.GetContentRecord)
	}
}

// RealUploadRequest 真实上传请求结构
type RealUploadRequest struct {
	ContentKSUID string                 `json:"content_ksuid" binding:"required"`
	CreatorKSUID string                 `json:"creator_ksuid" binding:"required"`
	Title        string                 `json:"title" binding:"required"`
	Description  string                 `json:"description"`
	ContentType  string                 `json:"content_type" binding:"required"`
	FileData     string                 `json:"file_data"`
	FileName     string                 `json:"file_name"`
	Metadata     map[string]interface{} `json:"metadata"`
}

// RealUploadResponse 真实上传响应结构
type RealUploadResponse struct {
	Success   bool   `json:"success"`
	Message   string `json:"message"`
	OnChainID string `json:"on_chain_id,omitempty"`
	TxHash    string `json:"tx_hash,omitempty"`
}

// ContentDetailResponse 内容详情响应结构
type ContentDetailResponse struct {
	// Token记录信息
	TokenRecord interface{} `json:"token_record"`
	// 内容详情信息
	ContentDetail interface{} `json:"content_detail"`
}
