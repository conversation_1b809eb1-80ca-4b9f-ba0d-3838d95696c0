package handler

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog/log"

	"pxpat-backend/internal/content-cluster/content-management-service/dto"
	"pxpat-backend/internal/content-cluster/content-management-service/external/service"
	"pxpat-backend/pkg/errors"
	"pxpat-backend/pkg/middleware/tracing"
	"pxpat-backend/pkg/opentelemetry"
	globalTypes "pxpat-backend/pkg/types"
)

// StatsHandler 统计分析处理器
type StatsHandler struct {
	statsService service.StatsAnalysisService
}

// NewStatsHandler 创建统计分析处理器实例
func NewStatsHandler(statsService service.StatsAnalysisService) *StatsHandler {
	return &StatsHandler{
		statsService: statsService,
	}
}

// GetOverviewStats 获取总体统计概览
// @Summary 获取总体统计概览
// @Description 获取内容管理系统的总体统计概览
// @Tags 统计分析
// @Accept json
// @Produce json
// @Success 200 {object} globalTypes.GlobalResponse{data=dto.ContentOverviewResponse}
// @Failure 500 {object} globalTypes.GlobalResponse
// @Router /api/v1/management/stats/overview [get]
func (h *StatsHandler) GetOverviewStats(c *gin.Context) {
	// 1. 链路追踪
	ctx, span := tracing.StartSpanWithComponent(c.Request.Context(), "handler", "GetOverviewStats")
	defer span.End()
	c.Request = c.Request.WithContext(ctx)

	// 2. 获取trace信息
	traceID := tracing.GetTraceID(c)
	spanID := tracing.GetSpanID(c)

	// 3. 添加span属性
	opentelemetry.AddAttribute(span, "method", c.Request.Method)
	opentelemetry.AddAttribute(span, "path", c.Request.URL.Path)

	// 4. 记录请求日志
	log.Info().
		Str("trace_id", traceID).
		Str("span_id", spanID).
		Msg("收到获取总体统计概览请求")

	// 5. 调用服务层
	result, gErr := h.statsService.GetOverviewStats(ctx)
	if gErr != nil {
		log.Error().
			Str("trace_id", traceID).
			Interface("error", gErr).
			Msg("获取总体统计概览失败")

		opentelemetry.AddError(span, gErr)
		c.JSON(http.StatusInternalServerError, globalTypes.GlobalResponse{
			Code: gErr.Code,
			Msg:  gErr.Message,
		})
		return
	}

	// 6. 返回成功响应
	log.Info().
		Str("trace_id", traceID).
		Int64("total_contents", result.TotalContents).
		Int64("published_contents", result.PublishedContents).
		Msg("获取总体统计概览成功")

	c.JSON(http.StatusOK, globalTypes.GlobalResponse{
		Code: errors.SUCCESS,
		Data: result,
	})
}

// GetContentTypeStats 获取内容类型统计
// @Summary 获取内容类型统计
// @Description 获取指定内容类型的统计信息
// @Tags 统计分析
// @Accept json
// @Produce json
// @Param content_type path string true "内容类型" Enums(video, novel, music)
// @Success 200 {object} globalTypes.GlobalResponse{data=dto.ContentTypeStatsResponse}
// @Failure 400 {object} globalTypes.GlobalResponse
// @Failure 500 {object} globalTypes.GlobalResponse
// @Router /api/v1/management/stats/content-types/{content_type} [get]
func (h *StatsHandler) GetContentTypeStats(c *gin.Context) {
	// 1. 链路追踪
	ctx, span := tracing.StartSpanWithComponent(c.Request.Context(), "handler", "GetContentTypeStats")
	defer span.End()
	c.Request = c.Request.WithContext(ctx)

	// 2. 获取trace信息
	traceID := tracing.GetTraceID(c)
	spanID := tracing.GetSpanID(c)

	// 3. 获取路径参数
	contentType := c.Param("content_type")
	if contentType == "" {
		log.Error().
			Str("trace_id", traceID).
			Msg("内容类型参数缺失")

		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code: errors.INVALID_PARAMETER,
			Msg:  "内容类型不能为空",
		})
		return
	}

	// 4. 添加span属性
	opentelemetry.AddAttribute(span, "method", c.Request.Method)
	opentelemetry.AddAttribute(span, "path", c.Request.URL.Path)
	opentelemetry.AddAttribute(span, "content_type", contentType)

	// 5. 记录请求日志
	log.Info().
		Str("trace_id", traceID).
		Str("span_id", spanID).
		Str("content_type", contentType).
		Msg("收到获取内容类型统计请求")

	// 6. 调用服务层
	result, gErr := h.statsService.GetContentTypeStats(ctx, contentType)
	if gErr != nil {
		log.Error().
			Str("trace_id", traceID).
			Str("content_type", contentType).
			Interface("error", gErr).
			Msg("获取内容类型统计失败")

		opentelemetry.AddError(span, gErr)
		
		// 根据错误类型返回不同的HTTP状态码
		statusCode := http.StatusInternalServerError
		if gErr.Code == errors.INVALID_PARAMETER {
			statusCode = http.StatusBadRequest
		}

		c.JSON(statusCode, globalTypes.GlobalResponse{
			Code: gErr.Code,
			Msg:  gErr.Message,
		})
		return
	}

	// 7. 返回成功响应
	log.Info().
		Str("trace_id", traceID).
		Str("content_type", contentType).
		Int64("total_count", result.TotalCount).
		Int64("published_count", result.PublishedCount).
		Msg("获取内容类型统计成功")

	c.JSON(http.StatusOK, globalTypes.GlobalResponse{
		Code: errors.SUCCESS,
		Data: result,
	})
}

// GetAllContentTypeStats 获取所有内容类型统计
// @Summary 获取所有内容类型统计
// @Description 获取所有内容类型的统计信息
// @Tags 统计分析
// @Accept json
// @Produce json
// @Success 200 {object} globalTypes.GlobalResponse{data=[]dto.ContentTypeStatsResponse}
// @Failure 500 {object} globalTypes.GlobalResponse
// @Router /api/v1/management/stats/content-types [get]
func (h *StatsHandler) GetAllContentTypeStats(c *gin.Context) {
	// 1. 链路追踪
	ctx, span := tracing.StartSpanWithComponent(c.Request.Context(), "handler", "GetAllContentTypeStats")
	defer span.End()
	c.Request = c.Request.WithContext(ctx)

	// 2. 获取trace信息
	traceID := tracing.GetTraceID(c)
	spanID := tracing.GetSpanID(c)

	// 3. 添加span属性
	opentelemetry.AddAttribute(span, "method", c.Request.Method)
	opentelemetry.AddAttribute(span, "path", c.Request.URL.Path)

	// 4. 记录请求日志
	log.Info().
		Str("trace_id", traceID).
		Str("span_id", spanID).
		Msg("收到获取所有内容类型统计请求")

	// 5. 调用服务层
	result, gErr := h.statsService.GetAllContentTypeStats(ctx)
	if gErr != nil {
		log.Error().
			Str("trace_id", traceID).
			Interface("error", gErr).
			Msg("获取所有内容类型统计失败")

		opentelemetry.AddError(span, gErr)
		c.JSON(http.StatusInternalServerError, globalTypes.GlobalResponse{
			Code: gErr.Code,
			Msg:  gErr.Message,
		})
		return
	}

	// 6. 返回成功响应
	log.Info().
		Str("trace_id", traceID).
		Int("content_type_count", len(result)).
		Msg("获取所有内容类型统计成功")

	c.JSON(http.StatusOK, globalTypes.GlobalResponse{
		Code: errors.SUCCESS,
		Data: result,
	})
}

// GetUserStats 获取用户统计
// @Summary 获取用户统计
// @Description 获取指定用户的交互统计信息
// @Tags 统计分析
// @Accept json
// @Produce json
// @Param user_ksuid path string true "用户KSUID"
// @Success 200 {object} globalTypes.GlobalResponse{data=dto.UserInteractionStatsResponse}
// @Failure 400 {object} globalTypes.GlobalResponse
// @Failure 500 {object} globalTypes.GlobalResponse
// @Router /api/v1/management/stats/users/{user_ksuid} [get]
func (h *StatsHandler) GetUserStats(c *gin.Context) {
	// 1. 链路追踪
	ctx, span := tracing.StartSpanWithComponent(c.Request.Context(), "handler", "GetUserStats")
	defer span.End()
	c.Request = c.Request.WithContext(ctx)

	// 2. 获取trace信息
	traceID := tracing.GetTraceID(c)
	spanID := tracing.GetSpanID(c)

	// 3. 获取路径参数
	userKSUID := c.Param("user_ksuid")
	if userKSUID == "" {
		log.Error().
			Str("trace_id", traceID).
			Msg("用户KSUID参数缺失")

		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code: errors.INVALID_PARAMETER,
			Msg:  "用户KSUID不能为空",
		})
		return
	}

	// 4. 添加span属性
	opentelemetry.AddAttribute(span, "method", c.Request.Method)
	opentelemetry.AddAttribute(span, "path", c.Request.URL.Path)
	opentelemetry.AddAttribute(span, "user_ksuid", userKSUID)

	// 5. 记录请求日志
	log.Info().
		Str("trace_id", traceID).
		Str("span_id", spanID).
		Str("user_ksuid", userKSUID).
		Msg("收到获取用户统计请求")

	// 6. 调用服务层
	result, gErr := h.statsService.GetUserStats(ctx, userKSUID)
	if gErr != nil {
		log.Error().
			Str("trace_id", traceID).
			Str("user_ksuid", userKSUID).
			Interface("error", gErr).
			Msg("获取用户统计失败")

		opentelemetry.AddError(span, gErr)
		c.JSON(http.StatusInternalServerError, globalTypes.GlobalResponse{
			Code: gErr.Code,
			Msg:  gErr.Message,
		})
		return
	}

	// 7. 返回成功响应
	log.Info().
		Str("trace_id", traceID).
		Str("user_ksuid", userKSUID).
		Msg("获取用户统计成功")

	c.JSON(http.StatusOK, globalTypes.GlobalResponse{
		Code: errors.SUCCESS,
		Data: result,
	})
}

// GetActiveUsers 获取活跃用户列表
// @Summary 获取活跃用户列表
// @Description 获取指定时间段内的活跃用户列表
// @Tags 统计分析
// @Accept json
// @Produce json
// @Param period query string false "时间段" default(weekly) Enums(daily, weekly, monthly)
// @Param limit query int false "限制数量" default(10)
// @Success 200 {object} globalTypes.GlobalResponse{data=dto.ActiveUserListResponse}
// @Failure 400 {object} globalTypes.GlobalResponse
// @Failure 500 {object} globalTypes.GlobalResponse
// @Router /api/v1/management/stats/active-users [get]
func (h *StatsHandler) GetActiveUsers(c *gin.Context) {
	// 1. 链路追踪
	ctx, span := tracing.StartSpanWithComponent(c.Request.Context(), "handler", "GetActiveUsers")
	defer span.End()
	c.Request = c.Request.WithContext(ctx)

	// 2. 获取trace信息
	traceID := tracing.GetTraceID(c)
	spanID := tracing.GetSpanID(c)

	// 3. 获取查询参数
	period := c.DefaultQuery("period", "weekly")
	limitStr := c.DefaultQuery("limit", "10")

	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit <= 0 {
		limit = 10
	}

	// 4. 添加span属性
	opentelemetry.AddAttribute(span, "method", c.Request.Method)
	opentelemetry.AddAttribute(span, "path", c.Request.URL.Path)
	opentelemetry.AddAttribute(span, "period", period)
	opentelemetry.AddAttribute(span, "limit", limit)

	// 5. 记录请求日志
	log.Info().
		Str("trace_id", traceID).
		Str("span_id", spanID).
		Str("period", period).
		Int("limit", limit).
		Msg("收到获取活跃用户列表请求")

	// 6. 调用服务层
	result, gErr := h.statsService.GetActiveUsers(ctx, period, limit)
	if gErr != nil {
		log.Error().
			Str("trace_id", traceID).
			Str("period", period).
			Int("limit", limit).
			Interface("error", gErr).
			Msg("获取活跃用户列表失败")

		opentelemetry.AddError(span, gErr)

		// 根据错误类型返回不同的HTTP状态码
		statusCode := http.StatusInternalServerError
		if gErr.Code == errors.INVALID_PARAMETER {
			statusCode = http.StatusBadRequest
		}

		c.JSON(statusCode, globalTypes.GlobalResponse{
			Code: gErr.Code,
			Msg:  gErr.Message,
		})
		return
	}

	// 7. 返回成功响应
	log.Info().
		Str("trace_id", traceID).
		Str("period", period).
		Int("user_count", len(result.Users)).
		Msg("获取活跃用户列表成功")

	c.JSON(http.StatusOK, globalTypes.GlobalResponse{
		Code: errors.SUCCESS,
		Data: result,
	})
}

// GetContentTrends 获取内容趋势分析
// @Summary 获取内容趋势分析
// @Description 获取指定时间段和内容类型的趋势分析
// @Tags 统计分析
// @Accept json
// @Produce json
// @Param period query string false "时间段" default(weekly) Enums(daily, weekly, monthly)
// @Param content_type query string false "内容类型" Enums(video, novel, music)
// @Success 200 {object} globalTypes.GlobalResponse{data=dto.ContentTrendsStatsResponse}
// @Failure 400 {object} globalTypes.GlobalResponse
// @Failure 500 {object} globalTypes.GlobalResponse
// @Router /api/v1/management/stats/trends [get]
func (h *StatsHandler) GetContentTrends(c *gin.Context) {
	// 1. 链路追踪
	ctx, span := tracing.StartSpanWithComponent(c.Request.Context(), "handler", "GetContentTrends")
	defer span.End()
	c.Request = c.Request.WithContext(ctx)

	// 2. 获取trace信息
	traceID := tracing.GetTraceID(c)
	spanID := tracing.GetSpanID(c)

	// 3. 获取查询参数
	period := c.DefaultQuery("period", "weekly")
	contentType := c.Query("content_type")

	// 4. 添加span属性
	opentelemetry.AddAttribute(span, "method", c.Request.Method)
	opentelemetry.AddAttribute(span, "path", c.Request.URL.Path)
	opentelemetry.AddAttribute(span, "period", period)
	opentelemetry.AddAttribute(span, "content_type", contentType)

	// 5. 记录请求日志
	log.Info().
		Str("trace_id", traceID).
		Str("span_id", spanID).
		Str("period", period).
		Str("content_type", contentType).
		Msg("收到获取内容趋势分析请求")

	// 6. 调用服务层
	result, gErr := h.statsService.GetContentTrends(ctx, period, contentType)
	if gErr != nil {
		log.Error().
			Str("trace_id", traceID).
			Str("period", period).
			Str("content_type", contentType).
			Interface("error", gErr).
			Msg("获取内容趋势分析失败")

		opentelemetry.AddError(span, gErr)

		// 根据错误类型返回不同的HTTP状态码
		statusCode := http.StatusInternalServerError
		if gErr.Code == errors.INVALID_PARAMETER {
			statusCode = http.StatusBadRequest
		}

		c.JSON(statusCode, globalTypes.GlobalResponse{
			Code: gErr.Code,
			Msg:  gErr.Message,
		})
		return
	}

	// 7. 返回成功响应
	log.Info().
		Str("trace_id", traceID).
		Str("period", period).
		Str("content_type", contentType).
		Int("data_points", len(result.ContentCounts)).
		Msg("获取内容趋势分析成功")

	c.JSON(http.StatusOK, globalTypes.GlobalResponse{
		Code: errors.SUCCESS,
		Data: result,
	})
}

// GetPopularContents 获取热门内容列表
// @Summary 获取热门内容列表
// @Description 获取指定时间段内的热门内容列表
// @Tags 统计分析
// @Accept json
// @Produce json
// @Param period query string false "时间段" default(weekly) Enums(daily, weekly, monthly)
// @Param limit query int false "限制数量" default(10)
// @Success 200 {object} globalTypes.GlobalResponse{data=dto.PopularContentListResponse}
// @Failure 400 {object} globalTypes.GlobalResponse
// @Failure 500 {object} globalTypes.GlobalResponse
// @Router /api/v1/management/stats/popular-contents [get]
func (h *StatsHandler) GetPopularContents(c *gin.Context) {
	// 1. 链路追踪
	ctx, span := tracing.StartSpanWithComponent(c.Request.Context(), "handler", "GetPopularContents")
	defer span.End()
	c.Request = c.Request.WithContext(ctx)

	// 2. 获取trace信息
	traceID := tracing.GetTraceID(c)
	spanID := tracing.GetSpanID(c)

	// 3. 获取查询参数
	period := c.DefaultQuery("period", "weekly")
	limitStr := c.DefaultQuery("limit", "10")

	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit <= 0 {
		limit = 10
	}

	// 4. 添加span属性
	opentelemetry.AddAttribute(span, "method", c.Request.Method)
	opentelemetry.AddAttribute(span, "path", c.Request.URL.Path)
	opentelemetry.AddAttribute(span, "period", period)
	opentelemetry.AddAttribute(span, "limit", limit)

	// 5. 记录请求日志
	log.Info().
		Str("trace_id", traceID).
		Str("span_id", spanID).
		Str("period", period).
		Int("limit", limit).
		Msg("收到获取热门内容列表请求")

	// 6. 调用服务层
	result, gErr := h.statsService.GetPopularContents(ctx, period, limit)
	if gErr != nil {
		log.Error().
			Str("trace_id", traceID).
			Str("period", period).
			Int("limit", limit).
			Interface("error", gErr).
			Msg("获取热门内容列表失败")

		opentelemetry.AddError(span, gErr)

		// 根据错误类型返回不同的HTTP状态码
		statusCode := http.StatusInternalServerError
		if gErr.Code == errors.INVALID_PARAMETER {
			statusCode = http.StatusBadRequest
		}

		c.JSON(statusCode, globalTypes.GlobalResponse{
			Code: gErr.Code,
			Msg:  gErr.Message,
		})
		return
	}

	// 7. 返回成功响应
	log.Info().
		Str("trace_id", traceID).
		Str("period", period).
		Int("content_count", len(result.Contents)).
		Msg("获取热门内容列表成功")

	c.JSON(http.StatusOK, globalTypes.GlobalResponse{
		Code: errors.SUCCESS,
		Data: result,
	})
}

// GetCategoryStats 获取分类统计
// @Summary 获取分类统计
// @Description 获取所有分类的统计信息
// @Tags 统计分析
// @Accept json
// @Produce json
// @Success 200 {object} globalTypes.GlobalResponse{data=[]dto.CategoryStatsResponse}
// @Failure 500 {object} globalTypes.GlobalResponse
// @Router /api/v1/management/stats/categories [get]
func (h *StatsHandler) GetCategoryStats(c *gin.Context) {
	// 1. 链路追踪
	ctx, span := tracing.StartSpanWithComponent(c.Request.Context(), "handler", "GetCategoryStats")
	defer span.End()
	c.Request = c.Request.WithContext(ctx)

	// 2. 获取trace信息
	traceID := tracing.GetTraceID(c)
	spanID := tracing.GetSpanID(c)

	// 3. 添加span属性
	opentelemetry.AddAttribute(span, "method", c.Request.Method)
	opentelemetry.AddAttribute(span, "path", c.Request.URL.Path)

	// 4. 记录请求日志
	log.Info().
		Str("trace_id", traceID).
		Str("span_id", spanID).
		Msg("收到获取分类统计请求")

	// 5. 调用服务层
	result, gErr := h.statsService.GetCategoryStats(ctx)
	if gErr != nil {
		log.Error().
			Str("trace_id", traceID).
			Interface("error", gErr).
			Msg("获取分类统计失败")

		opentelemetry.AddError(span, gErr)
		c.JSON(http.StatusInternalServerError, globalTypes.GlobalResponse{
			Code: gErr.Code,
			Msg:  gErr.Message,
		})
		return
	}

	// 6. 返回成功响应
	log.Info().
		Str("trace_id", traceID).
		Int("category_count", len(result)).
		Msg("获取分类统计成功")

	c.JSON(http.StatusOK, globalTypes.GlobalResponse{
		Code: errors.SUCCESS,
		Data: result,
	})
}

// GetTagStats 获取标签统计
// @Summary 获取标签统计
// @Description 获取热门标签的统计信息
// @Tags 统计分析
// @Accept json
// @Produce json
// @Param limit query int false "限制数量" default(20)
// @Success 200 {object} globalTypes.GlobalResponse{data=[]dto.TagStatsResponse}
// @Failure 400 {object} globalTypes.GlobalResponse
// @Failure 500 {object} globalTypes.GlobalResponse
// @Router /api/v1/management/stats/tags [get]
func (h *StatsHandler) GetTagStats(c *gin.Context) {
	// 1. 链路追踪
	ctx, span := tracing.StartSpanWithComponent(c.Request.Context(), "handler", "GetTagStats")
	defer span.End()
	c.Request = c.Request.WithContext(ctx)

	// 2. 获取trace信息
	traceID := tracing.GetTraceID(c)
	spanID := tracing.GetSpanID(c)

	// 3. 获取查询参数
	limitStr := c.DefaultQuery("limit", "20")

	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit <= 0 {
		limit = 20
	}

	// 4. 添加span属性
	opentelemetry.AddAttribute(span, "method", c.Request.Method)
	opentelemetry.AddAttribute(span, "path", c.Request.URL.Path)
	opentelemetry.AddAttribute(span, "limit", limit)

	// 5. 记录请求日志
	log.Info().
		Str("trace_id", traceID).
		Str("span_id", spanID).
		Int("limit", limit).
		Msg("收到获取标签统计请求")

	// 6. 调用服务层
	result, gErr := h.statsService.GetTagStats(ctx, limit)
	if gErr != nil {
		log.Error().
			Str("trace_id", traceID).
			Int("limit", limit).
			Interface("error", gErr).
			Msg("获取标签统计失败")

		opentelemetry.AddError(span, gErr)

		// 根据错误类型返回不同的HTTP状态码
		statusCode := http.StatusInternalServerError
		if gErr.Code == errors.INVALID_PARAMETER {
			statusCode = http.StatusBadRequest
		}

		c.JSON(statusCode, globalTypes.GlobalResponse{
			Code: gErr.Code,
			Msg:  gErr.Message,
		})
		return
	}

	// 7. 返回成功响应
	log.Info().
		Str("trace_id", traceID).
		Int("tag_count", len(result)).
		Msg("获取标签统计成功")

	c.JSON(http.StatusOK, globalTypes.GlobalResponse{
		Code: errors.SUCCESS,
		Data: result,
	})
}

// GetDashboardStats 获取仪表板统计
// @Summary 获取仪表板统计
// @Description 获取管理后台仪表板的综合统计信息
// @Tags 统计分析
// @Accept json
// @Produce json
// @Success 200 {object} globalTypes.GlobalResponse{data=dto.DashboardStatsResponse}
// @Failure 500 {object} globalTypes.GlobalResponse
// @Router /api/v1/management/stats/dashboard [get]
func (h *StatsHandler) GetDashboardStats(c *gin.Context) {
	// 1. 链路追踪
	ctx, span := tracing.StartSpanWithComponent(c.Request.Context(), "handler", "GetDashboardStats")
	defer span.End()
	c.Request = c.Request.WithContext(ctx)

	// 2. 获取trace信息
	traceID := tracing.GetTraceID(c)
	spanID := tracing.GetSpanID(c)

	// 3. 添加span属性
	opentelemetry.AddAttribute(span, "method", c.Request.Method)
	opentelemetry.AddAttribute(span, "path", c.Request.URL.Path)

	// 4. 记录请求日志
	log.Info().
		Str("trace_id", traceID).
		Str("span_id", spanID).
		Msg("收到获取仪表板统计请求")

	// 5. 调用服务层
	result, gErr := h.statsService.GetDashboardStats(ctx)
	if gErr != nil {
		log.Error().
			Str("trace_id", traceID).
			Interface("error", gErr).
			Msg("获取仪表板统计失败")

		opentelemetry.AddError(span, gErr)
		c.JSON(http.StatusInternalServerError, globalTypes.GlobalResponse{
			Code: gErr.Code,
			Msg:  gErr.Message,
		})
		return
	}

	// 6. 返回成功响应
	log.Info().
		Str("trace_id", traceID).
		Msg("获取仪表板统计成功")

	c.JSON(http.StatusOK, globalTypes.GlobalResponse{
		Code: errors.SUCCESS,
		Data: result,
	})
}

// HealthCheck 健康检查
// @Summary 健康检查
// @Description 检查统计分析服务的健康状态
// @Tags 系统管理
// @Accept json
// @Produce json
// @Success 200 {object} globalTypes.GlobalResponse
// @Failure 500 {object} globalTypes.GlobalResponse
// @Router /api/v1/management/stats/health [get]
func (h *StatsHandler) HealthCheck(c *gin.Context) {
	// 1. 链路追踪
	ctx, span := tracing.StartSpanWithComponent(c.Request.Context(), "handler", "StatsHealthCheck")
	defer span.End()
	c.Request = c.Request.WithContext(ctx)

	// 2. 获取trace信息
	traceID := tracing.GetTraceID(c)

	// 3. 添加span属性
	opentelemetry.AddAttribute(span, "method", c.Request.Method)
	opentelemetry.AddAttribute(span, "path", c.Request.URL.Path)

	// 4. 记录请求日志
	log.Debug().
		Str("trace_id", traceID).
		Msg("收到统计分析服务健康检查请求")

	// 5. 调用服务层
	gErr := h.statsService.HealthCheck(ctx)
	if gErr != nil {
		log.Error().
			Str("trace_id", traceID).
			Interface("error", gErr).
			Msg("统计分析服务健康检查失败")

		opentelemetry.AddError(span, gErr)
		c.JSON(http.StatusInternalServerError, globalTypes.GlobalResponse{
			Code: gErr.Code,
			Msg:  gErr.Message,
		})
		return
	}

	// 6. 返回成功响应
	log.Debug().
		Str("trace_id", traceID).
		Msg("统计分析服务健康检查成功")

	c.JSON(http.StatusOK, globalTypes.GlobalResponse{
		Code: errors.SUCCESS,
		Msg:  "统计分析服务健康",
	})
}
