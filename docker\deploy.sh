#!/bin/bash

# 内容管理服务部署脚本
# 支持开发环境和生产环境部署

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"

# 默认配置
ENVIRONMENT="production"
COMPOSE_FILE="docker-compose.yml"
BUILD_CACHE="true"
PULL_IMAGES="true"
RUN_TESTS="false"
BACKUP_DATA="false"

# 显示帮助信息
show_help() {
    echo -e "${BLUE}内容管理服务部署脚本${NC}"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -e, --env ENVIRONMENT     部署环境 (dev|production) [默认: production]"
    echo "  -f, --file FILE          Docker Compose文件 [默认: docker-compose.yml]"
    echo "  --no-cache               构建时不使用缓存"
    echo "  --no-pull                不拉取最新镜像"
    echo "  --test                   部署前运行测试"
    echo "  --backup                 部署前备份数据"
    echo "  -h, --help               显示帮助信息"
    echo ""
    echo "示例:"
    echo "  $0                       # 生产环境部署"
    echo "  $0 -e dev                # 开发环境部署"
    echo "  $0 --test --backup       # 运行测试并备份数据后部署"
}

# 解析命令行参数
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -e|--env)
                ENVIRONMENT="$2"
                shift 2
                ;;
            -f|--file)
                COMPOSE_FILE="$2"
                shift 2
                ;;
            --no-cache)
                BUILD_CACHE="false"
                shift
                ;;
            --no-pull)
                PULL_IMAGES="false"
                shift
                ;;
            --test)
                RUN_TESTS="true"
                shift
                ;;
            --backup)
                BACKUP_DATA="true"
                shift
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                echo -e "${RED}未知选项: $1${NC}"
                show_help
                exit 1
                ;;
        esac
    done
}

# 验证环境
validate_environment() {
    case $ENVIRONMENT in
        dev|development)
            ENVIRONMENT="dev"
            COMPOSE_FILE="docker-compose.dev.yml"
            ;;
        prod|production)
            ENVIRONMENT="production"
            COMPOSE_FILE="docker-compose.yml"
            ;;
        *)
            echo -e "${RED}无效的环境: $ENVIRONMENT${NC}"
            echo "支持的环境: dev, production"
            exit 1
            ;;
    esac
}

# 检查依赖
check_dependencies() {
    echo -e "${YELLOW}检查依赖...${NC}"
    
    # 检查Docker
    if ! command -v docker &> /dev/null; then
        echo -e "${RED}错误: Docker未安装${NC}"
        exit 1
    fi
    
    # 检查Docker Compose
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        echo -e "${RED}错误: Docker Compose未安装${NC}"
        exit 1
    fi
    
    # 检查配置文件
    if [[ ! -f "$SCRIPT_DIR/$COMPOSE_FILE" ]]; then
        echo -e "${RED}错误: 找不到Docker Compose文件: $COMPOSE_FILE${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}✓ 依赖检查通过${NC}"
}

# 运行测试
run_tests() {
    if [[ "$RUN_TESTS" == "true" ]]; then
        echo -e "${YELLOW}运行测试...${NC}"
        cd "$PROJECT_ROOT"
        
        # 运行单元测试
        make test-unit || {
            echo -e "${RED}✗ 单元测试失败${NC}"
            exit 1
        }
        
        # 运行集成测试
        make test-integration || {
            echo -e "${RED}✗ 集成测试失败${NC}"
            exit 1
        }
        
        echo -e "${GREEN}✓ 测试通过${NC}"
    fi
}

# 备份数据
backup_data() {
    if [[ "$BACKUP_DATA" == "true" ]]; then
        echo -e "${YELLOW}备份数据...${NC}"
        
        BACKUP_DIR="$SCRIPT_DIR/backups/$(date +%Y%m%d_%H%M%S)"
        mkdir -p "$BACKUP_DIR"
        
        # 备份PostgreSQL数据
        if docker ps | grep -q postgres; then
            echo "备份PostgreSQL数据..."
            docker exec content-postgres pg_dumpall -U content_user > "$BACKUP_DIR/postgres_backup.sql"
        fi
        
        # 备份Redis数据
        if docker ps | grep -q redis; then
            echo "备份Redis数据..."
            docker exec content-redis redis-cli --rdb - > "$BACKUP_DIR/redis_backup.rdb"
        fi
        
        echo -e "${GREEN}✓ 数据备份完成: $BACKUP_DIR${NC}"
    fi
}

# 构建镜像
build_images() {
    echo -e "${YELLOW}构建镜像...${NC}"
    
    cd "$SCRIPT_DIR"
    
    BUILD_ARGS=""
    if [[ "$BUILD_CACHE" == "false" ]]; then
        BUILD_ARGS="--no-cache"
    fi
    
    if command -v docker-compose &> /dev/null; then
        docker-compose -f "$COMPOSE_FILE" build $BUILD_ARGS
    else
        docker compose -f "$COMPOSE_FILE" build $BUILD_ARGS
    fi
    
    echo -e "${GREEN}✓ 镜像构建完成${NC}"
}

# 拉取镜像
pull_images() {
    if [[ "$PULL_IMAGES" == "true" ]]; then
        echo -e "${YELLOW}拉取最新镜像...${NC}"
        
        cd "$SCRIPT_DIR"
        
        if command -v docker-compose &> /dev/null; then
            docker-compose -f "$COMPOSE_FILE" pull
        else
            docker compose -f "$COMPOSE_FILE" pull
        fi
        
        echo -e "${GREEN}✓ 镜像拉取完成${NC}"
    fi
}

# 启动服务
start_services() {
    echo -e "${YELLOW}启动服务...${NC}"
    
    cd "$SCRIPT_DIR"
    
    if command -v docker-compose &> /dev/null; then
        docker-compose -f "$COMPOSE_FILE" up -d
    else
        docker compose -f "$COMPOSE_FILE" up -d
    fi
    
    echo -e "${GREEN}✓ 服务启动完成${NC}"
}

# 等待服务就绪
wait_for_services() {
    echo -e "${YELLOW}等待服务就绪...${NC}"
    
    # 等待数据库就绪
    echo "等待PostgreSQL..."
    timeout=60
    while ! docker exec content-postgres pg_isready -U content_user -d content_management &>/dev/null; do
        sleep 2
        timeout=$((timeout - 2))
        if [[ $timeout -le 0 ]]; then
            echo -e "${RED}✗ PostgreSQL启动超时${NC}"
            exit 1
        fi
    done
    
    # 等待Redis就绪
    echo "等待Redis..."
    timeout=60
    while ! docker exec content-redis redis-cli ping &>/dev/null; do
        sleep 2
        timeout=$((timeout - 2))
        if [[ $timeout -le 0 ]]; then
            echo -e "${RED}✗ Redis启动超时${NC}"
            exit 1
        fi
    done
    
    # 等待应用服务就绪
    echo "等待内容管理服务..."
    timeout=120
    while ! curl -f http://localhost:12010/api/v1/health &>/dev/null; do
        sleep 5
        timeout=$((timeout - 5))
        if [[ $timeout -le 0 ]]; then
            echo -e "${RED}✗ 内容管理服务启动超时${NC}"
            exit 1
        fi
    done
    
    echo -e "${GREEN}✓ 所有服务就绪${NC}"
}

# 显示服务状态
show_status() {
    echo -e "${BLUE}=== 服务状态 ===${NC}"
    
    cd "$SCRIPT_DIR"
    
    if command -v docker-compose &> /dev/null; then
        docker-compose -f "$COMPOSE_FILE" ps
    else
        docker compose -f "$COMPOSE_FILE" ps
    fi
    
    echo ""
    echo -e "${BLUE}=== 服务访问地址 ===${NC}"
    echo "内容管理服务: http://localhost:12010"
    echo "健康检查: http://localhost:12010/api/v1/health"
    
    if [[ "$ENVIRONMENT" == "production" ]]; then
        echo "Prometheus: http://localhost:9090"
        echo "Grafana: http://localhost:3000 (admin/admin123)"
        echo "Jaeger: http://localhost:16686"
    else
        echo "pgAdmin: http://localhost:8080 (<EMAIL>/admin123)"
        echo "Redis Commander: http://localhost:8081"
        echo "MailHog: http://localhost:8025"
    fi
}

# 主函数
main() {
    echo -e "${BLUE}=== 内容管理服务部署 ===${NC}"
    echo "环境: $ENVIRONMENT"
    echo "Compose文件: $COMPOSE_FILE"
    echo ""
    
    check_dependencies
    run_tests
    backup_data
    pull_images
    build_images
    start_services
    wait_for_services
    show_status
    
    echo ""
    echo -e "${GREEN}🎉 部署完成！${NC}"
    echo -e "${YELLOW}使用以下命令查看日志:${NC}"
    echo "  docker-compose -f $COMPOSE_FILE logs -f content-management-service"
}

# 解析参数并执行
parse_args "$@"
validate_environment
main
