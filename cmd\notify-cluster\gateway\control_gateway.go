package main

import (
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog"
	"github.com/rs/zerolog/log"
	"net/http"
	"os"
	"pxpat-backend/internal/notify-cluster/gateway/control/router"
	"pxpat-backend/internal/notify-cluster/gateway/control/types"
	configLoader "pxpat-backend/pkg/config"
)

func main() {
	// 配置日志
	zerolog.TimeFieldFormat = zerolog.TimeFormatUnix
	log.Logger = log.Output(zerolog.ConsoleWriter{Out: os.Stderr})

	clusterName := "notify"
	gatewayName := "control_gateway"

	cfg := configLoader.Load[types.Config](configLoader.LoaderConfig{
		TypeName:       configLoader.Gateway,
		ClusterName:    clusterName,
		GatewayName:    gatewayName,
		UseRedis:       false,
		IsMultiGateway: true,
	})

	r := gin.Default()
	// 注册API路由
	router.RegisterRoutes(r, cfg)

	// 5. 启动网关服务
	addr := fmt.Sprintf("%s:%d", cfg.Server.Host, cfg.Server.Port)
	server := &http.Server{
		Addr:    addr,
		Handler: r,
	}

	log.Printf("Notify Monitor Gateway started on %s", addr)
	if err := server.ListenAndServe(); err != nil {
		log.Fatal().Err(err).Msg(fmt.Sprintf("%s-%s 启动网关服务失败\n", clusterName, gatewayName))
	}
}
