package main

import (
	"context"
	"fmt"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
	"github.com/rs/zerolog/log"
	"go.uber.org/fx"
	"gorm.io/gorm"

	"pxpat-backend/cmd"
	"pxpat-backend/internal/content-cluster/content-management-service/client"
	"pxpat-backend/internal/content-cluster/content-management-service/config"
	"pxpat-backend/internal/content-cluster/content-management-service/external/handler"
	"pxpat-backend/internal/content-cluster/content-management-service/external/service"
	"pxpat-backend/internal/content-cluster/content-management-service/migrations"
	"pxpat-backend/internal/content-cluster/content-management-service/repository"
	"pxpat-backend/internal/content-cluster/content-management-service/routes"
	"pxpat-backend/internal/content-cluster/content-management-service/types"
	"pxpat-backend/internal/content-cluster/content-management-service/utils"
	"pxpat-backend/pkg/cache"
	"pxpat-backend/pkg/consul"
	"pxpat-backend/pkg/database"
	"pxpat-backend/pkg/health"
	"pxpat-backend/pkg/jwt"
	"pxpat-backend/pkg/logger"
	globalTypes "pxpat-backend/pkg/types"
)

// 1. 配置提供函数
func provideConfig() *types.Config {
	cfg := &types.Config{}
	if err := config.LoadConfig(cfg); err != nil {
		log.Fatal().Err(err).Msg("加载配置失败")
	}
	return cfg
}

// 2. 基础设施提供函数
func provideLogger(cfg *types.Config) {
	logger.InitLogger(cfg.Log)
}

func provideDatabase(cfg *types.Config) (*gorm.DB, error) {
	db, err := database.NewPostgresConnection(cfg.Database)
	if err != nil {
		return nil, fmt.Errorf("连接数据库失败: %w", err)
	}

	// 自动迁移
	if err := migrations.AutoMigrate(db); err != nil {
		return nil, fmt.Errorf("数据库迁移失败: %w", err)
	}

	return db, nil
}

func provideRedis(cfg *types.Config) (*redis.Client, error) {
	rdb := redis.NewClient(&redis.Options{
		Addr:     cfg.Redis.Address,
		Password: cfg.Redis.Password,
		DB:       cfg.Redis.DB,
	})

	// 测试连接
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := rdb.Ping(ctx).Err(); err != nil {
		return nil, fmt.Errorf("连接Redis失败: %w", err)
	}

	return rdb, nil
}

func provideCacheManager(rdb *redis.Client) cache.Manager {
	return cache.NewRedisManager(rdb)
}

func provideJWTManager(cfg *types.Config) jwt.Manager {
	return jwt.NewJWTManager(cfg.JWT)
}

// 3. 服务客户端提供函数
func provideVideoServiceClient(cfg *types.Config) client.VideoServiceClient {
	return client.NewVideoServiceClient(
		cfg.Services.VideoService.BaseURL,
		cfg.Services.VideoService.Timeout,
	)
}

func provideInteractionServiceClient(cfg *types.Config) client.InteractionServiceClient {
	return client.NewInteractionServiceClient(
		cfg.Services.InteractionService.BaseURL,
		cfg.Services.InteractionService.Timeout,
	)
}

// 4. Repository层提供函数
func provideOperationLogRepository(db *gorm.DB, cacheManager cache.Manager) repository.OperationLogRepository {
	return repository.NewOperationLogRepository(db, cacheManager)
}

func provideContentCacheRepository(db *gorm.DB, rdb *redis.Client, cacheManager cache.Manager) repository.ContentCacheRepository {
	return repository.NewContentCacheRepository(db, rdb, cacheManager)
}

func provideConfigRepository(db *gorm.DB, cacheManager cache.Manager) repository.ConfigRepository {
	return repository.NewConfigRepository(db, cacheManager)
}

// 5. 工具函数提供函数
func provideContentConverter() utils.ContentConverter {
	return utils.NewContentConverter()
}

func provideDataAggregator(
	videoClient client.VideoServiceClient,
	interactionClient client.InteractionServiceClient,
) utils.DataAggregator {
	return utils.NewDataAggregator(videoClient, interactionClient)
}

func provideContentValidator() utils.ContentValidator {
	return utils.NewContentValidator()
}

// 6. Service层提供函数
func provideContentManagementService(
	videoClient client.VideoServiceClient,
	interactionClient client.InteractionServiceClient,
	cacheRepo repository.ContentCacheRepository,
	operationRepo repository.OperationLogRepository,
	configRepo repository.ConfigRepository,
	converter utils.ContentConverter,
	aggregator utils.DataAggregator,
	validator utils.ContentValidator,
	rdb *redis.Client,
) service.ContentManagementService {
	return service.NewContentManagementService(
		videoClient,
		interactionClient,
		cacheRepo,
		operationRepo,
		configRepo,
		converter,
		aggregator,
		validator,
		rdb,
	)
}

func provideStatsAnalysisService(
	videoClient client.VideoServiceClient,
	interactionClient client.InteractionServiceClient,
	cacheRepo repository.ContentCacheRepository,
	operationRepo repository.OperationLogRepository,
	configRepo repository.ConfigRepository,
	aggregator utils.DataAggregator,
	rdb *redis.Client,
) service.StatsAnalysisService {
	return service.NewStatsAnalysisService(
		videoClient,
		interactionClient,
		cacheRepo,
		operationRepo,
		configRepo,
		aggregator,
		rdb,
	)
}

func provideBatchOperationService(
	videoClient client.VideoServiceClient,
	interactionClient client.InteractionServiceClient,
	cacheRepo repository.ContentCacheRepository,
	operationRepo repository.OperationLogRepository,
	configRepo repository.ConfigRepository,
	converter utils.ContentConverter,
	aggregator utils.DataAggregator,
	validator utils.ContentValidator,
	rdb *redis.Client,
) service.BatchOperationService {
	return service.NewBatchOperationService(
		videoClient,
		interactionClient,
		cacheRepo,
		operationRepo,
		configRepo,
		converter,
		aggregator,
		validator,
		rdb,
	)
}

// 7. Handler层提供函数
func provideContentHandler(contentService service.ContentManagementService) *handler.ContentHandler {
	return handler.NewContentHandler(contentService)
}

func provideStatsHandler(statsService service.StatsAnalysisService) *handler.StatsHandler {
	return handler.NewStatsHandler(statsService)
}

func provideBatchHandler(batchService service.BatchOperationService) *handler.BatchHandler {
	return handler.NewBatchHandler(batchService)
}

// 8. 路由和服务器提供函数
func provideGinEngine(
	cfg *types.Config,
	contentHandler *handler.ContentHandler,
	statsHandler *handler.StatsHandler,
	batchHandler *handler.BatchHandler,
	jwtManager jwt.Manager,
	rdb *redis.Client,
) *gin.Engine {
	routerConfig := routes.RouterConfig{
		ContentHandler: contentHandler,
		StatsHandler:   statsHandler,
		BatchHandler:   batchHandler,
		JWTManager:     jwtManager,
		Redis:          rdb,
		EnableSwagger:  cfg.Server.EnableSwagger,
		EnableCORS:     cfg.Server.EnableCORS,
		Debug:          cfg.Server.Debug,
	}

	return routes.SetupRouter(routerConfig)
}

// 9. 健康检查提供函数
func provideHealthHandler(
	consulManager *consul.Manager,
	db *gorm.DB,
	rdb *redis.Client,
) *consul.HealthHandler {
	healthHandler := consul.NewHealthHandler(consulManager, "1.0.0")

	// 添加数据库健康检查
	healthHandler.AddChecker(health.NewDatabaseHealthChecker("database", func() error {
		sqlDB, err := db.DB()
		if err != nil {
			return err
		}
		return sqlDB.Ping()
	}))

	// 添加Redis健康检查
	healthHandler.AddChecker(health.NewRedisHealthChecker("redis", func() error {
		return rdb.Ping(context.Background()).Err()
	}))

	return healthHandler
}

// 10. Consul管理器提供函数
func provideConsulManager(cfg *types.Config, healthHandler *consul.HealthHandler) *consul.Manager {
	return consul.NewManager(cfg.Consul, healthHandler)
}

// 11. 生命周期管理
func manageLifecycle(
	lc fx.Lifecycle,
	cfg *types.Config,
	consulManager *consul.Manager,
	ginEngine *gin.Engine,
) {
	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) error {
			log.Info().Msg("启动内容管理服务")

			// 启动Consul管理器
			if err := consulManager.Start(ctx); err != nil {
				return fmt.Errorf("启动Consul管理器失败: %w", err)
			}

			// 启动HTTP服务器
			go func() {
				serverConfig := globalTypes.GlobalServerConfig{
					Port:          cfg.Server.Port,
					ReadTimeout:   cfg.Server.ReadTimeout,
					WriteTimeout:  cfg.Server.WriteTimeout,
					IdleTimeout:   cfg.Server.IdleTimeout,
					EnableGracefulShutdown: cfg.Server.EnableGracefulShutdown,
				}
				cmd.GraceStartAndClose(serverConfig, ginEngine)
			}()

			log.Info().
				Int("port", cfg.Server.Port).
				Msg("内容管理服务启动成功")

			return nil
		},
		OnStop: func(ctx context.Context) error {
			log.Info().Msg("停止内容管理服务")

			// 停止Consul管理器
			if err := consulManager.Stop(); err != nil {
				log.Error().Err(err).Msg("停止Consul管理器失败")
			}

			log.Info().Msg("内容管理服务停止完成")
			return nil
		},
	})
}

// 12. main函数
func main() {
	app := fx.New(
		// 配置和基础设施
		fx.Provide(
			provideConfig,
			provideDatabase,
			provideRedis,
			provideCacheManager,
			provideJWTManager,
		),

		// 服务客户端
		fx.Provide(
			provideVideoServiceClient,
			provideInteractionServiceClient,
		),

		// Repository层
		fx.Provide(
			provideOperationLogRepository,
			provideContentCacheRepository,
			provideConfigRepository,
		),

		// 工具函数
		fx.Provide(
			provideContentConverter,
			provideDataAggregator,
			provideContentValidator,
		),

		// Service层
		fx.Provide(
			provideContentManagementService,
			provideStatsAnalysisService,
			provideBatchOperationService,
		),

		// Handler层
		fx.Provide(
			provideContentHandler,
			provideStatsHandler,
			provideBatchHandler,
		),

		// 路由和服务器
		fx.Provide(
			provideGinEngine,
			provideHealthHandler,
			provideConsulManager,
		),

		// 初始化和生命周期管理
		fx.Invoke(
			provideLogger,
			manageLifecycle,
		),
	)

	app.Run()
}
