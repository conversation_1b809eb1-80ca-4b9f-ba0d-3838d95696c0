package middleware

import (
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
	"github.com/rs/zerolog/log"

	"pxpat-backend/pkg/errors"
	"pxpat-backend/pkg/middleware/ksuid"
	"pxpat-backend/pkg/middleware/tracing"
	"pxpat-backend/pkg/opentelemetry"
	globalTypes "pxpat-backend/pkg/types"
)

// RateLimitConfig 限流配置
type RateLimitConfig struct {
	Limit    int           // 限制次数
	Window   time.Duration // 时间窗口
	KeyFunc  func(*gin.Context) string // 生成限流键的函数
	Message  string        // 限流提示消息
}

// RateLimitMiddleware 限流中间件
type RateLimitMiddleware struct {
	redis *redis.Client
}

// NewRateLimitMiddleware 创建限流中间件实例
func NewRateLimitMiddleware(redis *redis.Client) *RateLimitMiddleware {
	return &RateLimitMiddleware{
		redis: redis,
	}
}

// RateLimit 通用限流中间件
func (m *RateLimitMiddleware) RateLimit(config RateLimitConfig) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 1. 链路追踪
		ctx, span := tracing.StartSpanWithComponent(c.Request.Context(), "middleware", "RateLimit")
		defer span.End()
		c.Request = c.Request.WithContext(ctx)

		// 2. 获取trace信息
		traceID := tracing.GetTraceID(c)

		// 3. 生成限流键
		key := config.KeyFunc(c)
		if key == "" {
			// 如果无法生成键，跳过限流
			c.Next()
			return
		}

		// 4. 添加span属性
		opentelemetry.AddAttribute(span, "method", c.Request.Method)
		opentelemetry.AddAttribute(span, "path", c.Request.URL.Path)
		opentelemetry.AddAttribute(span, "rate_limit_key", key)
		opentelemetry.AddAttribute(span, "rate_limit", config.Limit)
		opentelemetry.AddAttribute(span, "rate_window", config.Window.String())

		// 5. 检查限流
		allowed, remaining, resetTime, err := m.checkRateLimit(ctx, key, config.Limit, config.Window)
		if err != nil {
			log.Error().
				Str("trace_id", traceID).
				Str("rate_limit_key", key).
				Err(err).
				Msg("限流检查失败")

			opentelemetry.AddError(span, err)
			// 限流检查失败时，允许请求通过（降级处理）
			c.Next()
			return
		}

		// 6. 设置响应头
		c.Header("X-RateLimit-Limit", strconv.Itoa(config.Limit))
		c.Header("X-RateLimit-Remaining", strconv.Itoa(remaining))
		c.Header("X-RateLimit-Reset", strconv.FormatInt(resetTime, 10))

		// 7. 检查是否超出限制
		if !allowed {
			log.Warn().
				Str("trace_id", traceID).
				Str("rate_limit_key", key).
				Int("limit", config.Limit).
				Int("remaining", remaining).
				Msg("请求超出限流限制")

			opentelemetry.AddAttribute(span, "rate_limit_exceeded", true)
			opentelemetry.AddError(span, errors.NewRateLimitError("请求过于频繁"))

			message := config.Message
			if message == "" {
				message = "请求过于频繁，请稍后再试"
			}

			c.JSON(http.StatusTooManyRequests, globalTypes.GlobalResponse{
				Code: errors.RATE_LIMIT_EXCEEDED,
				Msg:  message,
			})
			c.Abort()
			return
		}

		// 8. 记录限流检查通过日志
		log.Debug().
			Str("trace_id", traceID).
			Str("rate_limit_key", key).
			Int("remaining", remaining).
			Msg("限流检查通过")

		// 9. 继续处理请求
		c.Next()
	}
}

// IPRateLimit IP限流中间件
func (m *RateLimitMiddleware) IPRateLimit(limit int, window time.Duration) gin.HandlerFunc {
	config := RateLimitConfig{
		Limit:  limit,
		Window: window,
		KeyFunc: func(c *gin.Context) string {
			return fmt.Sprintf("rate_limit:ip:%s", c.ClientIP())
		},
		Message: "IP请求过于频繁，请稍后再试",
	}
	return m.RateLimit(config)
}

// UserRateLimit 用户限流中间件
func (m *RateLimitMiddleware) UserRateLimit(limit int, window time.Duration) gin.HandlerFunc {
	config := RateLimitConfig{
		Limit:  limit,
		Window: window,
		KeyFunc: func(c *gin.Context) string {
			userKSUID := ksuid.GetKSUID(c)
			if userKSUID == "" {
				// 未认证用户使用IP限流
				return fmt.Sprintf("rate_limit:ip:%s", c.ClientIP())
			}
			return fmt.Sprintf("rate_limit:user:%s", userKSUID)
		},
		Message: "用户请求过于频繁，请稍后再试",
	}
	return m.RateLimit(config)
}

// APIRateLimit API限流中间件
func (m *RateLimitMiddleware) APIRateLimit(limit int, window time.Duration) gin.HandlerFunc {
	config := RateLimitConfig{
		Limit:  limit,
		Window: window,
		KeyFunc: func(c *gin.Context) string {
			userKSUID := ksuid.GetKSUID(c)
			path := c.Request.URL.Path
			if userKSUID == "" {
				return fmt.Sprintf("rate_limit:api:ip:%s:%s", c.ClientIP(), path)
			}
			return fmt.Sprintf("rate_limit:api:user:%s:%s", userKSUID, path)
		},
		Message: "API请求过于频繁，请稍后再试",
	}
	return m.RateLimit(config)
}

// BatchOperationRateLimit 批量操作限流中间件
func (m *RateLimitMiddleware) BatchOperationRateLimit(limit int, window time.Duration) gin.HandlerFunc {
	config := RateLimitConfig{
		Limit:  limit,
		Window: window,
		KeyFunc: func(c *gin.Context) string {
			userKSUID := ksuid.GetKSUID(c)
			if userKSUID == "" {
				return fmt.Sprintf("rate_limit:batch:ip:%s", c.ClientIP())
			}
			return fmt.Sprintf("rate_limit:batch:user:%s", userKSUID)
		},
		Message: "批量操作过于频繁，请稍后再试",
	}
	return m.RateLimit(config)
}

// checkRateLimit 检查限流状态
func (m *RateLimitMiddleware) checkRateLimit(ctx gin.Context, key string, limit int, window time.Duration) (allowed bool, remaining int, resetTime int64, err error) {
	// 使用滑动窗口计数器算法
	now := time.Now()
	windowStart := now.Add(-window)
	
	// Redis Lua脚本实现原子性操作
	luaScript := `
		local key = KEYS[1]
		local window_start = ARGV[1]
		local now = ARGV[2]
		local limit = tonumber(ARGV[3])
		local ttl = tonumber(ARGV[4])
		
		-- 清理过期的记录
		redis.call('ZREMRANGEBYSCORE', key, 0, window_start)
		
		-- 获取当前窗口内的请求数
		local current = redis.call('ZCARD', key)
		
		if current < limit then
			-- 添加当前请求
			redis.call('ZADD', key, now, now)
			redis.call('EXPIRE', key, ttl)
			return {1, limit - current - 1, now + ttl * 1000}
		else
			-- 超出限制
			return {0, 0, now + ttl * 1000}
		end
	`
	
	windowStartMs := windowStart.UnixMilli()
	nowMs := now.UnixMilli()
	ttlSeconds := int(window.Seconds())
	
	result, err := m.redis.Eval(ctx, luaScript, []string{key}, windowStartMs, nowMs, limit, ttlSeconds).Result()
	if err != nil {
		return false, 0, 0, fmt.Errorf("rate limit check failed: %w", err)
	}
	
	resultSlice, ok := result.([]interface{})
	if !ok || len(resultSlice) != 3 {
		return false, 0, 0, fmt.Errorf("unexpected rate limit result format")
	}
	
	allowedInt, _ := resultSlice[0].(int64)
	remainingInt, _ := resultSlice[1].(int64)
	resetTimeInt, _ := resultSlice[2].(int64)
	
	return allowedInt == 1, int(remainingInt), resetTimeInt, nil
}

// GetRateLimitStatus 获取限流状态（用于监控）
func (m *RateLimitMiddleware) GetRateLimitStatus(ctx gin.Context, key string, window time.Duration) (current int, err error) {
	windowStart := time.Now().Add(-window)
	windowStartMs := windowStart.UnixMilli()
	
	// 清理过期记录并获取当前计数
	luaScript := `
		local key = KEYS[1]
		local window_start = ARGV[1]
		
		redis.call('ZREMRANGEBYSCORE', key, 0, window_start)
		return redis.call('ZCARD', key)
	`
	
	result, err := m.redis.Eval(ctx, luaScript, []string{key}, windowStartMs).Result()
	if err != nil {
		return 0, fmt.Errorf("get rate limit status failed: %w", err)
	}
	
	currentInt, ok := result.(int64)
	if !ok {
		return 0, fmt.Errorf("unexpected rate limit status result format")
	}
	
	return int(currentInt), nil
}
