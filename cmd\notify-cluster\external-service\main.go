// 运行以下命令生成 admin-intra Swagger 文档:
// swag init --parseDependency --parseInternal -g cmd/admin-cluster/admin-intra/main.go -o docs/api/admin-cluster/admin-intra --packageName admin_service --exclude intra/user-cluster,intra/content-cluster

// @title 管理员服务API
// @version 1.0
// @description 管理员服务提供管理员账号登录、退出以及基础的用户管理功能
// @termsOfService http://swagger.io/terms/

// @contact.name API支持
// @contact.url http://www.swagger.io/support
// @contact.email <EMAIL>

// @license.name Apache 2.0
// @license.url http://www.apache.org/licenses/LICENSE-2.0.html

// @host localhost:17001
// @BasePath /api
// @schemes http https

package main

import (
	"context"
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/robfig/cron/v3"
	"github.com/rs/zerolog"
	"github.com/rs/zerolog/log"
	"go.uber.org/fx"
	"gorm.io/gorm"
	"os"
	cron2 "pxpat-backend/internal/notify-cluster/external-service/cron"
	"pxpat-backend/internal/notify-cluster/external-service/model"
	"pxpat-backend/internal/notify-cluster/external-service/routes"
	"pxpat-backend/internal/notify-cluster/external-service/types"
	configLoader "pxpat-backend/pkg/config"
	"pxpat-backend/pkg/consul"
	"pxpat-backend/pkg/consul/health"
	DBLoader "pxpat-backend/pkg/database"
)

// 提供配置
func provideConfig() *types.Config {
	clusterName := "notify"
	serviceName := "external"

	return configLoader.Load[types.Config](configLoader.LoaderConfig{
		TypeName:    configLoader.Service,
		ClusterName: clusterName,
		ServiceName: serviceName,
		UseRedis:    false,
	})
}

// 提供日志器
func provideLogger() {
	// 配置日志
	zerolog.TimeFieldFormat = zerolog.TimeFormatUnix
	log.Logger = log.Output(zerolog.ConsoleWriter{Out: os.Stderr})
}

// 提供数据库连接
func provideDatabase(cfg *types.Config) (*gorm.DB, error) {
	clusterName := "notify"
	serviceName := "external"

	// 连接数据库
	connDB, err := DBLoader.ConnectDB(cfg.Database)
	if err != nil {
		log.Fatal().Err(err).Msg(fmt.Sprintf("%s-%s 连接数据库失败\n", clusterName, serviceName))
		return nil, err
	}

	// 注册数据库模型
	err = connDB.AutoMigrate(
		// 管理员相关模型
		&model.NotifyExternalModel{},
	)
	if err != nil {
		log.Error().Err(err).Msg("Failed to auto migrate database")
		log.Fatal().Err(err).Msg("Database migration failed")
		return nil, err
	}

	return connDB, nil
}

// 提供定时任务管理器
func provideCronManager(cfg *types.Config, db *gorm.DB) *cron.Cron {
	cronObj := cron2.NewNotifyExternalCron(db, cfg)
	// 启动定时任务
	// TODO 数量上来后改用kafka等消息队列
	cronManager := cron.New(cron.WithSeconds())
	_, err := cronManager.AddFunc("@every 10s", cronObj.Handler)
	if err != nil {
		log.Fatal().Err(err).Msg("定时任务启动失败")
		return nil
	}

	log.Info().Msg("定时任务管理器初始化完成")
	return cronManager
}

// 提供Consul管理器
func provideConsulManager(cfg *types.Config) (*consul.Manager, error) {
	consulManager, err := consul.NewManager(&cfg.Consul)
	if err != nil {
		log.Fatal().Err(err).Msg("Consul管理器初始化失败")
		return nil, err
	}
	return consulManager, nil
}

// 提供健康检查处理器
func provideHealthHandler(consulManager *consul.Manager, db *gorm.DB) *consul.HealthHandler {
	healthHandler := consul.NewHealthHandler(consulManager, "1.0.0")

	// 添加数据库健康检查
	healthHandler.AddChecker(health.NewDatabaseHealthChecker("database", func() error {
		sqlDB, err := db.DB()
		if err != nil {
			return err
		}
		return sqlDB.Ping()
	}))

	return healthHandler
}

// 提供Gin引擎
func provideGinEngine(cfg *types.Config, db *gorm.DB, healthHandler *consul.HealthHandler) *gin.Engine {
	// 创建HTTP路由器
	router := gin.Default()
	routes.InitRouter(router, db, cfg)

	// 注册健康检查路由
	healthHandler.RegisterHealthRoutes(router)

	return router
}

// 应用生命周期管理
func manageLifecycle(
	lc fx.Lifecycle,
	cfg *types.Config,
	consulManager *consul.Manager,
	cronManager *cron.Cron,
	ginEngine *gin.Engine,
) {
	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) error {
			// 启动Consul管理器
			if err := consulManager.Start(ctx); err != nil {
				log.Fatal().Err(err).Msg("Consul管理器启动失败")
				return err
			}
			log.Info().Msg("Consul管理器启动成功")

			// 启动定时任务
			cronManager.Start()
			log.Info().Msg("定时任务启动成功")

			// 启动HTTP服务器（在goroutine中）
			go func() {
				httpAddr := fmt.Sprintf(":%d", cfg.Server.Port)
				log.Info().Msgf("Starting HTTP server on %s", httpAddr)

				if err := ginEngine.Run(httpAddr); err != nil {
					log.Fatal().Err(err).Msg("Failed to start HTTP server")
				}
			}()

			return nil
		},
		OnStop: func(ctx context.Context) error {
			// 停止定时任务
			cronManager.Stop()
			log.Info().Msg("定时任务停止成功")

			// 停止Consul管理器
			if err := consulManager.Stop(); err != nil {
				log.Error().Err(err).Msg("停止Consul管理器失败")
			} else {
				log.Info().Msg("Consul管理器停止成功")
			}

			log.Info().Msg("Server exiting")
			return nil
		},
	})
}

func main() {
	app := fx.New(
		// 提供依赖
		fx.Provide(
			provideConfig,
			provideDatabase,
			provideCronManager,
			provideConsulManager,
			provideHealthHandler,
			provideGinEngine,
		),
		// 调用生命周期管理
		fx.Invoke(
			provideLogger,
			manageLifecycle,
		),
	)

	// 运行应用
	app.Run()
}
