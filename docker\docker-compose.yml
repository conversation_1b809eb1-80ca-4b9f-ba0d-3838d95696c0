# 内容管理服务 Docker Compose 配置
version: '3.8'

services:
  # 内容管理服务
  content-management-service:
    build:
      context: ..
      dockerfile: docker/Dockerfile.content-management
    container_name: content-management-service
    restart: unless-stopped
    ports:
      - "12010:12010"
    environment:
      # 服务配置
      - GO_ENV=production
      - GIN_MODE=release
      
      # 数据库配置
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_USER=content_user
      - DB_PASSWORD=content_password
      - DB_NAME=content_management
      - DB_SSL_MODE=disable
      
      # Redis配置
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=redis_password
      - REDIS_DB=0
      
      # JWT配置
      - JWT_SECRET_KEY=your-super-secret-jwt-key-change-in-production
      - JWT_TOKEN_DURATION=1h
      - JWT_REFRESH_DURATION=24h
      
      # 外部服务配置
      - VIDEO_SERVICE_URL=http://video-service:12001
      - INTERACTION_SERVICE_URL=http://interaction-service:12002
      
      # Consul配置
      - CONSUL_HOST=consul
      - CONSUL_PORT=8500
      - CONSUL_DATACENTER=dc1
      
      # 日志配置
      - LOG_LEVEL=info
      - LOG_FORMAT=json
    volumes:
      - ./logs:/app/logs
      - ./config:/app/config:ro
    depends_on:
      - postgres
      - redis
      - consul
    networks:
      - content-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:12010/api/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # PostgreSQL 数据库
  postgres:
    image: postgres:15-alpine
    container_name: content-postgres
    restart: unless-stopped
    environment:
      - POSTGRES_DB=content_management
      - POSTGRES_USER=content_user
      - POSTGRES_PASSWORD=content_password
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-scripts:/docker-entrypoint-initdb.d:ro
    ports:
      - "5432:5432"
    networks:
      - content-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U content_user -d content_management"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis 缓存
  redis:
    image: redis:7-alpine
    container_name: content-redis
    restart: unless-stopped
    command: redis-server --requirepass redis_password --appendonly yes
    volumes:
      - redis_data:/data
      - ./redis.conf:/usr/local/etc/redis/redis.conf:ro
    ports:
      - "6379:6379"
    networks:
      - content-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5

  # Consul 服务发现
  consul:
    image: consul:1.16
    container_name: content-consul
    restart: unless-stopped
    command: agent -server -bootstrap-expect=1 -ui -client=0.0.0.0 -bind=0.0.0.0
    environment:
      - CONSUL_DATACENTER=dc1
      - CONSUL_ENCRYPT=your-consul-encrypt-key
    volumes:
      - consul_data:/consul/data
      - ./consul.json:/consul/config/consul.json:ro
    ports:
      - "8500:8500"
      - "8600:8600/udp"
    networks:
      - content-network
    healthcheck:
      test: ["CMD", "consul", "members"]
      interval: 10s
      timeout: 3s
      retries: 5

  # Prometheus 监控
  prometheus:
    image: prom/prometheus:v2.45.0
    container_name: content-prometheus
    restart: unless-stopped
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    ports:
      - "9090:9090"
    networks:
      - content-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:9090/"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Grafana 可视化
  grafana:
    image: grafana/grafana:10.0.0
    container_name: content-grafana
    restart: unless-stopped
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=admin123
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./grafana/provisioning:/etc/grafana/provisioning:ro
      - ./grafana/dashboards:/var/lib/grafana/dashboards:ro
    ports:
      - "3000:3000"
    depends_on:
      - prometheus
    networks:
      - content-network
    healthcheck:
      test: ["CMD-SHELL", "wget --no-verbose --tries=1 --spider http://localhost:3000/api/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Jaeger 链路追踪
  jaeger:
    image: jaegertracing/all-in-one:1.47
    container_name: content-jaeger
    restart: unless-stopped
    environment:
      - COLLECTOR_OTLP_ENABLED=true
    ports:
      - "16686:16686"
      - "14268:14268"
    networks:
      - content-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:16686/"]
      interval: 30s
      timeout: 10s
      retries: 3

# 网络配置
networks:
  content-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# 数据卷配置
volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  consul_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
