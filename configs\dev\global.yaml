# 全局服务器配置
server:
  host: "0.0.0.0"
  mode: "debug"
  read_timeout_seconds: 30s
  write_timeout_seconds: 30s
  idle_timeout_seconds: 120s
  shutdown_timeout_seconds: 120s
  all_cluster_list:
    notify_cluster:
      host: "localhost"
      port: 10200
  all_service_list:
    # 用户组
    audit_service:
      host: "localhost"
      port: 16001
      timeout: 10s
    user_service:
      host: "localhost"
      port: 11001
      timeout: 10s
    # 内容组
    video_service:
      host: "localhost"
      port: 12001
      timeout: 10s
    interaction_service:
      host: "localhost"
      port: 12009
      timeout: 10s
    # 通知组
    notify_external_service:
      host: "localhost"
      port: 10211
      timeout: 10s
    # 财政组
    points_service:
      host: "localhost"
      port: 15001
      timeout: 10s
    token_service:
      host: "testbackend.pxpac.com"
      port: 443
      timeout: 10s
    # 存储组
    content_storage_service:
      host: "localhost"
      port: 13001
      timeout: 10s
    media_process_service:
      host: "localhost"
      port: 13002
      timeout: 10s
    user_storage_service:
      host: "localhost"
      port: 13004
      timeout: 10s

jwt:
  secret: your-secret-key
  expiration: 168h # 7天

database:
  host: localhost
  port: 5432
  user: postgres
  password: postgres
  dbname: pxpat
  ssl_mode: disable
  max_open_conns: 10
  max_idle_conns: 5
  conn_max_lifetime_minutes: 60m
  auto_migrate: true
  time_zone: "Asia/Shanghai"
  log_level: "debug" # "info", "warn", "error", "silent"

redis:
  host: localhost
  port: 6379
  password: ""
  db: -1
  pool_size: 10
  min_idle_conns: 5
  dial_timeout_seconds: 5s
  read_timeout_seconds: 3s
  write_timeout_seconds: 3s
  pool_timeout_seconds: 4s

log:
  level: "debug"
  format: "json"
  output: "both"
  max_size: 100
  max_backups: 5
  max_age: 30
  compress: false
  # FilePath 自己添加
  service_file_path: "logs/service/{{serviceName}}-service.log"
  postgres_file_path: "logs/postgres/{{serviceName}}-postgres.log"

security:
  cors:
    enabled: true
    allowed_origins: "*"
    allowed_methods: "GET, POST, PUT, DELETE, OPTIONS"
    allowed_headers: "Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization, accept, origin, Cache-Control, X-Requested-With"
    allowed_credentials: "true"

  tracing:
    enabled: true
    service_name: "help-service"
    service_version: "1.0.0"
    exporter_type: "otlp-http"  # 使用HTTP方式发送到OTLP收集器
    endpoint: "localhost:4318"  # OTLP HTTP端点（4318是HTTP端口，4317是gRPC端口）
    sampling_ratio: 1.0  # 开发环境100%采样
    timeout: 10s
    insecure: true

  rate_limit:
    enabled: true
    requests_per_minute: 100
    burst: 200

# 全局MinIO配置
storage:
  minio:
    provider: "minio"
    endpoint: "localdev:9000"
    access_key_id: "minioadmin"
    secret_access_key: "minioadmin"
    use_ssl: false
    region: "us-east-1"

# 全局消息队列配置
rabbitmq:
  url: "amqp://admin:admin123@localdev:5672/"