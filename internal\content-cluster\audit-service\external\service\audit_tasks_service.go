package service

import (
	"context"
	"fmt"
	"gorm.io/gorm"
	"pxpat-backend/internal/content-cluster/audit-service/client"
	"pxpat-backend/internal/content-cluster/audit-service/dto"
	publisher "pxpat-backend/internal/content-cluster/audit-service/messaging/publisher"
	"pxpat-backend/internal/content-cluster/audit-service/model"
	"pxpat-backend/internal/content-cluster/audit-service/repository"
	"pxpat-backend/pkg/errors"

	"github.com/rs/zerolog/log"
	"go.opentelemetry.io/otel/trace"
)

type ExternalAuditTasksService struct {
	auditTasksRepository *repository.AuditTasksRepository
	contentServiceClient client.ContentServiceClient
	userServiceClient    client.UserServiceClient
	tokenServiceClient   client.TokenServiceClient
	mqPublisher          *publisher.Publisher
	db                   *gorm.DB
}

func NewExternalAuditTasksService(auditTasksRepository *repository.AuditTasksRepository, contentServiceClient client.ContentServiceClient, userServiceClient client.UserServiceClient, tokenServiceClient client.TokenServiceClient, mqPublisher *publisher.Publisher, db *gorm.DB) *ExternalAuditTasksService {
	return &ExternalAuditTasksService{
		auditTasksRepository: auditTasksRepository,
		contentServiceClient: contentServiceClient,
		userServiceClient:    userServiceClient,
		tokenServiceClient:   tokenServiceClient,
		mqPublisher:          mqPublisher,
		db:                   db,
	}
}

// getTraceInfoFromContext 从context中获取trace信息
func getTraceInfoFromContext(ctx context.Context) (traceID, spanID string) {
	spanCtx := trace.SpanContextFromContext(ctx)
	if spanCtx.IsValid() {
		traceID = spanCtx.TraceID().String()
		spanID = spanCtx.SpanID().String()
	}
	return
}

// buildAuditorLogsWithUserInfo 构建包含用户信息的审核员记录列表
func (s *ExternalAuditTasksService) buildAuditorLogsWithUserInfo(ctx context.Context, auditLogs []model.AuditUserLogs) []dto.AuditorLogItem {
	traceID, spanID := getTraceInfoFromContext(ctx)

	// 收集所有需要获取用户信息的KSUID
	userKSUIDs := make([]string, 0)
	userKSUIDSet := make(map[string]bool) // 用于去重
	for _, auditLog := range auditLogs {
		if auditLog.UserKSUID != "" && !userKSUIDSet[auditLog.UserKSUID] {
			userKSUIDs = append(userKSUIDs, auditLog.UserKSUID)
			userKSUIDSet[auditLog.UserKSUID] = true
		}
	}

	// 批量获取用户信息
	var userInfoMap map[string]*dto.UserInfo
	if len(userKSUIDs) > 0 {
		userInfoMap = make(map[string]*dto.UserInfo)

		userResponse, err := s.userServiceClient.BatchGetUsers(userKSUIDs)
		if err != nil {
			log.Error().
				Str("trace_id", traceID).
				Str("span_id", spanID).
				Err(err).
				Interface("user_ksuids", userKSUIDs).
				Msg("批量获取用户信息失败")
			// 不返回错误，继续处理，只是用户信息为空
		} else {
			// 构建用户信息映射
			for _, user := range userResponse.Users {
				userInfoMap[user.UserKSUID] = (*dto.UserInfo)(&user)
			}
		}
	}

	// 构建审核员记录列表，包含用户信息
	auditorLogs := make([]dto.AuditorLogItem, 0)
	for _, auditLog := range auditLogs {
		auditorLogItem := dto.AuditorLogItem{
			ID:         auditLog.ID,
			UserKSUID:  auditLog.UserKSUID,
			VoteType:   auditLog.VoteType,
			VoteWeight: auditLog.VoteWeight,
			Reason:     auditLog.Reason,
			CreatedAt:  auditLog.CreatedAt.Format("2006-01-02 15:04:05"),
		}

		// 添加用户信息
		if userInfo, exists := userInfoMap[auditLog.UserKSUID]; exists {
			auditorLogItem.UserInfo = userInfo
		}

		auditorLogs = append(auditorLogs, auditorLogItem)
	}

	return auditorLogs
}

func (s *ExternalAuditTasksService) CreateAuditTask(ctx context.Context, req *dto.AuditTasksRequest, needVotes int) *errors.Errors {
	// 获取trace信息
	traceID, spanID := getTraceInfoFromContext(ctx)

	log.Info().
		Str("trace_id", traceID).
		Str("span_id", spanID).
		Str("user_ksuid", req.UserKSUID).
		Str("content_ksuid", req.ContentKSUID).
		Int("need_votes", needVotes).
		Msg("Creating external audit task")

	// 1. 通过客户端获取内容详情
	content, err := s.contentServiceClient.GetContentByKSUIDForService(req.ContentKSUID)
	if err != nil {
		log.Error().
			Str("trace_id", traceID).
			Str("span_id", spanID).
			Err(err).
			Str("user_ksuid", req.UserKSUID).
			Str("content_ksuid", req.ContentKSUID).
			Msg("Failed to get content details for audit task creation")
		return errors.NewGlobalErrors(errors.SYSTEM_ERROR, errors.SEND_INTERNAL_HTTP_ERROR, err)
	}

	log.Debug().
		Str("trace_id", traceID).
		Str("span_id", spanID).
		Str("content_ksuid", req.ContentKSUID).
		Str("content_status", string(content.Status)).
		Str("content_user_ksuid", content.UserKSUID).
		Bool("is_transcode_audit_video", content.IsTranscodeAuditVideo).
		Msg("Content details retrieved for audit task creation")

	if content.Status != "draft" {
		log.Warn().
			Str("trace_id", traceID).
			Str("span_id", spanID).
			Str("content_ksuid", req.ContentKSUID).
			Str("current_status", string(content.Status)).
			Msg("Content is not in draft status, cannot create audit task")
		return errors.NewGlobalErrors(errors.CONTENT_STATUS_ERROR, errors.CONTENT_STATUS_ERROR, errors.NoneError)
	}

	// 2. 验证ks_uid是否一致
	if content.UserKSUID != req.UserKSUID {
		log.Warn().
			Str("trace_id", traceID).
			Str("span_id", spanID).
			Str("request_user_ksuid", req.UserKSUID).
			Str("content_user_ksuid", content.UserKSUID).
			Str("content_ksuid", req.ContentKSUID).
			Msg("Content creator mismatch, permission denied")
		return errors.NewGlobalErrors(errors.PERMISSION_DENIED, errors.PERMISSION_DENIED,
			fmt.Errorf("content creator mismatch: expected %s, got %s", req.UserKSUID, content.UserKSUID))
	}

	// 3. 检查IsTranscodeAuditVideo字段，只有为true时才创建审核任务
	if !content.IsTranscodeAuditVideo {
		return errors.NewGlobalErrors(errors.CONTENT_STATUS_ERROR, errors.CONTENT_STATUS_ERROR,
			fmt.Errorf("content has not been transcoded for audit, cannot create audit task"))
	}

	// 4. 更新内容状态为audit
	err = s.contentServiceClient.UpdateContentStatus(req.ContentKSUID, dto.StatusAudit)
	if err != nil {
		return errors.NewGlobalErrors(errors.SYSTEM_ERROR, errors.SEND_INTERNAL_HTTP_ERROR, err)
	}

	// 5. 创建审核任务，明确设置content_type为video
	err = s.auditTasksRepository.CreateAuditTask(ctx, &model.AuditTasks{
		CreatorKSUID: req.UserKSUID,
		ContentKSUID: req.ContentKSUID,
		FileID:       content.FileKSUID,
		ContentType:  "video", // 明确设置为video
		NeedVotes:    needVotes,
	})
	if err != nil {
		return errors.NewGlobalErrors(errors.SYSTEM_ERROR, errors.CREATE_RECORD_ERROR, err)
	}

	return nil
}

// VoteAuditTask 审核投票功能
func (s *ExternalAuditTasksService) VoteAuditTask(ctx context.Context, req *dto.AuditVoteRequest) (*dto.AuditVoteResponse, *errors.Errors) {
	// 获取trace信息
	traceID, spanID := getTraceInfoFromContext(ctx)

	// TODO: 在这里添加返回票数的代码，暂时写死每个人1票
	voteWeight := 8

	log.Info().
		Str("trace_id", traceID).
		Str("span_id", spanID).
		Str("user_ksuid", req.UserKSUID).
		Uint64("task_id", req.TaskID).
		Str("vote_type", req.VoteType).
		Int("vote_weight", voteWeight).
		Msg("开始处理审核投票")

	// 1. 先检查用户是否已经投过票（在事务外检查，提高性能）
	hasVoted, err := s.auditTasksRepository.CheckUserAlreadyVoted(ctx, req.TaskID, req.UserKSUID)
	if err != nil {
		log.Error().
			Str("trace_id", traceID).
			Str("span_id", spanID).
			Err(err).
			Str("user_ksuid", req.UserKSUID).
			Uint64("task_id", req.TaskID).
			Msg("检查用户投票状态失败")
		return nil, errors.NewGlobalErrors(errors.SYSTEM_ERROR, errors.GET_RECORD_ERROR, err)
	}
	if hasVoted {
		log.Warn().
			Str("trace_id", traceID).
			Str("span_id", spanID).
			Str("user_ksuid", req.UserKSUID).
			Uint64("task_id", req.TaskID).
			Msg("用户已经投过票")
		return nil, errors.NewGlobalErrors(errors.ALREADY_VOTED, errors.ALREADY_VOTED,
			fmt.Errorf("user has already voted for this task"))
	}

	// 2. 使用事务确保数据一致性
	var result *dto.AuditVoteResponse
	err = s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 2.1 获取审核任务（带行锁）
		task, err := s.auditTasksRepository.GetAuditTaskByID(ctx, tx, req.TaskID)
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return errors.NewGlobalErrors(errors.DATA_NOT_FOUND, errors.DATA_NOT_FOUND,
					fmt.Errorf("no such audit task: %w", err))
			}
			return err
		}

		// 2.2 检查任务状态
		if task.Status != "pending" {
			return fmt.Errorf("audit task is not in pending status")
		}

		// 2.3 创建审核日志记录
		auditLog := &model.AuditUserLogs{
			AuditTaskID: req.TaskID,
			UserKSUID:   req.UserKSUID,
			VoteType:    req.VoteType,
			Level:       req.Level,
			Reason:      req.Reason, // 可以后续扩展为必填字段
			VoteWeight:  voteWeight,
		}

		err = s.auditTasksRepository.CreateAuditUserLog(ctx, auditLog)
		if err != nil {
			return fmt.Errorf("failed to create audit log: %w", err)
		}

		// 2.4 更新投票数据
		task.Votes += voteWeight

		switch req.VoteType {
		case "permit":
			task.PermitNum += voteWeight
		case "reject":
			task.RejectNum += voteWeight
		default:
			return fmt.Errorf("invalid vote type: %s", req.VoteType)
		}

		// 2.5 判断是否达到投票要求
		if task.Votes >= task.NeedVotes {
			// 计算通过率
			permitRate := float64(task.PermitNum) / float64(task.NeedVotes)

			// 获取内容详情用于构建完整的审核事件
			content, err := s.contentServiceClient.GetContentByKSUIDForService(task.ContentKSUID)
			if err != nil {
				log.Error().
					Str("trace_id", traceID).
					Str("span_id", spanID).
					Err(err).
					Str("content_ksuid", task.ContentKSUID).
					Msg("获取内容详情失败，无法构建完整的审核事件")
				return fmt.Errorf("failed to get content details for audit event: %w", err)
			}

			// 构建审核员日志信息用于metadata
			auditorLogs, err := s.auditTasksRepository.GetAuditLogsByTaskID(ctx, task.ID)
			if err != nil {
				log.Error().
					Str("trace_id", traceID).
					Str("span_id", spanID).
					Err(err).
					Uint64("task_id", task.ID).
					Msg("获取审核员日志失败")
				// 不返回错误，继续处理
				auditorLogs = []model.AuditUserLogs{}
			}

			// 构建metadata
			metadata := map[string]interface{}{
				"content_ksuid":    task.ContentKSUID,
				"user_ksuid":       task.CreatorKSUID,
				"video_ID":         content.VideoID,
				"duration":         content.Duration,
				"language":         content.Language,
				"orientation":      content.Orientation,
				"rating":           string(content.Rating),
				"content_source":   string(content.SourceType),
				"upload_timestamp": content.CreatedAt.Format("2006-01-02T15:04:05Z"),
				"upload_source":    "pxpac_chain_script",
				"audit_info": map[string]interface{}{
					"status":     "permit",
					"votes":      task.Votes,
					"permit_num": task.PermitNum,
					"reject_num": task.RejectNum,
					"auditor_logs": func() []map[string]interface{} {
						logs := make([]map[string]interface{}, len(auditorLogs))
						for i, log := range auditorLogs {
							logs[i] = map[string]interface{}{
								"user_ksuid":  log.UserKSUID,
								"vote_type":   log.VoteType,
								"vote_weight": log.VoteWeight,
								"reason":      log.Reason,
								"created_at":  log.CreatedAt.Format("2006-01-02T15:04:05Z"),
							}
						}
						return logs
					}(),
				},
			}

			// 如果有协作者信息，添加到metadata
			if content.OriginalCollaborators != "" {
				metadata["collaborators"] = content.OriginalCollaborators
			}

			auditOKEvent := &dto.AuditOKEvent{
				ContentKSUID: task.ContentKSUID,
				AuditTaskID:  task.ID,
				UserKSUID:    task.CreatorKSUID,
				FileKSUID:    task.FileID,
				ContentType:  task.ContentType,
				Level:        task.Level, // 设置审核级别
				TotalVotes:   task.Votes,
				PermitRate:   permitRate,
				PermitVotes:  task.PermitNum,
				RejectVotes:  task.RejectNum,
				// 新增字段
				Title:       content.Title,
				Description: content.Description,
				FileData:    "", // TODO: 需要从文件服务获取文件数据的base64编码
				FileName:    fmt.Sprintf("pxpac_video_%s.json", task.ContentKSUID),
				Metadata:    metadata,
			}

			if permitRate >= 0.6 { // 60%通过率
				// 审核通过
				task.Status = "permit"

				level, err := s.auditTasksRepository.GetAuditLogsByTaskIDGroupByLevel(ctx, task.ID)
				if err != nil {
					return err
				}
				task.Level = level

				// 发送审核通过事件
				err = s.mqPublisher.PublishAuditOKEvent(ctx, auditOKEvent)
				if err != nil {
					log.Error().
						Str("trace_id", traceID).
						Str("span_id", spanID).
						Err(err).
						Str("content_ksuid", task.ContentKSUID).
						Uint64("task_id", task.ID).
						Msg("发送审核通过事件失败")
					return fmt.Errorf("failed to publish audit permitted event: %w", err)
				}

				log.Info().
					Str("trace_id", traceID).
					Str("span_id", spanID).
					Str("content_ksuid", task.ContentKSUID).
					Uint64("task_id", task.ID).
					Float64("permit_rate", permitRate).
					Msg("审核通过事件发送成功，内容服务将处理后续逻辑")

				// 调用token-service的real-upload接口
				if s.tokenServiceClient != nil {
					realUploadReq := &client.RealUploadRequest{
						ContentKSUID: auditOKEvent.ContentKSUID,
						CreatorKSUID: auditOKEvent.UserKSUID,
						Title:        auditOKEvent.Title,
						Description:  auditOKEvent.Description,
						ContentType:  auditOKEvent.ContentType,
						FileData:     auditOKEvent.FileData,
						FileName:     auditOKEvent.FileName,
						Metadata:     auditOKEvent.Metadata,
					}

					realUploadResp, err := s.tokenServiceClient.RealUpload(realUploadReq)
					if err != nil {
						log.Error().
							Str("trace_id", traceID).
							Str("span_id", spanID).
							Err(err).
							Str("content_ksuid", task.ContentKSUID).
							Uint64("task_id", task.ID).
							Msg("调用token-service real-upload接口失败")
						// 不返回错误，因为审核状态已经更新成功，上链失败可以后续重试
					} else {
						log.Info().
							Str("trace_id", traceID).
							Str("span_id", spanID).
							Str("content_ksuid", task.ContentKSUID).
							Str("on_chain_id", realUploadResp.OnChainID).
							Str("tx_hash", realUploadResp.TxHash).
							Msg("token-service real-upload调用成功，内容已上链")
					}
				} else {
					log.Warn().
						Str("trace_id", traceID).
						Str("span_id", spanID).
						Str("content_ksuid", task.ContentKSUID).
						Msg("token-service客户端未配置，跳过real-upload调用")
				}

				// 发送转码请求到媒体处理服务，标记为审核后转码，完成后自动发布
				if err := s.sendTranscodingRequest(ctx, task); err != nil {
					log.Error().
						Str("trace_id", traceID).
						Str("span_id", spanID).
						Err(err).
						Str("content_ksuid", task.ContentKSUID).
						Uint64("task_id", task.ID).
						Msg("发送转码请求失败")
					// 不返回错误，因为审核状态已经更新成功，转码失败可以后续重试
				}
			} else {
				// 发送审核拒绝事件
				task.Status = "reject"
				err := s.mqPublisher.PublishAuditOKEvent(ctx, auditOKEvent)
				if err != nil {
					return fmt.Errorf("failed to update content status to rejected: %w", err)
				}
			}
		}

		// 2.6 保存更新后的任务
		err = s.auditTasksRepository.UpdateAuditTaskVotes(ctx, tx, task)
		if err != nil {
			return err
		}

		// 2.7 构建响应
		result = &dto.AuditVoteResponse{
			TaskID:      task.ID,
			Status:      task.Status,
			Votes:       task.Votes,
			PermitNum:   task.PermitNum,
			RejectNum:   task.RejectNum,
			NeedVotes:   task.NeedVotes,
			IsCompleted: task.Status != "pending",
		}

		return nil
	})

	switch {
	case errors.Is(err, gorm.ErrRecordNotFound):
		return nil, errors.NewGlobalErrors(errors.SYSTEM_ERROR, errors.GET_RECORD_ERROR, err)
	case err != nil:
		if err.Error() == "audit task is not in pending status" {
			return nil, errors.NewGlobalErrors(errors.AUDIT_TASK_STATUS_NOT_IS_PENDING, errors.AUDIT_TASK_STATUS_NOT_IS_PENDING, err)
		}
		return nil, errors.NewGlobalErrors(errors.SYSTEM_ERROR, errors.UPDATE_RECORD_ERROR, err)
	}

	return result, nil
}

// GetRandomAuditTask 随机获取指定类型的待审核任务
func (s *ExternalAuditTasksService) GetRandomAuditTask(ctx context.Context, req *dto.GetRandomAuditTaskRequest) (*dto.GetRandomAuditTaskResponse, *errors.Errors) {
	// 1. 随机获取一个用户未审核过的指定类型待审核任务
	task, err := s.auditTasksRepository.GetRandomPendingTaskByTypeExcludingUser(ctx, req.ContentType, req.UserKSUID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.NewGlobalErrors(errors.NO_AVAILABLE_TASK, errors.NO_AVAILABLE_TASK,
				fmt.Errorf("no available %s audit task for user", req.ContentType))
		}
		return nil, errors.NewGlobalErrors(errors.SYSTEM_ERROR, errors.GET_RECORD_ERROR, err)
	}

	// 2. 构建包含用户信息的审核员记录
	auditorLogs := s.buildAuditorLogsWithUserInfo(ctx, task.AuditorLogs)

	// 3. 构建响应
	response := &dto.GetRandomAuditTaskResponse{
		TaskID:       task.ID,
		ContentKSUID: task.ContentKSUID,
		ContentType:  task.ContentType,
		Round:        task.Round,
		Votes:        task.Votes,
		PermitNum:    task.PermitNum,
		RejectNum:    task.RejectNum,
		NeedVotes:    task.NeedVotes,
		CreatedAt:    task.CreatedAt.Format("2006-01-02 15:04:05"),
		AuditorLogs:  auditorLogs,
	}

	return response, nil
}

// GetPendingAuditTasks 获取用户未审核的待审核任务列表
func (s *ExternalAuditTasksService) GetPendingAuditTasks(ctx context.Context, KSUID string, contentType string, page, pageSize int) (*dto.GetPendingAuditTasksResponse, *errors.Errors) {
	// 1. 计算偏移量
	offset := (page - 1) * pageSize

	// 2. 获取待审核任务列表和总数
	tasks, total, err := s.auditTasksRepository.GetPendingAuditTasksExcludingUser(ctx, KSUID, contentType, offset, pageSize)
	if err != nil {
		return nil, errors.NewGlobalErrors(errors.SYSTEM_ERROR, errors.GET_RECORD_ERROR, err)
	}

	// 3. 构建任务列表响应
	var taskItems []dto.PendingAuditTaskItem
	for _, task := range tasks {
		// 构建包含用户信息的审核员记录
		auditorLogs := s.buildAuditorLogsWithUserInfo(ctx, task.AuditorLogs)

		taskItem := dto.PendingAuditTaskItem{
			TaskID:       task.ID,
			ContentKSUID: task.ContentKSUID,
			ContentType:  task.ContentType,
			Round:        task.Round,
			Votes:        task.Votes,
			PermitNum:    task.PermitNum,
			RejectNum:    task.RejectNum,
			NeedVotes:    task.NeedVotes,
			CreatedAt:    task.CreatedAt.Format("2006-01-02 15:04:05"),
			AuditorLogs:  auditorLogs,
		}
		taskItems = append(taskItems, taskItem)
	}

	// 4. 计算总页数
	totalPages := int(total) / pageSize
	if int(total)%pageSize > 0 {
		totalPages++
	}

	// 5. 构建响应
	response := &dto.GetPendingAuditTasksResponse{
		Tasks:      taskItems,
		Total:      total,
		Page:       page,
		PageSize:   pageSize,
		TotalPages: totalPages,
	}

	return response, nil
}

// sendTranscodingRequest 发送转码请求到媒体处理服务
func (s *ExternalAuditTasksService) sendTranscodingRequest(ctx context.Context, task *model.AuditTasks) error {
	// 如果没有配置MQ发布器，跳过
	if s.mqPublisher == nil || !s.mqPublisher.IsConnected() {
		log.Warn().Msg("MQ发布器未配置或未连接，跳过转码请求")
		return nil
	}

	// 构建转码请求事件
	event := &dto.TranscodingRequestEvent{

		UserKSUID:        task.CreatorKSUID,
		ContentKSUID:     task.ContentKSUID,
		FileKSUID:        task.FileID,
		Ext:              ".mp4", // 默认视频扩展名，后续可以从存储服务获取
		IsAuditTransCode: false,
		AutoPushAudit:    false, // 审核服务发起的转码不需要自动推送到审核
		AutoPublish:      true,  // 审核通过后的转码完成时自动发布
		ContentType:      task.ContentType,
		Resolutions:      []string{"1080p", "720p", "480p", "360p"}, // 支持多分辨率转码
	}

	// 发送转码请求
	if err := s.mqPublisher.PublishTranscodingRequest(ctx, event); err != nil {
		return fmt.Errorf("发布转码请求失败: %w", err)
	}

	log.Info().
		Str("content_ksuid", event.ContentKSUID).
		Str("file_ksuid", event.FileKSUID).
		Interface("resolutions", event.Resolutions).
		Msg("审核服务转码请求已发送")

	return nil
}

// GetAuditTaskByID 根据ID获取审核任务详情
func (s *ExternalAuditTasksService) GetAuditTaskByID(ctx context.Context, taskID uint64) (*dto.GetAuditTaskByIDResponse, *errors.Errors) {
	// 获取trace信息
	traceID, spanID := getTraceInfoFromContext(ctx)

	log.Info().
		Str("trace_id", traceID).
		Str("span_id", spanID).
		Uint64("task_id", taskID).
		Msg("Getting audit task by ID")

	// 1. 根据ID获取审核任务
	task, err := s.auditTasksRepository.GetAuditTaskByIDWithoutTx(ctx, taskID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.NewGlobalErrors(errors.DATA_NOT_FOUND, errors.DATA_NOT_FOUND,
				fmt.Errorf("audit task not found with id: %d", taskID))
		}
		return nil, errors.NewGlobalErrors(errors.SYSTEM_ERROR, errors.GET_RECORD_ERROR, err)
	}

	// 2. 构建包含用户信息的审核员记录
	auditorLogs := s.buildAuditorLogsWithUserInfo(ctx, task.AuditorLogs)

	// 3. 构建响应
	response := &dto.GetAuditTaskByIDResponse{
		TaskID:      task.ID,
		VideoKSUID:  task.ContentKSUID,
		ContentType: task.ContentType,
		Status:      task.Status,
		Round:       task.Round,
		Votes:       task.Votes,
		PermitNum:   task.PermitNum,
		RejectNum:   task.RejectNum,
		NeedVotes:   task.NeedVotes,
		CreatedAt:   task.CreatedAt.Format("2006-01-02 15:04:05"),
		UpdatedAt:   task.UpdatedAt.Format("2006-01-02 15:04:05"),
		AuditorLogs: auditorLogs,
	}

	log.Info().
		Str("trace_id", traceID).
		Str("span_id", spanID).
		Uint64("task_id", taskID).
		Str("status", task.Status).
		Int("votes", task.Votes).
		Msg("Successfully retrieved audit task by ID")

	return response, nil
}
