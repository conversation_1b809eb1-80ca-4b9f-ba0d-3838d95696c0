# Alertmanager配置文件
# 定义告警路由和通知方式

global:
  # SMTP配置
  smtp_smarthost: 'localhost:587'
  smtp_from: '<EMAIL>'
  smtp_auth_username: '<EMAIL>'
  smtp_auth_password: 'your-email-password'
  
  # Slack配置
  slack_api_url: 'https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK'

# 模板配置
templates:
  - '/etc/alertmanager/templates/*.tmpl'

# 路由配置
route:
  group_by: ['alertname', 'cluster', 'service']
  group_wait: 10s
  group_interval: 10s
  repeat_interval: 1h
  receiver: 'default-receiver'
  
  routes:
    # 关键服务告警 - 立即通知
    - match:
        severity: critical
      receiver: 'critical-alerts'
      group_wait: 0s
      repeat_interval: 5m
      continue: true
    
    # 内容管理服务告警
    - match:
        service: content-management-service
      receiver: 'content-service-alerts'
      group_by: ['alertname', 'instance']
      continue: true
    
    # 数据库告警
    - match:
        service: postgres
      receiver: 'database-alerts'
      group_by: ['alertname', 'instance']
      continue: true
    
    # Redis告警
    - match:
        service: redis
      receiver: 'redis-alerts'
      group_by: ['alertname', 'instance']
      continue: true
    
    # 系统资源告警
    - match:
        service: system
      receiver: 'system-alerts'
      group_by: ['alertname', 'instance']
      continue: true
    
    # 业务指标告警
    - match_re:
        alertname: '(HighCacheMissRate|HighBatchOperationFailureRate|QueueSizeHigh)'
      receiver: 'business-alerts'
      group_by: ['alertname', 'service']

# 接收器配置
receivers:
  # 默认接收器
  - name: 'default-receiver'
    email_configs:
      - to: '<EMAIL>'
        subject: '[PXPAT] {{ .GroupLabels.alertname }} - {{ .Status | toUpper }}'
        body: |
          {{ range .Alerts }}
          告警: {{ .Annotations.summary }}
          描述: {{ .Annotations.description }}
          标签: {{ range .Labels.SortedPairs }}{{ .Name }}={{ .Value }} {{ end }}
          时间: {{ .StartsAt.Format "2006-01-02 15:04:05" }}
          {{ end }}

  # 关键告警接收器
  - name: 'critical-alerts'
    slack_configs:
      - api_url: 'https://hooks.slack.com/services/YOUR/CRITICAL/WEBHOOK'
        channel: '#critical-alerts'
        title: '🚨 关键告警 - {{ .GroupLabels.alertname }}'
        text: |
          {{ range .Alerts }}
          *告警*: {{ .Annotations.summary }}
          *描述*: {{ .Annotations.description }}
          *服务*: {{ .Labels.service }}
          *实例*: {{ .Labels.instance }}
          *时间*: {{ .StartsAt.Format "2006-01-02 15:04:05" }}
          {{ end }}
        send_resolved: true
    
    email_configs:
      - to: '<EMAIL>'
        subject: '🚨 [CRITICAL] {{ .GroupLabels.alertname }}'
        body: |
          关键告警触发！
          
          {{ range .Alerts }}
          告警: {{ .Annotations.summary }}
          描述: {{ .Annotations.description }}
          服务: {{ .Labels.service }}
          实例: {{ .Labels.instance }}
          严重程度: {{ .Labels.severity }}
          时间: {{ .StartsAt.Format "2006-01-02 15:04:05" }}
          
          {{ end }}
    
    # 短信通知（需要配置SMS网关）
    webhook_configs:
      - url: 'http://sms-gateway:8080/send'
        send_resolved: false
        http_config:
          basic_auth:
            username: 'alertmanager'
            password: 'sms-gateway-password'

  # 内容服务告警接收器
  - name: 'content-service-alerts'
    slack_configs:
      - api_url: 'https://hooks.slack.com/services/YOUR/CONTENT/WEBHOOK'
        channel: '#content-service'
        title: '⚠️ 内容管理服务告警 - {{ .GroupLabels.alertname }}'
        text: |
          {{ range .Alerts }}
          *告警*: {{ .Annotations.summary }}
          *描述*: {{ .Annotations.description }}
          *实例*: {{ .Labels.instance }}
          *时间*: {{ .StartsAt.Format "2006-01-02 15:04:05" }}
          {{ end }}
        send_resolved: true

  # 数据库告警接收器
  - name: 'database-alerts'
    slack_configs:
      - api_url: 'https://hooks.slack.com/services/YOUR/DATABASE/WEBHOOK'
        channel: '#database-alerts'
        title: '🗄️ 数据库告警 - {{ .GroupLabels.alertname }}'
        text: |
          {{ range .Alerts }}
          *告警*: {{ .Annotations.summary }}
          *描述*: {{ .Annotations.description }}
          *实例*: {{ .Labels.instance }}
          *时间*: {{ .StartsAt.Format "2006-01-02 15:04:05" }}
          {{ end }}
        send_resolved: true

  # Redis告警接收器
  - name: 'redis-alerts'
    slack_configs:
      - api_url: 'https://hooks.slack.com/services/YOUR/REDIS/WEBHOOK'
        channel: '#redis-alerts'
        title: '🔴 Redis告警 - {{ .GroupLabels.alertname }}'
        text: |
          {{ range .Alerts }}
          *告警*: {{ .Annotations.summary }}
          *描述*: {{ .Annotations.description }}
          *实例*: {{ .Labels.instance }}
          *时间*: {{ .StartsAt.Format "2006-01-02 15:04:05" }}
          {{ end }}
        send_resolved: true

  # 系统告警接收器
  - name: 'system-alerts'
    slack_configs:
      - api_url: 'https://hooks.slack.com/services/YOUR/SYSTEM/WEBHOOK'
        channel: '#system-alerts'
        title: '💻 系统告警 - {{ .GroupLabels.alertname }}'
        text: |
          {{ range .Alerts }}
          *告警*: {{ .Annotations.summary }}
          *描述*: {{ .Annotations.description }}
          *节点*: {{ .Labels.instance }}
          *时间*: {{ .StartsAt.Format "2006-01-02 15:04:05" }}
          {{ end }}
        send_resolved: true

  # 业务指标告警接收器
  - name: 'business-alerts'
    slack_configs:
      - api_url: 'https://hooks.slack.com/services/YOUR/BUSINESS/WEBHOOK'
        channel: '#business-metrics'
        title: '📊 业务指标告警 - {{ .GroupLabels.alertname }}'
        text: |
          {{ range .Alerts }}
          *告警*: {{ .Annotations.summary }}
          *描述*: {{ .Annotations.description }}
          *服务*: {{ .Labels.service }}
          *时间*: {{ .StartsAt.Format "2006-01-02 15:04:05" }}
          {{ end }}
        send_resolved: true

# 抑制规则
inhibit_rules:
  # 如果服务完全down，抑制其他相关告警
  - source_match:
      alertname: 'ServiceDown'
    target_match_re:
      alertname: '(HighErrorRate|HighResponseTime|HighCPUUsage|HighMemoryUsage)'
    equal: ['instance']
  
  # 如果数据库down，抑制连接相关告警
  - source_match:
      alertname: 'DatabaseDown'
    target_match:
      alertname: 'DatabaseConnectionsHigh'
    equal: ['instance']
  
  # 如果Redis down，抑制内存和连接告警
  - source_match:
      alertname: 'RedisDown'
    target_match_re:
      alertname: '(RedisMemoryHigh|RedisConnectionsHigh)'
    equal: ['instance']
