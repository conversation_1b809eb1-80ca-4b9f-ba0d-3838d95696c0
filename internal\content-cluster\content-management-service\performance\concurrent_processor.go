package performance

import (
	"context"
	"fmt"
	"runtime"
	"sync"
	"time"

	"github.com/rs/zerolog/log"
)

// ConcurrentProcessor 并发处理器
type ConcurrentProcessor struct {
	maxWorkers    int
	maxQueueSize  int
	workerPool    chan chan Job
	jobQueue      chan Job
	workers       []*Worker
	quit          chan bool
	wg            sync.WaitGroup
	
	// 性能监控
	metrics *ProcessorMetrics
	mu      sync.RWMutex
}

// Job 任务接口
type Job interface {
	Process(ctx context.Context) error
	GetID() string
	GetPriority() int
}

// Worker 工作者
type Worker struct {
	id         int
	workerPool chan chan Job
	jobChannel chan Job
	quit       chan bool
	processor  *ConcurrentProcessor
}

// ProcessorMetrics 处理器性能指标
type ProcessorMetrics struct {
	TotalJobs      int64
	CompletedJobs  int64
	FailedJobs     int64
	ActiveWorkers  int
	QueueLength    int
	AvgProcessTime time.Duration
	MaxProcessTime time.Duration
	
	// 按优先级统计
	PriorityStats map[int]*PriorityStats
	
	mu sync.RWMutex
}

// PriorityStats 优先级统计
type PriorityStats struct {
	JobCount       int64
	CompletedCount int64
	FailedCount    int64
	AvgTime        time.Duration
}

// BatchJob 批量任务
type BatchJob struct {
	ID       string
	Priority int
	Items    []interface{}
	Processor func(ctx context.Context, item interface{}) error
}

// ContentProcessJob 内容处理任务
type ContentProcessJob struct {
	ID          string
	Priority    int
	ContentKSUID string
	Operation   string
	Parameters  map[string]interface{}
	Callback    func(result interface{}, err error)
}

// NewConcurrentProcessor 创建并发处理器
func NewConcurrentProcessor(maxWorkers, maxQueueSize int) *ConcurrentProcessor {
	if maxWorkers <= 0 {
		maxWorkers = runtime.NumCPU()
	}
	
	if maxQueueSize <= 0 {
		maxQueueSize = 1000
	}
	
	processor := &ConcurrentProcessor{
		maxWorkers:   maxWorkers,
		maxQueueSize: maxQueueSize,
		workerPool:   make(chan chan Job, maxWorkers),
		jobQueue:     make(chan Job, maxQueueSize),
		workers:      make([]*Worker, maxWorkers),
		quit:         make(chan bool),
		metrics: &ProcessorMetrics{
			PriorityStats: make(map[int]*PriorityStats),
		},
	}
	
	// 启动工作者
	processor.startWorkers()
	
	// 启动调度器
	go processor.startDispatcher()
	
	// 启动性能监控
	go processor.startMetricsCollector()
	
	return processor
}

// Submit 提交任务
func (cp *ConcurrentProcessor) Submit(job Job) error {
	select {
	case cp.jobQueue <- job:
		cp.recordJobSubmitted(job)
		return nil
	default:
		return fmt.Errorf("任务队列已满")
	}
}

// SubmitBatch 提交批量任务
func (cp *ConcurrentProcessor) SubmitBatch(jobs []Job) error {
	for _, job := range jobs {
		if err := cp.Submit(job); err != nil {
			return fmt.Errorf("提交批量任务失败: %w", err)
		}
	}
	return nil
}

// ProcessBatch 批量处理
func (cp *ConcurrentProcessor) ProcessBatch(ctx context.Context, items []interface{}, processor func(ctx context.Context, item interface{}) error) error {
	batchSize := len(items)
	if batchSize == 0 {
		return nil
	}
	
	// 计算最优的并发数
	concurrency := cp.calculateOptimalConcurrency(batchSize)
	
	// 创建工作通道
	itemChan := make(chan interface{}, batchSize)
	errorChan := make(chan error, batchSize)
	
	// 发送任务到通道
	go func() {
		defer close(itemChan)
		for _, item := range items {
			itemChan <- item
		}
	}()
	
	// 启动工作协程
	var wg sync.WaitGroup
	for i := 0; i < concurrency; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			for item := range itemChan {
				if err := processor(ctx, item); err != nil {
					errorChan <- err
				}
			}
		}()
	}
	
	// 等待所有任务完成
	go func() {
		wg.Wait()
		close(errorChan)
	}()
	
	// 收集错误
	var errors []error
	for err := range errorChan {
		errors = append(errors, err)
	}
	
	if len(errors) > 0 {
		return fmt.Errorf("批量处理失败，错误数量: %d", len(errors))
	}
	
	return nil
}

// ProcessConcurrently 并发处理函数
func (cp *ConcurrentProcessor) ProcessConcurrently(ctx context.Context, tasks []func() error, maxConcurrency int) error {
	if len(tasks) == 0 {
		return nil
	}
	
	if maxConcurrency <= 0 {
		maxConcurrency = cp.maxWorkers
	}
	
	// 限制并发数
	semaphore := make(chan struct{}, maxConcurrency)
	errorChan := make(chan error, len(tasks))
	
	var wg sync.WaitGroup
	
	for _, task := range tasks {
		wg.Add(1)
		go func(t func() error) {
			defer wg.Done()
			
			// 获取信号量
			semaphore <- struct{}{}
			defer func() { <-semaphore }()
			
			if err := t(); err != nil {
				errorChan <- err
			}
		}(task)
	}
	
	// 等待所有任务完成
	go func() {
		wg.Wait()
		close(errorChan)
	}()
	
	// 收集错误
	var errors []error
	for err := range errorChan {
		errors = append(errors, err)
	}
	
	if len(errors) > 0 {
		return fmt.Errorf("并发处理失败，错误数量: %d", len(errors))
	}
	
	return nil
}

// startWorkers 启动工作者
func (cp *ConcurrentProcessor) startWorkers() {
	for i := 0; i < cp.maxWorkers; i++ {
		worker := &Worker{
			id:         i,
			workerPool: cp.workerPool,
			jobChannel: make(chan Job),
			quit:       make(chan bool),
			processor:  cp,
		}
		
		cp.workers[i] = worker
		worker.start()
	}
}

// startDispatcher 启动调度器
func (cp *ConcurrentProcessor) startDispatcher() {
	for {
		select {
		case job := <-cp.jobQueue:
			// 获取可用的工作者
			go func() {
				jobChannel := <-cp.workerPool
				jobChannel <- job
			}()
		case <-cp.quit:
			return
		}
	}
}

// startMetricsCollector 启动性能指标收集器
func (cp *ConcurrentProcessor) startMetricsCollector() {
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()
	
	for {
		select {
		case <-ticker.C:
			cp.logMetrics()
		case <-cp.quit:
			return
		}
	}
}

// calculateOptimalConcurrency 计算最优并发数
func (cp *ConcurrentProcessor) calculateOptimalConcurrency(taskCount int) int {
	// 基于任务数量和可用工作者数量计算最优并发数
	if taskCount <= cp.maxWorkers {
		return taskCount
	}
	
	// 使用CPU核心数作为基准
	cpuCount := runtime.NumCPU()
	if taskCount < cpuCount*2 {
		return cpuCount
	}
	
	return cp.maxWorkers
}

// recordJobSubmitted 记录任务提交
func (cp *ConcurrentProcessor) recordJobSubmitted(job Job) {
	cp.metrics.mu.Lock()
	defer cp.metrics.mu.Unlock()
	
	cp.metrics.TotalJobs++
	cp.metrics.QueueLength = len(cp.jobQueue)
	
	priority := job.GetPriority()
	if _, exists := cp.metrics.PriorityStats[priority]; !exists {
		cp.metrics.PriorityStats[priority] = &PriorityStats{}
	}
	cp.metrics.PriorityStats[priority].JobCount++
}

// recordJobCompleted 记录任务完成
func (cp *ConcurrentProcessor) recordJobCompleted(job Job, duration time.Duration, err error) {
	cp.metrics.mu.Lock()
	defer cp.metrics.mu.Unlock()
	
	priority := job.GetPriority()
	stats := cp.metrics.PriorityStats[priority]
	
	if err != nil {
		cp.metrics.FailedJobs++
		stats.FailedCount++
	} else {
		cp.metrics.CompletedJobs++
		stats.CompletedCount++
	}
	
	// 更新平均处理时间
	if cp.metrics.CompletedJobs == 1 {
		cp.metrics.AvgProcessTime = duration
		cp.metrics.MaxProcessTime = duration
		stats.AvgTime = duration
	} else {
		cp.metrics.AvgProcessTime = (cp.metrics.AvgProcessTime + duration) / 2
		stats.AvgTime = (stats.AvgTime + duration) / 2
		
		if duration > cp.metrics.MaxProcessTime {
			cp.metrics.MaxProcessTime = duration
		}
	}
	
	cp.metrics.QueueLength = len(cp.jobQueue)
}

// logMetrics 记录性能指标日志
func (cp *ConcurrentProcessor) logMetrics() {
	cp.metrics.mu.RLock()
	defer cp.metrics.mu.RUnlock()
	
	log.Info().
		Int64("total_jobs", cp.metrics.TotalJobs).
		Int64("completed_jobs", cp.metrics.CompletedJobs).
		Int64("failed_jobs", cp.metrics.FailedJobs).
		Int("active_workers", cp.metrics.ActiveWorkers).
		Int("queue_length", cp.metrics.QueueLength).
		Dur("avg_process_time", cp.metrics.AvgProcessTime).
		Dur("max_process_time", cp.metrics.MaxProcessTime).
		Msg("并发处理器性能指标")
}

// Stop 停止处理器
func (cp *ConcurrentProcessor) Stop() {
	close(cp.quit)
	
	// 停止所有工作者
	for _, worker := range cp.workers {
		worker.stop()
	}
	
	cp.wg.Wait()
}

// GetMetrics 获取性能指标
func (cp *ConcurrentProcessor) GetMetrics() *ProcessorMetrics {
	cp.metrics.mu.RLock()
	defer cp.metrics.mu.RUnlock()
	
	// 深拷贝指标数据
	metrics := &ProcessorMetrics{
		TotalJobs:      cp.metrics.TotalJobs,
		CompletedJobs:  cp.metrics.CompletedJobs,
		FailedJobs:     cp.metrics.FailedJobs,
		ActiveWorkers:  cp.metrics.ActiveWorkers,
		QueueLength:    cp.metrics.QueueLength,
		AvgProcessTime: cp.metrics.AvgProcessTime,
		MaxProcessTime: cp.metrics.MaxProcessTime,
		PriorityStats:  make(map[int]*PriorityStats),
	}
	
	for priority, stats := range cp.metrics.PriorityStats {
		metrics.PriorityStats[priority] = &PriorityStats{
			JobCount:       stats.JobCount,
			CompletedCount: stats.CompletedCount,
			FailedCount:    stats.FailedCount,
			AvgTime:        stats.AvgTime,
		}
	}
	
	return metrics
}

// Worker方法实现

// start 启动工作者
func (w *Worker) start() {
	go func() {
		w.processor.wg.Add(1)
		defer w.processor.wg.Done()
		
		for {
			// 将工作者注册到工作者池
			w.workerPool <- w.jobChannel
			
			select {
			case job := <-w.jobChannel:
				// 处理任务
				w.processJob(job)
			case <-w.quit:
				return
			}
		}
	}()
}

// processJob 处理任务
func (w *Worker) processJob(job Job) {
	startTime := time.Now()
	
	// 更新活跃工作者数量
	w.processor.metrics.mu.Lock()
	w.processor.metrics.ActiveWorkers++
	w.processor.metrics.mu.Unlock()
	
	defer func() {
		w.processor.metrics.mu.Lock()
		w.processor.metrics.ActiveWorkers--
		w.processor.metrics.mu.Unlock()
	}()
	
	// 执行任务
	ctx := context.Background()
	err := job.Process(ctx)
	
	duration := time.Since(startTime)
	
	// 记录任务完成
	w.processor.recordJobCompleted(job, duration, err)
	
	if err != nil {
		log.Error().
			Err(err).
			Str("job_id", job.GetID()).
			Int("worker_id", w.id).
			Dur("duration", duration).
			Msg("任务处理失败")
	} else {
		log.Debug().
			Str("job_id", job.GetID()).
			Int("worker_id", w.id).
			Dur("duration", duration).
			Msg("任务处理完成")
	}
}

// stop 停止工作者
func (w *Worker) stop() {
	close(w.quit)
}

// Job接口实现

// Process 实现BatchJob的Process方法
func (bj *BatchJob) Process(ctx context.Context) error {
	for _, item := range bj.Items {
		if err := bj.Processor(ctx, item); err != nil {
			return fmt.Errorf("批量任务项处理失败: %w", err)
		}
	}
	return nil
}

// GetID 实现BatchJob的GetID方法
func (bj *BatchJob) GetID() string {
	return bj.ID
}

// GetPriority 实现BatchJob的GetPriority方法
func (bj *BatchJob) GetPriority() int {
	return bj.Priority
}

// Process 实现ContentProcessJob的Process方法
func (cpj *ContentProcessJob) Process(ctx context.Context) error {
	// 这里应该实现具体的内容处理逻辑
	// 简化实现
	time.Sleep(100 * time.Millisecond) // 模拟处理时间
	
	if cpj.Callback != nil {
		cpj.Callback("处理完成", nil)
	}
	
	return nil
}

// GetID 实现ContentProcessJob的GetID方法
func (cpj *ContentProcessJob) GetID() string {
	return cpj.ID
}

// GetPriority 实现ContentProcessJob的GetPriority方法
func (cpj *ContentProcessJob) GetPriority() int {
	return cpj.Priority
}
