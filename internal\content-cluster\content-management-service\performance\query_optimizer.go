package performance

import (
	"context"
	"fmt"
	"strings"
	"sync"
	"time"

	"github.com/rs/zerolog/log"
	"gorm.io/gorm"

	"pxpat-backend/internal/content-cluster/content-management-service/types"
)

// QueryOptimizer 查询优化器
type QueryOptimizer struct {
	db *gorm.DB
	
	// 查询性能监控
	metrics *QueryMetrics
	mu      sync.RWMutex
	
	// 慢查询阈值
	slowQueryThreshold time.Duration
	
	// 查询缓存
	queryCache map[string]*QueryCacheEntry
	cacheMu    sync.RWMutex
}

// QueryMetrics 查询性能指标
type QueryMetrics struct {
	TotalQueries    int64
	SlowQueries     int64
	FailedQueries   int64
	AvgQueryTime    time.Duration
	MaxQueryTime    time.Duration
	MinQueryTime    time.Duration
	
	// 按表统计
	TableStats map[string]*TableQueryStats
	
	mu sync.RWMutex
}

// TableQueryStats 表查询统计
type TableQueryStats struct {
	QueryCount   int64
	AvgTime      time.Duration
	SlowCount    int64
	ErrorCount   int64
}

// QueryCacheEntry 查询缓存条目
type QueryCacheEntry struct {
	SQL       string
	Args      []interface{}
	Result    interface{}
	CreatedAt time.Time
	TTL       time.Duration
}

// NewQueryOptimizer 创建查询优化器
func NewQueryOptimizer(db *gorm.DB) *QueryOptimizer {
	optimizer := &QueryOptimizer{
		db:                 db,
		metrics:            &QueryMetrics{TableStats: make(map[string]*TableQueryStats)},
		slowQueryThreshold: 1 * time.Second,
		queryCache:         make(map[string]*QueryCacheEntry),
	}
	
	// 启动性能监控
	optimizer.startPerformanceMonitoring()
	
	return optimizer
}

// OptimizedQuery 优化的查询执行
func (qo *QueryOptimizer) OptimizedQuery(ctx context.Context, query string, args []interface{}, dest interface{}) error {
	startTime := time.Now()
	
	// 检查查询缓存
	if cached := qo.getCachedQuery(query, args); cached != nil {
		log.Debug().Str("query", query).Msg("使用查询缓存")
		return nil
	}
	
	// 执行查询
	err := qo.db.WithContext(ctx).Raw(query, args...).Scan(dest).Error
	
	duration := time.Since(startTime)
	
	// 记录性能指标
	qo.recordQueryMetrics(query, duration, err)
	
	// 如果查询成功且不是慢查询，缓存结果
	if err == nil && duration < qo.slowQueryThreshold {
		qo.cacheQuery(query, args, dest, 5*time.Minute)
	}
	
	return err
}

// OptimizedFind 优化的查找操作
func (qo *QueryOptimizer) OptimizedFind(ctx context.Context, model interface{}, conditions map[string]interface{}, options *QueryOptions) error {
	startTime := time.Now()
	
	db := qo.db.WithContext(ctx)
	
	// 应用查询条件
	for field, value := range conditions {
		db = db.Where(fmt.Sprintf("%s = ?", field), value)
	}
	
	// 应用查询选项
	if options != nil {
		if options.Select != "" {
			db = db.Select(options.Select)
		}
		if len(options.Preload) > 0 {
			for _, preload := range options.Preload {
				db = db.Preload(preload)
			}
		}
		if options.OrderBy != "" {
			db = db.Order(options.OrderBy)
		}
		if options.Limit > 0 {
			db = db.Limit(options.Limit)
		}
		if options.Offset > 0 {
			db = db.Offset(options.Offset)
		}
	}
	
	err := db.Find(model).Error
	duration := time.Since(startTime)
	
	// 记录性能指标
	tableName := qo.getTableName(model)
	qo.recordQueryMetrics(fmt.Sprintf("SELECT * FROM %s", tableName), duration, err)
	
	return err
}

// OptimizedCount 优化的计数操作
func (qo *QueryOptimizer) OptimizedCount(ctx context.Context, model interface{}, conditions map[string]interface{}) (int64, error) {
	startTime := time.Now()
	
	db := qo.db.WithContext(ctx).Model(model)
	
	// 应用查询条件
	for field, value := range conditions {
		db = db.Where(fmt.Sprintf("%s = ?", field), value)
	}
	
	var count int64
	err := db.Count(&count).Error
	
	duration := time.Since(startTime)
	
	// 记录性能指标
	tableName := qo.getTableName(model)
	qo.recordQueryMetrics(fmt.Sprintf("SELECT COUNT(*) FROM %s", tableName), duration, err)
	
	return count, err
}

// BatchInsert 批量插入优化
func (qo *QueryOptimizer) BatchInsert(ctx context.Context, models interface{}, batchSize int) error {
	startTime := time.Now()
	
	err := qo.db.WithContext(ctx).CreateInBatches(models, batchSize).Error
	
	duration := time.Since(startTime)
	qo.recordQueryMetrics("BATCH INSERT", duration, err)
	
	return err
}

// BatchUpdate 批量更新优化
func (qo *QueryOptimizer) BatchUpdate(ctx context.Context, model interface{}, conditions map[string]interface{}, updates map[string]interface{}) error {
	startTime := time.Now()
	
	db := qo.db.WithContext(ctx).Model(model)
	
	// 应用条件
	for field, value := range conditions {
		db = db.Where(fmt.Sprintf("%s = ?", field), value)
	}
	
	err := db.Updates(updates).Error
	
	duration := time.Since(startTime)
	tableName := qo.getTableName(model)
	qo.recordQueryMetrics(fmt.Sprintf("UPDATE %s", tableName), duration, err)
	
	return err
}

// OptimizeIndexes 索引优化建议
func (qo *QueryOptimizer) OptimizeIndexes(ctx context.Context) ([]IndexSuggestion, error) {
	suggestions := make([]IndexSuggestion, 0)
	
	// 分析慢查询
	slowQueries := qo.getSlowQueries()
	
	for _, query := range slowQueries {
		if suggestion := qo.analyzeQueryForIndex(query); suggestion != nil {
			suggestions = append(suggestions, *suggestion)
		}
	}
	
	return suggestions, nil
}

// QueryOptions 查询选项
type QueryOptions struct {
	Select  string
	Preload []string
	OrderBy string
	Limit   int
	Offset  int
}

// IndexSuggestion 索引建议
type IndexSuggestion struct {
	Table   string
	Columns []string
	Type    string // btree, hash, gin, etc.
	Reason  string
}

// SlowQuery 慢查询记录
type SlowQuery struct {
	SQL      string
	Duration time.Duration
	Count    int64
}

// getCachedQuery 获取缓存的查询结果
func (qo *QueryOptimizer) getCachedQuery(query string, args []interface{}) *QueryCacheEntry {
	qo.cacheMu.RLock()
	defer qo.cacheMu.RUnlock()
	
	key := qo.generateCacheKey(query, args)
	entry, exists := qo.queryCache[key]
	
	if !exists {
		return nil
	}
	
	// 检查是否过期
	if time.Since(entry.CreatedAt) > entry.TTL {
		delete(qo.queryCache, key)
		return nil
	}
	
	return entry
}

// cacheQuery 缓存查询结果
func (qo *QueryOptimizer) cacheQuery(query string, args []interface{}, result interface{}, ttl time.Duration) {
	qo.cacheMu.Lock()
	defer qo.cacheMu.Unlock()
	
	key := qo.generateCacheKey(query, args)
	qo.queryCache[key] = &QueryCacheEntry{
		SQL:       query,
		Args:      args,
		Result:    result,
		CreatedAt: time.Now(),
		TTL:       ttl,
	}
}

// generateCacheKey 生成缓存键
func (qo *QueryOptimizer) generateCacheKey(query string, args []interface{}) string {
	key := query
	for _, arg := range args {
		key += fmt.Sprintf("_%v", arg)
	}
	return key
}

// recordQueryMetrics 记录查询性能指标
func (qo *QueryOptimizer) recordQueryMetrics(query string, duration time.Duration, err error) {
	qo.metrics.mu.Lock()
	defer qo.metrics.mu.Unlock()
	
	qo.metrics.TotalQueries++
	
	if err != nil {
		qo.metrics.FailedQueries++
	}
	
	if duration > qo.slowQueryThreshold {
		qo.metrics.SlowQueries++
		log.Warn().
			Str("query", query).
			Dur("duration", duration).
			Msg("慢查询检测")
	}
	
	// 更新平均查询时间
	if qo.metrics.TotalQueries == 1 {
		qo.metrics.AvgQueryTime = duration
		qo.metrics.MaxQueryTime = duration
		qo.metrics.MinQueryTime = duration
	} else {
		qo.metrics.AvgQueryTime = (qo.metrics.AvgQueryTime + duration) / 2
		if duration > qo.metrics.MaxQueryTime {
			qo.metrics.MaxQueryTime = duration
		}
		if duration < qo.metrics.MinQueryTime {
			qo.metrics.MinQueryTime = duration
		}
	}
	
	// 更新表级统计
	tableName := qo.extractTableName(query)
	if tableName != "" {
		if _, exists := qo.metrics.TableStats[tableName]; !exists {
			qo.metrics.TableStats[tableName] = &TableQueryStats{}
		}
		
		stats := qo.metrics.TableStats[tableName]
		stats.QueryCount++
		stats.AvgTime = (stats.AvgTime + duration) / 2
		
		if duration > qo.slowQueryThreshold {
			stats.SlowCount++
		}
		
		if err != nil {
			stats.ErrorCount++
		}
	}
}

// getTableName 获取模型对应的表名
func (qo *QueryOptimizer) getTableName(model interface{}) string {
	stmt := &gorm.Statement{DB: qo.db}
	stmt.Parse(model)
	return stmt.Schema.Table
}

// extractTableName 从SQL中提取表名
func (qo *QueryOptimizer) extractTableName(query string) string {
	query = strings.ToUpper(strings.TrimSpace(query))
	
	if strings.HasPrefix(query, "SELECT") {
		// 简化的表名提取逻辑
		parts := strings.Split(query, " FROM ")
		if len(parts) > 1 {
			tablePart := strings.Split(parts[1], " ")[0]
			return strings.Trim(tablePart, "`\"[]")
		}
	} else if strings.HasPrefix(query, "INSERT INTO") {
		parts := strings.Split(query, "INSERT INTO ")
		if len(parts) > 1 {
			tablePart := strings.Split(parts[1], " ")[0]
			return strings.Trim(tablePart, "`\"[]")
		}
	} else if strings.HasPrefix(query, "UPDATE") {
		parts := strings.Split(query, "UPDATE ")
		if len(parts) > 1 {
			tablePart := strings.Split(parts[1], " ")[0]
			return strings.Trim(tablePart, "`\"[]")
		}
	}
	
	return ""
}

// getSlowQueries 获取慢查询列表
func (qo *QueryOptimizer) getSlowQueries() []SlowQuery {
	// 这里应该从性能监控数据中获取慢查询
	// 简化实现
	return []SlowQuery{}
}

// analyzeQueryForIndex 分析查询并提供索引建议
func (qo *QueryOptimizer) analyzeQueryForIndex(query SlowQuery) *IndexSuggestion {
	// 简化的索引分析逻辑
	// 实际实现应该解析SQL并分析WHERE条件、JOIN条件等
	return nil
}

// startPerformanceMonitoring 启动性能监控
func (qo *QueryOptimizer) startPerformanceMonitoring() {
	go func() {
		ticker := time.NewTicker(1 * time.Minute)
		defer ticker.Stop()
		
		for range ticker.C {
			qo.cleanupExpiredCache()
			qo.logPerformanceMetrics()
		}
	}()
}

// cleanupExpiredCache 清理过期缓存
func (qo *QueryOptimizer) cleanupExpiredCache() {
	qo.cacheMu.Lock()
	defer qo.cacheMu.Unlock()
	
	now := time.Now()
	for key, entry := range qo.queryCache {
		if now.Sub(entry.CreatedAt) > entry.TTL {
			delete(qo.queryCache, key)
		}
	}
}

// logPerformanceMetrics 记录性能指标日志
func (qo *QueryOptimizer) logPerformanceMetrics() {
	qo.metrics.mu.RLock()
	defer qo.metrics.mu.RUnlock()
	
	if qo.metrics.TotalQueries > 0 {
		log.Info().
			Int64("total_queries", qo.metrics.TotalQueries).
			Int64("slow_queries", qo.metrics.SlowQueries).
			Int64("failed_queries", qo.metrics.FailedQueries).
			Dur("avg_query_time", qo.metrics.AvgQueryTime).
			Dur("max_query_time", qo.metrics.MaxQueryTime).
			Float64("slow_query_rate", float64(qo.metrics.SlowQueries)/float64(qo.metrics.TotalQueries)).
			Msg("查询性能指标")
	}
}

// GetMetrics 获取查询性能指标
func (qo *QueryOptimizer) GetMetrics() *QueryMetrics {
	qo.metrics.mu.RLock()
	defer qo.metrics.mu.RUnlock()
	
	// 深拷贝指标数据
	metrics := &QueryMetrics{
		TotalQueries:  qo.metrics.TotalQueries,
		SlowQueries:   qo.metrics.SlowQueries,
		FailedQueries: qo.metrics.FailedQueries,
		AvgQueryTime:  qo.metrics.AvgQueryTime,
		MaxQueryTime:  qo.metrics.MaxQueryTime,
		MinQueryTime:  qo.metrics.MinQueryTime,
		TableStats:    make(map[string]*TableQueryStats),
	}
	
	for table, stats := range qo.metrics.TableStats {
		metrics.TableStats[table] = &TableQueryStats{
			QueryCount: stats.QueryCount,
			AvgTime:    stats.AvgTime,
			SlowCount:  stats.SlowCount,
			ErrorCount: stats.ErrorCount,
		}
	}
	
	return metrics
}
