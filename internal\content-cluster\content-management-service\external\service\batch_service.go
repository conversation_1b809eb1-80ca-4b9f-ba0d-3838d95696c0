package service

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/redis/go-redis/v9"
	"github.com/rs/zerolog/log"

	"pxpat-backend/internal/content-cluster/content-management-service/client"
	"pxpat-backend/internal/content-cluster/content-management-service/dto"
	"pxpat-backend/internal/content-cluster/content-management-service/repository"
	"pxpat-backend/internal/content-cluster/content-management-service/types"
	"pxpat-backend/internal/content-cluster/content-management-service/utils"
	"pxpat-backend/pkg/errors"
)

// BatchOperationService 批量操作服务接口
type BatchOperationService interface {
	// 批量状态操作
	BatchUpdateStatus(ctx context.Context, operatorKSUID string, request *dto.BatchUpdateStatusRequest) (*dto.BatchOperationResponse, *errors.Errors)
	BatchDelete(ctx context.Context, operatorKSUID string, request *dto.BatchDeleteRequest) (*dto.BatchOperationResponse, *errors.Errors)
	BatchPublish(ctx context.Context, operatorKSUID string, contentKSUIDs []string) (*dto.BatchOperationResponse, *errors.Errors)
	BatchArchive(ctx context.Context, operatorKSUID string, contentKSUIDs []string) (*dto.BatchOperationResponse, *errors.Errors)
	
	// 批量内容操作
	BatchUpdateCategory(ctx context.Context, operatorKSUID string, contentKSUIDs []string, categoryID uint) (*dto.BatchOperationResponse, *errors.Errors)
	BatchUpdateTags(ctx context.Context, operatorKSUID string, contentKSUIDs []string, tags []string) (*dto.BatchOperationResponse, *errors.Errors)
	BatchTransferOwnership(ctx context.Context, operatorKSUID string, contentKSUIDs []string, newOwnerKSUID string) (*dto.BatchOperationResponse, *errors.Errors)
	
	// 内容搜索
	SearchContents(ctx context.Context, request *dto.ContentSearchRequest) (*dto.ContentSearchResponse, *errors.Errors)
	
	// 操作日志管理
	GetOperationLogs(ctx context.Context, query *dto.OperationLogQueryRequest) (*dto.OperationLogListResponse, *errors.Errors)
	GetOperationLogStats(ctx context.Context, period string) (*dto.OperationLogStatsResponse, *errors.Errors)
	
	// 配置管理
	GetConfigs(ctx context.Context, query *dto.ConfigQueryRequest) (*dto.ConfigListResponse, *errors.Errors)
	UpdateConfig(ctx context.Context, operatorKSUID string, configKey string, request *dto.ConfigUpdateRequest) (*dto.ConfigResponse, *errors.Errors)
	GetConfigStats(ctx context.Context) (*dto.ConfigStatsResponse, *errors.Errors)
	
	// 系统管理
	GetSystemStats(ctx context.Context) (*dto.SystemStatsResponse, *errors.Errors)
	GetServiceHealth(ctx context.Context) ([]*dto.ServiceHealthResponse, *errors.Errors)
	
	// 健康检查
	HealthCheck(ctx context.Context) *errors.Errors
}

// batchOperationService 批量操作服务实现
type batchOperationService struct {
	// 服务客户端
	videoClient       client.VideoServiceClient
	novelClient       client.NovelServiceClient       // 预留
	musicClient       client.MusicServiceClient       // 预留
	interactionClient client.InteractionServiceClient
	
	// 数据访问层
	cacheRepo     repository.ContentCacheRepository
	operationRepo repository.OperationLogRepository
	configRepo    repository.ConfigRepository
	
	// 工具组件
	converter  utils.ContentConverter
	aggregator utils.DataAggregator
	validator  utils.ContentValidator
	
	// 基础设施
	redis  *redis.Client
	logger *log.Logger
}

// NewBatchOperationService 创建批量操作服务实例
func NewBatchOperationService(
	videoClient client.VideoServiceClient,
	interactionClient client.InteractionServiceClient,
	cacheRepo repository.ContentCacheRepository,
	operationRepo repository.OperationLogRepository,
	configRepo repository.ConfigRepository,
	converter utils.ContentConverter,
	aggregator utils.DataAggregator,
	validator utils.ContentValidator,
	redis *redis.Client,
) BatchOperationService {
	return &batchOperationService{
		videoClient:       videoClient,
		interactionClient: interactionClient,
		cacheRepo:         cacheRepo,
		operationRepo:     operationRepo,
		configRepo:        configRepo,
		converter:         converter,
		aggregator:        aggregator,
		validator:         validator,
		redis:            redis,
		logger:           &log.Logger,
	}
}

// BatchUpdateStatus 批量更新内容状态
func (s *batchOperationService) BatchUpdateStatus(ctx context.Context, operatorKSUID string, request *dto.BatchUpdateStatusRequest) (*dto.BatchOperationResponse, *errors.Errors) {
	log.Info().
		Str("operator_ksuid", operatorKSUID).
		Int("content_count", len(request.ContentKSUIDs)).
		Str("status", request.Status).
		Msg("开始批量更新内容状态")

	// 参数验证
	if operatorKSUID == "" {
		return nil, errors.NewValidationError("操作者KSUID不能为空")
	}
	if len(request.ContentKSUIDs) == 0 {
		return nil, errors.NewValidationError("内容KSUID列表不能为空")
	}
	if request.Status == "" {
		return nil, errors.NewValidationError("状态不能为空")
	}

	// 验证状态值
	if gErr := s.validator.ValidateContentStatus(request.Status); gErr != nil {
		return nil, gErr
	}

	// 限制批量操作数量
	if len(request.ContentKSUIDs) > 100 {
		return nil, errors.NewValidationError("批量操作数量不能超过100个")
	}

	// 按内容类型分组
	contentsByType, gErr := s.groupContentsByType(ctx, request.ContentKSUIDs)
	if gErr != nil {
		return nil, gErr
	}

	// 执行批量更新
	result := s.executeBatchStatusUpdate(ctx, contentsByType, request.Status)

	// 记录操作日志
	s.logBatchOperation(ctx, operatorKSUID, "batch_update_status", "content", 
		fmt.Sprintf("批量更新%d个内容状态为: %s", len(request.ContentKSUIDs), request.Status),
		request, result)

	// 清除相关缓存
	s.clearBatchContentCache(ctx, request.ContentKSUIDs)

	log.Info().
		Str("operator_ksuid", operatorKSUID).
		Int("success_count", result.SuccessCount).
		Int("failure_count", result.FailureCount).
		Msg("批量更新内容状态完成")

	return dto.FromBatchOperationResult(result), nil
}

// BatchDelete 批量删除内容
func (s *batchOperationService) BatchDelete(ctx context.Context, operatorKSUID string, request *dto.BatchDeleteRequest) (*dto.BatchOperationResponse, *errors.Errors) {
	log.Info().
		Str("operator_ksuid", operatorKSUID).
		Int("content_count", len(request.ContentKSUIDs)).
		Msg("开始批量删除内容")

	// 参数验证
	if operatorKSUID == "" {
		return nil, errors.NewValidationError("操作者KSUID不能为空")
	}
	if len(request.ContentKSUIDs) == 0 {
		return nil, errors.NewValidationError("内容KSUID列表不能为空")
	}

	// 限制批量操作数量
	if len(request.ContentKSUIDs) > 50 {
		return nil, errors.NewValidationError("批量删除数量不能超过50个")
	}

	// 按内容类型分组
	contentsByType, gErr := s.groupContentsByType(ctx, request.ContentKSUIDs)
	if gErr != nil {
		return nil, gErr
	}

	// 执行批量删除
	result := s.executeBatchDelete(ctx, contentsByType)

	// 记录操作日志
	s.logBatchOperation(ctx, operatorKSUID, "batch_delete", "content",
		fmt.Sprintf("批量删除%d个内容", len(request.ContentKSUIDs)),
		request, result)

	// 清除相关缓存
	s.clearBatchContentCache(ctx, request.ContentKSUIDs)

	log.Info().
		Str("operator_ksuid", operatorKSUID).
		Int("success_count", result.SuccessCount).
		Int("failure_count", result.FailureCount).
		Msg("批量删除内容完成")

	return dto.FromBatchOperationResult(result), nil
}

// BatchPublish 批量发布内容
func (s *batchOperationService) BatchPublish(ctx context.Context, operatorKSUID string, contentKSUIDs []string) (*dto.BatchOperationResponse, *errors.Errors) {
	log.Info().
		Str("operator_ksuid", operatorKSUID).
		Int("content_count", len(contentKSUIDs)).
		Msg("开始批量发布内容")

	request := &dto.BatchUpdateStatusRequest{
		ContentKSUIDs: contentKSUIDs,
		Status:        "published",
		Reason:        "批量发布操作",
	}

	return s.BatchUpdateStatus(ctx, operatorKSUID, request)
}

// BatchArchive 批量归档内容
func (s *batchOperationService) BatchArchive(ctx context.Context, operatorKSUID string, contentKSUIDs []string) (*dto.BatchOperationResponse, *errors.Errors) {
	log.Info().
		Str("operator_ksuid", operatorKSUID).
		Int("content_count", len(contentKSUIDs)).
		Msg("开始批量归档内容")

	request := &dto.BatchUpdateStatusRequest{
		ContentKSUIDs: contentKSUIDs,
		Status:        "archived",
		Reason:        "批量归档操作",
	}

	return s.BatchUpdateStatus(ctx, operatorKSUID, request)
}

// groupContentsByType 按内容类型分组（需要先检测每个内容的类型）
func (s *batchOperationService) groupContentsByType(ctx context.Context, contentKSUIDs []string) (map[string][]string, *errors.Errors) {
	contentsByType := make(map[string][]string)

	// 并发检测每个内容的类型
	var wg sync.WaitGroup
	var mu sync.Mutex
	var detectionErrors []error

	for _, contentKSUID := range contentKSUIDs {
		wg.Add(1)
		go func(ksuid string) {
			defer wg.Done()

			contentType, err := s.getContentType(ctx, ksuid)
			if err != nil {
				mu.Lock()
				detectionErrors = append(detectionErrors, fmt.Errorf("failed to detect type for %s: %w", ksuid, err))
				mu.Unlock()
				return
			}

			mu.Lock()
			contentsByType[contentType] = append(contentsByType[contentType], ksuid)
			mu.Unlock()
		}(contentKSUID)
	}

	wg.Wait()

	if len(detectionErrors) > 0 {
		log.Error().
			Interface("errors", detectionErrors).
			Msg("检测内容类型失败")
		return nil, errors.NewInternalError("检测内容类型失败")
	}

	return contentsByType, nil
}

// getContentType 获取内容类型（通过KSUID前缀或缓存）
func (s *batchOperationService) getContentType(ctx context.Context, contentKSUID string) (string, error) {
	// 方法1: 从缓存获取
	cacheKey := fmt.Sprintf("content_type:%s", contentKSUID)
	contentType, err := s.redis.Get(ctx, cacheKey).Result()
	if err == nil {
		return contentType, nil
	}

	// 方法2: 通过并发查询检测内容类型（兜底方案）
	return s.detectContentType(ctx, contentKSUID)
}

// detectContentType 通过并发查询检测内容类型
func (s *batchOperationService) detectContentType(ctx context.Context, contentKSUID string) (string, error) {
	var wg sync.WaitGroup
	var mu sync.Mutex
	var detectedType string

	// 查询视频服务
	wg.Add(1)
	go func() {
		defer wg.Done()
		if _, err := s.videoClient.GetContentByKSUID(contentKSUID); err == nil {
			mu.Lock()
			detectedType = "video"
			mu.Unlock()
		}
	}()

	// 查询小说服务
	if s.novelClient != nil {
		wg.Add(1)
		go func() {
			defer wg.Done()
			// 预留小说服务查询
			log.Debug().Msg("小说服务类型检测暂未实现")
		}()
	}

	// 查询音乐服务
	if s.musicClient != nil {
		wg.Add(1)
		go func() {
			defer wg.Done()
			// 预留音乐服务查询
			log.Debug().Msg("音乐服务类型检测暂未实现")
		}()
	}

	wg.Wait()

	if detectedType == "" {
		return "", fmt.Errorf("content not found: %s", contentKSUID)
	}

	// 缓存检测结果（24小时）
	cacheKey := fmt.Sprintf("content_type:%s", contentKSUID)
	s.redis.Set(ctx, cacheKey, detectedType, 24*time.Hour)

	return detectedType, nil
}

// executeBatchStatusUpdate 执行批量状态更新
func (s *batchOperationService) executeBatchStatusUpdate(ctx context.Context, contentsByType map[string][]string, status string) *types.BatchOperationResult {
	result := &types.BatchOperationResult{
		TotalCount: 0,
		Failures:   []types.BatchOperationFailure{},
	}

	// 计算总数
	for _, ksUIDs := range contentsByType {
		result.TotalCount += len(ksUIDs)
	}

	var wg sync.WaitGroup
	var mu sync.Mutex

	// 并发更新各服务的内容
	for contentType, ksUIDs := range contentsByType {
		wg.Add(1)
		go func(cType string, ksUIDs []string) {
			defer wg.Done()

			var err error
			switch cType {
			case "video":
				err = s.videoClient.BatchUpdateStatus(ksUIDs, status)
			case "novel":
				if s.novelClient != nil {
					// 预留小说服务调用
					log.Debug().Msg("批量更新小说状态暂未实现")
				} else {
					err = fmt.Errorf("novel service unavailable")
				}
			case "music":
				if s.musicClient != nil {
					// 预留音乐服务调用
					log.Debug().Msg("批量更新音乐状态暂未实现")
				} else {
					err = fmt.Errorf("music service unavailable")
				}
			}

			mu.Lock()
			if err != nil {
				// 记录失败的内容
				for _, ksuid := range ksUIDs {
					result.Failures = append(result.Failures, types.BatchOperationFailure{
						ContentKSUID: ksuid,
						Error:        fmt.Sprintf("failed to update %s content: %v", cType, err),
					})
				}
				result.FailureCount += len(ksUIDs)
			} else {
				result.SuccessCount += len(ksUIDs)
			}
			mu.Unlock()
		}(contentType, ksUIDs)
	}

	wg.Wait()

	return result
}

// executeBatchDelete 执行批量删除
func (s *batchOperationService) executeBatchDelete(ctx context.Context, contentsByType map[string][]string) *types.BatchOperationResult {
	result := &types.BatchOperationResult{
		TotalCount: 0,
		Failures:   []types.BatchOperationFailure{},
	}

	// 计算总数
	for _, ksUIDs := range contentsByType {
		result.TotalCount += len(ksUIDs)
	}

	var wg sync.WaitGroup
	var mu sync.Mutex

	// 并发删除各服务的内容
	for contentType, ksUIDs := range contentsByType {
		wg.Add(1)
		go func(cType string, ksUIDs []string) {
			defer wg.Done()

			var err error
			switch cType {
			case "video":
				err = s.videoClient.BatchDeleteContents(ksUIDs)
			case "novel":
				if s.novelClient != nil {
					// 预留小说服务调用
					log.Debug().Msg("批量删除小说内容暂未实现")
				} else {
					err = fmt.Errorf("novel service unavailable")
				}
			case "music":
				if s.musicClient != nil {
					// 预留音乐服务调用
					log.Debug().Msg("批量删除音乐内容暂未实现")
				} else {
					err = fmt.Errorf("music service unavailable")
				}
			}

			mu.Lock()
			if err != nil {
				// 记录失败的内容
				for _, ksuid := range ksUIDs {
					result.Failures = append(result.Failures, types.BatchOperationFailure{
						ContentKSUID: ksuid,
						Error:        fmt.Sprintf("failed to delete %s content: %v", cType, err),
					})
				}
				result.FailureCount += len(ksUIDs)
			} else {
				result.SuccessCount += len(ksUIDs)
			}
			mu.Unlock()
		}(contentType, ksUIDs)
	}

	wg.Wait()

	return result
}

// logBatchOperation 记录批量操作日志
func (s *batchOperationService) logBatchOperation(ctx context.Context, operatorKSUID, operationType, targetType, description string, beforeData, afterData interface{}) {
	// 异步记录操作日志，避免影响主流程
	go func() {
		if err := s.operationRepo.Create(ctx, &repository.OperationLogCreateRequest{
			OperatorKSUID: operatorKSUID,
			OperationType: operationType,
			TargetType:    targetType,
			TargetKSUID:   "", // 批量操作没有单一目标
			Description:   description,
			BeforeData:    beforeData,
			AfterData:     afterData,
		}); err != nil {
			log.Error().
				Err(err).
				Str("operator_ksuid", operatorKSUID).
				Str("operation_type", operationType).
				Msg("记录批量操作日志失败")
		}
	}()
}

// clearBatchContentCache 清除批量内容相关缓存
func (s *batchOperationService) clearBatchContentCache(ctx context.Context, contentKSUIDs []string) {
	// 异步清除缓存，避免影响主流程
	go func() {
		for _, contentKSUID := range contentKSUIDs {
			// 清除内容类型缓存
			s.redis.Del(ctx, fmt.Sprintf("content_type:%s", contentKSUID))
		}

		// 清除其他相关缓存
		s.redis.Del(ctx, "stats:overview")
		s.redis.Del(ctx, "stats:all_content_types")

		log.Debug().
			Int("content_count", len(contentKSUIDs)).
			Msg("清除批量内容缓存完成")
	}()
}

// BatchUpdateCategory 批量更新内容分类
func (s *batchOperationService) BatchUpdateCategory(ctx context.Context, operatorKSUID string, contentKSUIDs []string, categoryID uint) (*dto.BatchOperationResponse, *errors.Errors) {
	log.Info().
		Str("operator_ksuid", operatorKSUID).
		Int("content_count", len(contentKSUIDs)).
		Uint("category_id", categoryID).
		Msg("开始批量更新内容分类")

	// 参数验证
	if operatorKSUID == "" {
		return nil, errors.NewValidationError("操作者KSUID不能为空")
	}
	if len(contentKSUIDs) == 0 {
		return nil, errors.NewValidationError("内容KSUID列表不能为空")
	}
	if categoryID == 0 {
		return nil, errors.NewValidationError("分类ID不能为空")
	}

	// 限制批量操作数量
	if len(contentKSUIDs) > 100 {
		return nil, errors.NewValidationError("批量操作数量不能超过100个")
	}

	// 按内容类型分组
	contentsByType, gErr := s.groupContentsByType(ctx, contentKSUIDs)
	if gErr != nil {
		return nil, gErr
	}

	// 执行批量分类更新
	result := s.executeBatchCategoryUpdate(ctx, contentsByType, categoryID)

	// 记录操作日志
	s.logBatchOperation(ctx, operatorKSUID, "batch_update_category", "content",
		fmt.Sprintf("批量更新%d个内容分类为: %d", len(contentKSUIDs), categoryID),
		nil, map[string]interface{}{"category_id": categoryID})

	// 清除相关缓存
	s.clearBatchContentCache(ctx, contentKSUIDs)

	log.Info().
		Str("operator_ksuid", operatorKSUID).
		Int("success_count", result.SuccessCount).
		Int("failure_count", result.FailureCount).
		Msg("批量更新内容分类完成")

	return dto.FromBatchOperationResult(result), nil
}

// BatchUpdateTags 批量更新内容标签
func (s *batchOperationService) BatchUpdateTags(ctx context.Context, operatorKSUID string, contentKSUIDs []string, tags []string) (*dto.BatchOperationResponse, *errors.Errors) {
	log.Info().
		Str("operator_ksuid", operatorKSUID).
		Int("content_count", len(contentKSUIDs)).
		Interface("tags", tags).
		Msg("开始批量更新内容标签")

	// 参数验证
	if operatorKSUID == "" {
		return nil, errors.NewValidationError("操作者KSUID不能为空")
	}
	if len(contentKSUIDs) == 0 {
		return nil, errors.NewValidationError("内容KSUID列表不能为空")
	}

	// 限制批量操作数量
	if len(contentKSUIDs) > 100 {
		return nil, errors.NewValidationError("批量操作数量不能超过100个")
	}

	// 按内容类型分组
	contentsByType, gErr := s.groupContentsByType(ctx, contentKSUIDs)
	if gErr != nil {
		return nil, gErr
	}

	// 执行批量标签更新
	result := s.executeBatchTagsUpdate(ctx, contentsByType, tags)

	// 记录操作日志
	s.logBatchOperation(ctx, operatorKSUID, "batch_update_tags", "content",
		fmt.Sprintf("批量更新%d个内容标签", len(contentKSUIDs)),
		nil, map[string]interface{}{"tags": tags})

	// 清除相关缓存
	s.clearBatchContentCache(ctx, contentKSUIDs)

	log.Info().
		Str("operator_ksuid", operatorKSUID).
		Int("success_count", result.SuccessCount).
		Int("failure_count", result.FailureCount).
		Msg("批量更新内容标签完成")

	return dto.FromBatchOperationResult(result), nil
}

// BatchTransferOwnership 批量转移内容所有权
func (s *batchOperationService) BatchTransferOwnership(ctx context.Context, operatorKSUID string, contentKSUIDs []string, newOwnerKSUID string) (*dto.BatchOperationResponse, *errors.Errors) {
	log.Info().
		Str("operator_ksuid", operatorKSUID).
		Int("content_count", len(contentKSUIDs)).
		Str("new_owner_ksuid", newOwnerKSUID).
		Msg("开始批量转移内容所有权")

	// 参数验证
	if operatorKSUID == "" {
		return nil, errors.NewValidationError("操作者KSUID不能为空")
	}
	if len(contentKSUIDs) == 0 {
		return nil, errors.NewValidationError("内容KSUID列表不能为空")
	}
	if newOwnerKSUID == "" {
		return nil, errors.NewValidationError("新所有者KSUID不能为空")
	}

	// 限制批量操作数量
	if len(contentKSUIDs) > 50 {
		return nil, errors.NewValidationError("批量转移数量不能超过50个")
	}

	// 按内容类型分组
	contentsByType, gErr := s.groupContentsByType(ctx, contentKSUIDs)
	if gErr != nil {
		return nil, gErr
	}

	// 执行批量所有权转移
	result := s.executeBatchOwnershipTransfer(ctx, contentsByType, newOwnerKSUID)

	// 记录操作日志
	s.logBatchOperation(ctx, operatorKSUID, "batch_transfer_ownership", "content",
		fmt.Sprintf("批量转移%d个内容所有权给: %s", len(contentKSUIDs), newOwnerKSUID),
		nil, map[string]interface{}{"new_owner_ksuid": newOwnerKSUID})

	// 清除相关缓存
	s.clearBatchContentCache(ctx, contentKSUIDs)

	log.Info().
		Str("operator_ksuid", operatorKSUID).
		Int("success_count", result.SuccessCount).
		Int("failure_count", result.FailureCount).
		Msg("批量转移内容所有权完成")

	return dto.FromBatchOperationResult(result), nil
}

// executeBatchCategoryUpdate 执行批量分类更新
func (s *batchOperationService) executeBatchCategoryUpdate(ctx context.Context, contentsByType map[string][]string, categoryID uint) *types.BatchOperationResult {
	result := &types.BatchOperationResult{
		TotalCount: 0,
		Failures:   []types.BatchOperationFailure{},
	}

	// 计算总数
	for _, ksUIDs := range contentsByType {
		result.TotalCount += len(ksUIDs)
	}

	var wg sync.WaitGroup
	var mu sync.Mutex

	// 并发更新各服务的内容分类
	for contentType, ksUIDs := range contentsByType {
		wg.Add(1)
		go func(cType string, ksUIDs []string) {
			defer wg.Done()

			var err error
			switch cType {
			case "video":
				err = s.videoClient.BatchUpdateCategory(ksUIDs, categoryID)
			case "novel":
				if s.novelClient != nil {
					// 预留小说服务调用
					log.Debug().Msg("批量更新小说分类暂未实现")
				} else {
					err = fmt.Errorf("novel service unavailable")
				}
			case "music":
				if s.musicClient != nil {
					// 预留音乐服务调用
					log.Debug().Msg("批量更新音乐分类暂未实现")
				} else {
					err = fmt.Errorf("music service unavailable")
				}
			}

			mu.Lock()
			if err != nil {
				// 记录失败的内容
				for _, ksuid := range ksUIDs {
					result.Failures = append(result.Failures, types.BatchOperationFailure{
						ContentKSUID: ksuid,
						Error:        fmt.Sprintf("failed to update %s content category: %v", cType, err),
					})
				}
				result.FailureCount += len(ksUIDs)
			} else {
				result.SuccessCount += len(ksUIDs)
			}
			mu.Unlock()
		}(contentType, ksUIDs)
	}

	wg.Wait()

	return result
}

// executeBatchTagsUpdate 执行批量标签更新
func (s *batchOperationService) executeBatchTagsUpdate(ctx context.Context, contentsByType map[string][]string, tags []string) *types.BatchOperationResult {
	result := &types.BatchOperationResult{
		TotalCount: 0,
		Failures:   []types.BatchOperationFailure{},
	}

	// 计算总数
	for _, ksUIDs := range contentsByType {
		result.TotalCount += len(ksUIDs)
	}

	var wg sync.WaitGroup
	var mu sync.Mutex

	// 并发更新各服务的内容标签
	for contentType, ksUIDs := range contentsByType {
		wg.Add(1)
		go func(cType string, ksUIDs []string) {
			defer wg.Done()

			var err error
			switch cType {
			case "video":
				err = s.videoClient.BatchUpdateTags(ksUIDs, tags)
			case "novel":
				if s.novelClient != nil {
					// 预留小说服务调用
					log.Debug().Msg("批量更新小说标签暂未实现")
				} else {
					err = fmt.Errorf("novel service unavailable")
				}
			case "music":
				if s.musicClient != nil {
					// 预留音乐服务调用
					log.Debug().Msg("批量更新音乐标签暂未实现")
				} else {
					err = fmt.Errorf("music service unavailable")
				}
			}

			mu.Lock()
			if err != nil {
				// 记录失败的内容
				for _, ksuid := range ksUIDs {
					result.Failures = append(result.Failures, types.BatchOperationFailure{
						ContentKSUID: ksuid,
						Error:        fmt.Sprintf("failed to update %s content tags: %v", cType, err),
					})
				}
				result.FailureCount += len(ksUIDs)
			} else {
				result.SuccessCount += len(ksUIDs)
			}
			mu.Unlock()
		}(contentType, ksUIDs)
	}

	wg.Wait()

	return result
}

// executeBatchOwnershipTransfer 执行批量所有权转移
func (s *batchOperationService) executeBatchOwnershipTransfer(ctx context.Context, contentsByType map[string][]string, newOwnerKSUID string) *types.BatchOperationResult {
	result := &types.BatchOperationResult{
		TotalCount: 0,
		Failures:   []types.BatchOperationFailure{},
	}

	// 计算总数
	for _, ksUIDs := range contentsByType {
		result.TotalCount += len(ksUIDs)
	}

	var wg sync.WaitGroup
	var mu sync.Mutex

	// 并发转移各服务的内容所有权
	for contentType, ksUIDs := range contentsByType {
		wg.Add(1)
		go func(cType string, ksUIDs []string) {
			defer wg.Done()

			var err error
			switch cType {
			case "video":
				err = s.videoClient.BatchTransferOwnership(ksUIDs, newOwnerKSUID)
			case "novel":
				if s.novelClient != nil {
					// 预留小说服务调用
					log.Debug().Msg("批量转移小说所有权暂未实现")
				} else {
					err = fmt.Errorf("novel service unavailable")
				}
			case "music":
				if s.musicClient != nil {
					// 预留音乐服务调用
					log.Debug().Msg("批量转移音乐所有权暂未实现")
				} else {
					err = fmt.Errorf("music service unavailable")
				}
			}

			mu.Lock()
			if err != nil {
				// 记录失败的内容
				for _, ksuid := range ksUIDs {
					result.Failures = append(result.Failures, types.BatchOperationFailure{
						ContentKSUID: ksuid,
						Error:        fmt.Sprintf("failed to transfer %s content ownership: %v", cType, err),
					})
				}
				result.FailureCount += len(ksUIDs)
			} else {
				result.SuccessCount += len(ksUIDs)
			}
			mu.Unlock()
		}(contentType, ksUIDs)
	}

	wg.Wait()

	return result
}

// SearchContents 搜索内容
func (s *batchOperationService) SearchContents(ctx context.Context, request *dto.ContentSearchRequest) (*dto.ContentSearchResponse, *errors.Errors) {
	log.Info().
		Interface("request", request).
		Msg("开始搜索内容")

	// 参数验证
	if request.Query == "" {
		return nil, errors.NewValidationError("搜索关键词不能为空")
	}

	// 设置默认值
	if request.Page <= 0 {
		request.Page = 1
	}
	if request.Limit <= 0 {
		request.Limit = 20
	}
	if request.Limit > 100 {
		request.Limit = 100
	}

	startTime := time.Now()

	// 这里需要实现搜索逻辑
	// 可以集成Elasticsearch或其他搜索引擎
	// 暂时返回空结果
	result := &types.ContentSearchResult{
		Contents:   []*types.BaseContent{},
		Total:      0,
		Page:       request.Page,
		Limit:      request.Limit,
		TotalPages: 0,
		Query:      request.Query,
	}

	searchTime := time.Since(startTime).Milliseconds()

	log.Info().
		Str("query", request.Query).
		Int("total_results", result.Total).
		Int64("search_time_ms", searchTime).
		Msg("搜索内容完成")

	return dto.FromContentSearchResult(result, searchTime), nil
}

// GetOperationLogs 获取操作日志
func (s *batchOperationService) GetOperationLogs(ctx context.Context, query *dto.OperationLogQueryRequest) (*dto.OperationLogListResponse, *errors.Errors) {
	log.Info().
		Interface("query", query).
		Msg("开始获取操作日志")

	// 设置默认值
	if query.Page <= 0 {
		query.Page = 1
	}
	if query.Limit <= 0 {
		query.Limit = 20
	}
	if query.Limit > 100 {
		query.Limit = 100
	}

	// 转换查询条件
	logQuery := query.ToOperationLogQuery()

	// 从数据库获取操作日志
	logs, total, err := s.operationRepo.Query(ctx, logQuery)
	if err != nil {
		log.Error().
			Err(err).
			Interface("query", query).
			Msg("获取操作日志失败")
		return nil, errors.NewInternalError("获取操作日志失败")
	}

	// 转换为响应DTO
	logResponses := make([]*dto.OperationLogResponse, len(logs))
	for i, logEntry := range logs {
		logResponses[i] = dto.FromOperationLog(logEntry)
	}

	totalPages := int((total + int64(query.Limit) - 1) / int64(query.Limit))

	result := &dto.OperationLogListResponse{
		Logs:       logResponses,
		Total:      total,
		Page:       query.Page,
		Limit:      query.Limit,
		TotalPages: totalPages,
	}

	log.Info().
		Int64("total_logs", total).
		Int("page_logs", len(logResponses)).
		Msg("获取操作日志成功")

	return result, nil
}

// GetOperationLogStats 获取操作日志统计
func (s *batchOperationService) GetOperationLogStats(ctx context.Context, period string) (*dto.OperationLogStatsResponse, *errors.Errors) {
	log.Info().
		Str("period", period).
		Msg("开始获取操作日志统计")

	// 参数验证
	if period == "" {
		period = "weekly"
	}

	// 尝试从缓存获取
	cacheKey := fmt.Sprintf("stats:operation_logs:%s", period)
	var cachedResult dto.OperationLogStatsResponse
	if err := s.redis.Get(ctx, cacheKey).Scan(&cachedResult); err == nil {
		log.Debug().
			Str("period", period).
			Msg("从缓存获取操作日志统计成功")
		return &cachedResult, nil
	}

	// 从数据库获取统计数据
	stats, err := s.operationRepo.GetStats(ctx, period)
	if err != nil {
		log.Error().
			Err(err).
			Str("period", period).
			Msg("获取操作日志统计失败")
		return nil, errors.NewInternalError("获取操作日志统计失败")
	}

	result := dto.FromOperationLogStats(stats)

	// 缓存结果（30分钟）
	s.redis.Set(ctx, cacheKey, result, 30*time.Minute)

	log.Info().
		Str("period", period).
		Int64("total_operations", result.TotalOperations).
		Msg("获取操作日志统计成功")

	return result, nil
}

// GetConfigs 获取配置列表
func (s *batchOperationService) GetConfigs(ctx context.Context, query *dto.ConfigQueryRequest) (*dto.ConfigListResponse, *errors.Errors) {
	log.Info().
		Interface("query", query).
		Msg("开始获取配置列表")

	// 设置默认值
	if query.Page <= 0 {
		query.Page = 1
	}
	if query.Limit <= 0 {
		query.Limit = 20
	}
	if query.Limit > 100 {
		query.Limit = 100
	}

	// 从数据库获取配置
	configs, total, err := s.configRepo.Query(ctx, &repository.ConfigQueryRequest{
		Category: query.Category,
		IsActive: query.IsActive,
		Page:     query.Page,
		Limit:    query.Limit,
	})
	if err != nil {
		log.Error().
			Err(err).
			Interface("query", query).
			Msg("获取配置列表失败")
		return nil, errors.NewInternalError("获取配置列表失败")
	}

	// 转换为响应DTO
	configResponses := make([]*dto.ConfigResponse, len(configs))
	for i, config := range configs {
		configResponses[i] = dto.FromConfig(config)
	}

	totalPages := int((total + int64(query.Limit) - 1) / int64(query.Limit))

	result := &dto.ConfigListResponse{
		Configs:    configResponses,
		Total:      total,
		Page:       query.Page,
		Limit:      query.Limit,
		TotalPages: totalPages,
	}

	log.Info().
		Int64("total_configs", total).
		Int("page_configs", len(configResponses)).
		Msg("获取配置列表成功")

	return result, nil
}

// UpdateConfig 更新配置
func (s *batchOperationService) UpdateConfig(ctx context.Context, operatorKSUID string, configKey string, request *dto.ConfigUpdateRequest) (*dto.ConfigResponse, *errors.Errors) {
	log.Info().
		Str("operator_ksuid", operatorKSUID).
		Str("config_key", configKey).
		Interface("request", request).
		Msg("开始更新配置")

	// 参数验证
	if operatorKSUID == "" {
		return nil, errors.NewValidationError("操作者KSUID不能为空")
	}
	if configKey == "" {
		return nil, errors.NewValidationError("配置键不能为空")
	}
	if request.ConfigValue == "" {
		return nil, errors.NewValidationError("配置值不能为空")
	}

	// 获取原配置
	oldConfig, err := s.configRepo.GetByKey(ctx, configKey)
	if err != nil {
		log.Error().
			Err(err).
			Str("config_key", configKey).
			Msg("获取原配置失败")
		return nil, errors.NewNotFoundError("配置不存在")
	}

	// 更新配置
	updateReq := &repository.ConfigUpdateRequest{
		ConfigValue: request.ConfigValue,
		Description: request.Description,
		IsActive:    request.IsActive,
	}

	updatedConfig, err := s.configRepo.Update(ctx, configKey, updateReq)
	if err != nil {
		log.Error().
			Err(err).
			Str("config_key", configKey).
			Msg("更新配置失败")
		return nil, errors.NewInternalError("更新配置失败")
	}

	// 记录操作日志
	s.logBatchOperation(ctx, operatorKSUID, "update_config", "config",
		fmt.Sprintf("更新配置: %s", configKey),
		oldConfig, updatedConfig)

	result := dto.FromConfig(updatedConfig)

	log.Info().
		Str("operator_ksuid", operatorKSUID).
		Str("config_key", configKey).
		Msg("更新配置成功")

	return result, nil
}

// GetConfigStats 获取配置统计
func (s *batchOperationService) GetConfigStats(ctx context.Context) (*dto.ConfigStatsResponse, *errors.Errors) {
	log.Info().Msg("开始获取配置统计")

	// 尝试从缓存获取
	cacheKey := "stats:configs"
	var cachedResult dto.ConfigStatsResponse
	if err := s.redis.Get(ctx, cacheKey).Scan(&cachedResult); err == nil {
		log.Debug().Msg("从缓存获取配置统计成功")
		return &cachedResult, nil
	}

	// 从数据库获取统计数据
	stats, err := s.configRepo.GetStats(ctx)
	if err != nil {
		log.Error().
			Err(err).
			Msg("获取配置统计失败")
		return nil, errors.NewInternalError("获取配置统计失败")
	}

	result := dto.FromConfigStats(stats)

	// 缓存结果（1小时）
	s.redis.Set(ctx, cacheKey, result, 1*time.Hour)

	log.Info().
		Int64("total_configs", result.TotalConfigs).
		Msg("获取配置统计成功")

	return result, nil
}

// GetSystemStats 获取系统统计
func (s *batchOperationService) GetSystemStats(ctx context.Context) (*dto.SystemStatsResponse, *errors.Errors) {
	log.Info().Msg("开始获取系统统计")

	// 尝试从缓存获取
	cacheKey := "stats:system"
	var cachedResult dto.SystemStatsResponse
	if err := s.redis.Get(ctx, cacheKey).Scan(&cachedResult); err == nil {
		log.Debug().Msg("从缓存获取系统统计成功")
		return &cachedResult, nil
	}

	// 并发获取各种系统统计
	var wg sync.WaitGroup
	var mu sync.Mutex
	var services []*dto.ServiceHealthResponse
	var totalContents, totalUsers, totalOperations int64
	var cacheStats *dto.CacheStatsResponse
	var databaseStats *dto.DatabaseStatsResponse

	// 获取服务健康状态
	wg.Add(1)
	go func() {
		defer wg.Done()
		if healthServices, gErr := s.GetServiceHealth(ctx); gErr == nil {
			mu.Lock()
			services = healthServices
			mu.Unlock()
		}
	}()

	// 获取缓存统计
	wg.Add(1)
	go func() {
		defer wg.Done()
		// 这里需要实现缓存统计逻辑
		mu.Lock()
		cacheStats = &dto.CacheStatsResponse{
			TotalCaches:   0,
			CachesByType:  make(map[string]int64),
			ExpiredCaches: 0,
			CacheHitRate:  0.0,
			AvgCacheAge:   0.0,
		}
		mu.Unlock()
	}()

	// 获取数据库统计
	wg.Add(1)
	go func() {
		defer wg.Done()
		// 这里需要实现数据库统计逻辑
		mu.Lock()
		databaseStats = &dto.DatabaseStatsResponse{
			TotalTables:     0,
			TotalRecords:    0,
			DatabaseSize:    0,
			ConnectionCount: 0,
		}
		mu.Unlock()
	}()

	wg.Wait()

	result := &dto.SystemStatsResponse{
		Services:        services,
		TotalContents:   totalContents,
		TotalUsers:      totalUsers,
		TotalOperations: totalOperations,
		CacheStats:      cacheStats,
		DatabaseStats:   databaseStats,
		Uptime:          0, // 需要实现系统启动时间计算
	}

	// 缓存结果（5分钟）
	s.redis.Set(ctx, cacheKey, result, 5*time.Minute)

	log.Info().Msg("获取系统统计成功")

	return result, nil
}

// GetServiceHealth 获取服务健康状态
func (s *batchOperationService) GetServiceHealth(ctx context.Context) ([]*dto.ServiceHealthResponse, *errors.Errors) {
	log.Info().Msg("开始获取服务健康状态")

	var services []*dto.ServiceHealthResponse

	// 检查视频服务
	videoHealth := s.checkServiceHealth("video-service", func() error {
		return s.videoClient.HealthCheck()
	})
	services = append(services, videoHealth)

	// 检查交互服务
	interactionHealth := s.checkServiceHealth("interaction-service", func() error {
		return s.interactionClient.HealthCheck()
	})
	services = append(services, interactionHealth)

	// 检查小说服务（如果启用）
	if s.novelClient != nil {
		novelHealth := s.checkServiceHealth("novel-service", func() error {
			// 预留小说服务健康检查
			return nil
		})
		services = append(services, novelHealth)
	}

	// 检查音乐服务（如果启用）
	if s.musicClient != nil {
		musicHealth := s.checkServiceHealth("music-service", func() error {
			// 预留音乐服务健康检查
			return nil
		})
		services = append(services, musicHealth)
	}

	// 检查Redis
	redisHealth := s.checkServiceHealth("redis", func() error {
		return s.redis.Ping(ctx).Err()
	})
	services = append(services, redisHealth)

	log.Info().
		Int("service_count", len(services)).
		Msg("获取服务健康状态完成")

	return services, nil
}

// checkServiceHealth 检查单个服务健康状态
func (s *batchOperationService) checkServiceHealth(serviceName string, healthCheck func() error) *dto.ServiceHealthResponse {
	startTime := time.Now()
	err := healthCheck()
	responseTime := time.Since(startTime).Milliseconds()

	health := &dto.ServiceHealthResponse{
		ServiceName:   serviceName,
		LastCheckTime: time.Now(),
		ResponseTime:  responseTime,
		IsHealthy:     err == nil,
	}

	if err == nil {
		health.Status = "healthy"
	} else {
		health.Status = "unhealthy"
		health.ErrorMessage = err.Error()
	}

	return health
}

// HealthCheck 健康检查
func (s *batchOperationService) HealthCheck(ctx context.Context) *errors.Errors {
	log.Debug().Msg("开始批量操作服务健康检查")

	var healthErrors []error

	// 检查视频服务
	if err := s.videoClient.HealthCheck(); err != nil {
		healthErrors = append(healthErrors, fmt.Errorf("video service unhealthy: %w", err))
	}

	// 检查交互服务
	if err := s.interactionClient.HealthCheck(); err != nil {
		healthErrors = append(healthErrors, fmt.Errorf("interaction service unhealthy: %w", err))
	}

	// 检查小说服务（如果启用）
	if s.novelClient != nil {
		// 预留小说服务健康检查
		log.Debug().Msg("小说服务健康检查暂未实现")
	}

	// 检查音乐服务（如果启用）
	if s.musicClient != nil {
		// 预留音乐服务健康检查
		log.Debug().Msg("音乐服务健康检查暂未实现")
	}

	// 检查Redis连接
	if err := s.redis.Ping(ctx).Err(); err != nil {
		healthErrors = append(healthErrors, fmt.Errorf("redis unhealthy: %w", err))
	}

	if len(healthErrors) > 0 {
		log.Error().
			Interface("errors", healthErrors).
			Msg("批量操作服务健康检查失败")
		return errors.NewServiceUnavailableError("部分服务不健康")
	}

	log.Debug().Msg("批量操作服务健康检查通过")
	return nil
}
