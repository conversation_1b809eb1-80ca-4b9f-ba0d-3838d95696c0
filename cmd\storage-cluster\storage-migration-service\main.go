package main

import (
	"context"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog/log"

	"pxpat-backend/internal/storage-cluster/storage-migration-service/handler"
	"pxpat-backend/internal/storage-cluster/storage-migration-service/migrations"
	"pxpat-backend/internal/storage-cluster/storage-migration-service/repository"
	"pxpat-backend/internal/storage-cluster/storage-migration-service/routes"
	"pxpat-backend/internal/storage-cluster/storage-migration-service/scheduler"
	"pxpat-backend/internal/storage-cluster/storage-migration-service/service"
	"pxpat-backend/internal/storage-cluster/storage-migration-service/types"
	configLoader "pxpat-backend/pkg/config"
	"pxpat-backend/pkg/consul"
	"pxpat-backend/pkg/consul/health"
	"pxpat-backend/pkg/database"
	"pxpat-backend/pkg/logger"
	"pxpat-backend/pkg/storage"
)

func main() {
	clusterName := "storage"
	serviceName := "storage-migration"

	// 加载配置
	cfg := configLoader.Load[types.Config](configLoader.LoaderConfig{
		TypeName:    configLoader.Service,
		ClusterName: clusterName,
		ServiceName: serviceName,
		UseRedis:    false, // 存储迁移服务不需要Redis
	})

	// 初始化日志
	err := logger.InitLogger(cfg.Log, serviceName)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to initialize logger")
		return
	}

	log.Info().Msg("存储迁移服务启动中...")

	// 初始化数据库
	db, err := database.ConnectDB(cfg.Database)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to initialize database")
	}
	log.Info().Msg("Database connected successfully")

	// 执行数据库迁移
	if err := migrations.AutoMigrate(db); err != nil {
		log.Fatal().Err(err).Msg("Database migration failed")
	}
	log.Info().Msg("Database migration completed")

	// 初始化存储客户端
	hotStorage, err := storage.NewStorageClient(cfg.StorageMigration.HotStorage)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to initialize hot storage client")
	}
	log.Info().Str("provider", cfg.StorageMigration.HotStorage.Provider).Msg("Hot storage client initialized")

	coldStorage, err := storage.NewStorageClient(cfg.StorageMigration.ColdStorage)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to initialize cold storage client")
	}
	log.Info().Str("provider", cfg.StorageMigration.ColdStorage.Provider).Msg("Cold storage client initialized")

	// 初始化仓储管理器
	repoManager := repository.NewRepositoryManager(db)
	log.Info().Msg("Repository manager initialized")

	// 初始化服务层
	loggingService := service.NewLoggingService(repoManager)
	discoveryService := service.NewFileDiscoveryService(repoManager, hotStorage)
	workerService := service.NewMigrationWorkerService(hotStorage, coldStorage, loggingService)
	migrationService := service.NewMigrationService(
		repoManager,
		workerService,
		discoveryService,
		loggingService,
		cfg.StorageMigration.MigrationPolicy.MaxConcurrentTasks,
		cfg.StorageMigration.MigrationPolicy.AutoMigrationDelay,
		7, // 保留7天
	)
	log.Info().Msg("Service layer initialized")

	// 初始化调度器
	var migrationScheduler *scheduler.Scheduler
	if cfg.StorageMigration.SchedulerConfig.Enabled {
		migrationScheduler = scheduler.NewScheduler(
			migrationService,
			cfg.StorageMigration.SchedulerConfig.ScanInterval,
			cfg.StorageMigration.SchedulerConfig.ProcessInterval,
			cfg.StorageMigration.MigrationPolicy.MaxConcurrentTasks,
		)

		if err := migrationScheduler.Start(); err != nil {
			log.Fatal().Err(err).Msg("Failed to start migration scheduler")
		}
		log.Info().Msg("Migration scheduler started")
	}

	// 初始化处理器
	migrationHandler := handler.NewMigrationHandler(migrationService, migrationScheduler)
	log.Info().Msg("Handler layer initialized")

	// 创建 Gin 路由
	router := gin.Default()

	// 设置路由
	routes.SetupRoutes(router, migrationHandler)
	log.Info().Msg("Routes configured")

	// 初始化Consul管理器
	consulManager, err := consul.NewManager(&cfg.Consul)
	if err != nil {
		log.Fatal().Err(err).Msg("Consul管理器初始化失败")
	}

	// 启动Consul管理器
	consulCtx := context.Background()
	if err := consulManager.Start(consulCtx); err != nil {
		log.Fatal().Err(err).Msg("Consul管理器启动失败")
	}

	// 初始化健康检查处理器
	healthHandler := consul.NewHealthHandler(consulManager, "1.0.0")

	// 添加数据库健康检查
	healthHandler.AddChecker(health.NewDatabaseHealthChecker("database", func() error {
		sqlDB, err := db.DB()
		if err != nil {
			return err
		}
		return sqlDB.Ping()
	}))

	// 注册健康检查路由
	healthHandler.RegisterHealthRoutes(router)

	// 启动HTTP服务器
	server := &http.Server{
		Addr:    fmt.Sprintf(":%d", cfg.Server.Port),
		Handler: router,
	}

	// 优雅关闭
	go func() {
		log.Info().Int("port", cfg.Server.Port).Msg("🚀 存储迁移服务启动")
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatal().Err(err).Msg("服务器启动失败")
		}
	}()

	// 等待中断信号
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit
	log.Info().Msg("正在关闭存储迁移服务...")

	// 停止调度器
	if migrationScheduler != nil {
		if err := migrationScheduler.Stop(); err != nil {
			log.Error().Err(err).Msg("Failed to stop migration scheduler")
		}
	}

	// 停止Consul管理器
	if err := consulManager.Stop(); err != nil {
		log.Error().Err(err).Msg("停止Consul管理器失败")
	}

	// 关闭HTTP服务器
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()
	if err := server.Shutdown(ctx); err != nil {
		log.Fatal().Err(err).Msg("服务器强制关闭")
	}

	log.Info().Msg("存储迁移服务已关闭")
}
