package main

import (
	"context"
	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog/log"
	"go.uber.org/fx"
	"gorm.io/gorm"
	"pxpat-backend/cmd"
	externalHandler "pxpat-backend/internal/storage-cluster/user-storage-service/external/handler"
	intraHandler "pxpat-backend/internal/storage-cluster/user-storage-service/intra/handler"
	"pxpat-backend/internal/storage-cluster/user-storage-service/messaging/publisher"
	"pxpat-backend/internal/storage-cluster/user-storage-service/migrations"
	"pxpat-backend/internal/storage-cluster/user-storage-service/repository"
	"pxpat-backend/internal/storage-cluster/user-storage-service/repository/impl"
	"pxpat-backend/internal/storage-cluster/user-storage-service/routes"
	"pxpat-backend/internal/storage-cluster/user-storage-service/service"
	"pxpat-backend/internal/storage-cluster/user-storage-service/types"
	"pxpat-backend/pkg/auth"
	configLoader "pxpat-backend/pkg/config"
	"pxpat-backend/pkg/consul"
	"pxpat-backend/pkg/consul/health"
	"pxpat-backend/pkg/database"
	"pxpat-backend/pkg/logger"
	"pxpat-backend/pkg/middleware/cors"
	"pxpat-backend/pkg/storage"
)

// 提供配置
func provideConfig() *types.Config {
	clusterName := "storage"
	serviceName := "user-storage"

	return configLoader.Load[types.Config](configLoader.LoaderConfig{
		TypeName:    configLoader.Service,
		ClusterName: clusterName,
		ServiceName: serviceName,
		UseRedis:    false,
	})
}

// 提供日志器
func provideLogger(cfg *types.Config) {
	serviceName := "user-storage"

	if err := logger.InitLogger(cfg.Log, serviceName); err != nil {
		log.Fatal().Err(err).Msg("Failed to initialize logger")
	}

	log.Info().Msg("用户存储服务启动中...")
}

// 提供数据库连接
func provideDatabase(cfg *types.Config) (*gorm.DB, error) {
	db, err := database.ConnectDB(cfg.Database)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to initialize database")
		return nil, err
	}

	// 运行数据库迁移
	if err := migrations.RunMigrations(db); err != nil {
		log.Fatal().Err(err).Msg("Failed to run migrations")
		return nil, err
	}

	return db, nil
}

// 提供存储客户端
func provideStorageClient(cfg *types.Config) (storage.StorageClient, error) {
	storageClient, err := storage.NewStorageClient(cfg.Storage.Minio)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to initialize storage client")
		return nil, err
	}
	return storageClient, nil
}

// 提供Repository层
func provideRepositories(db *gorm.DB) (repository.AvatarRepository, repository.BannerRepository) {
	avatarRepo := impl.NewGormAvatarRepository(db)
	bannerRepo := impl.NewGormBannerRepository(db)
	return avatarRepo, bannerRepo
}

// 提供MQ发布器
func provideMQPublisher(cfg *types.Config) (*publisher.Publisher, error) {
	mqPublisher, err := publisher.NewPublisher(cfg.RabbitMQ.URL)
	if err != nil {
		log.Error().Err(err).Msg("Failed to initialize MQ publisher")
		return nil, err
	}
	log.Info().Msg("MQ publisher initialized successfully")
	return mqPublisher, nil
}

// 提供Service层
func provideServices(
	storageClient storage.StorageClient,
	avatarRepo repository.AvatarRepository,
	bannerRepo repository.BannerRepository,
	mqPublisher *publisher.Publisher,
	cfg *types.Config,
) (*service.AvatarService, *service.BannerService) {
	avatarService := service.NewAvatarService(storageClient, avatarRepo, mqPublisher, cfg.Avatar)
	bannerService := service.NewBannerService(storageClient, bannerRepo, mqPublisher, cfg.Banner)
	return avatarService, bannerService
}

// 提供External Handler层
func provideExternalHandlers(
	avatarService *service.AvatarService,
	bannerService *service.BannerService,
) (*externalHandler.AvatarHandler, *externalHandler.BannerHandler) {
	avatarHandler := externalHandler.NewAvatarHandler(avatarService)
	bannerHandler := externalHandler.NewBannerHandler(bannerService)
	return avatarHandler, bannerHandler
}

// 提供Internal Handler层
func provideInternalHandlers(
	avatarService *service.AvatarService,
	bannerService *service.BannerService,
) (*intraHandler.InternalAvatarHandler, *intraHandler.InternalBannerHandler) {
	internalAvatarHandler := intraHandler.NewInternalAvatarHandler(avatarService)
	internalBannerHandler := intraHandler.NewInternalBannerHandler(bannerService)
	return internalAvatarHandler, internalBannerHandler
}

// 提供JWT管理器
func provideJWTManager(cfg *types.Config) *auth.Manager {
	jwtManager := auth.NewJWTManager(cfg.JWT.SecretKey, cfg.JWT.Expiration, cfg.JWT.Issuer)
	return &jwtManager
}

// 提供Consul管理器
func provideConsulManager(cfg *types.Config) (*consul.Manager, error) {
	consulManager, err := consul.NewManager(&cfg.Consul)
	if err != nil {
		log.Fatal().Err(err).Msg("Consul管理器初始化失败")
		return nil, err
	}
	return consulManager, nil
}

// 提供健康检查处理器
func provideHealthHandler(consulManager *consul.Manager, db *gorm.DB) *consul.HealthHandler {
	healthHandler := consul.NewHealthHandler(consulManager, "1.0.0")

	// 添加数据库健康检查
	healthHandler.AddChecker(health.NewDatabaseHealthChecker("database", func() error {
		sqlDB, err := db.DB()
		if err != nil {
			return err
		}
		return sqlDB.Ping()
	}))

	return healthHandler
}

// 提供Gin引擎
func provideGinEngine(
	cfg *types.Config,
	externalAvatarHandler *externalHandler.AvatarHandler,
	externalBannerHandler *externalHandler.BannerHandler,
	internalAvatarHandler *intraHandler.InternalAvatarHandler,
	internalBannerHandler *intraHandler.InternalBannerHandler,
	jwtManager *auth.Manager,
	healthHandler *consul.HealthHandler,
) *gin.Engine {
	gin.SetMode(cfg.Server.Mode)
	router := gin.Default()
	router.Use(cors.CORSMiddleware(cfg.Security.Cors))

	// 注册路由
	routes.RegisterRoutes(router, externalAvatarHandler, externalBannerHandler, internalAvatarHandler, internalBannerHandler, *jwtManager)

	// 注册健康检查路由
	healthHandler.RegisterHealthRoutes(router)

	return router
}

// 应用生命周期管理
func manageLifecycle(
	lc fx.Lifecycle,
	cfg *types.Config,
	consulManager *consul.Manager,
	ginEngine *gin.Engine,
) {
	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) error {
			// 启动Consul管理器
			if err := consulManager.Start(ctx); err != nil {
				log.Fatal().Err(err).Msg("Consul管理器启动失败")
				return err
			}
			log.Info().Msg("Consul管理器启动成功")

			// 启动HTTP服务器（在goroutine中）
			go func() {
				cmd.GraceStartAndClose(cfg.Server, ginEngine)
			}()

			return nil
		},
		OnStop: func(ctx context.Context) error {
			// 停止Consul管理器
			if err := consulManager.Stop(); err != nil {
				log.Error().Err(err).Msg("停止Consul管理器失败")
			} else {
				log.Info().Msg("Consul管理器停止成功")
			}

			log.Info().Msg("用户存储服务已关闭")
			return nil
		},
	})
}

func main() {
	app := fx.New(
		// 提供依赖
		fx.Provide(
			provideConfig,
			provideDatabase,
			provideStorageClient,
			provideRepositories,
			provideMQPublisher,
			provideServices,
			provideExternalHandlers,
			provideInternalHandlers,
			provideJWTManager,
			provideConsulManager,
			provideHealthHandler,
			provideGinEngine,
		),
		// 调用生命周期管理
		fx.Invoke(
			provideLogger,
			manageLifecycle,
		),
	)

	// 运行应用
	app.Run()
}
