package main

import (
	"context"
	"os"
	"os/signal"
	"syscall"

	"pxpat-backend/cmd"
	"pxpat-backend/internal/finance-cluster/wallet-service/handler"
	"pxpat-backend/internal/finance-cluster/wallet-service/messaging"
	pkgMessaging "pxpat-backend/pkg/messaging"
	"pxpat-backend/internal/finance-cluster/wallet-service/model"
	"pxpat-backend/internal/finance-cluster/wallet-service/repository/impl"
	"pxpat-backend/internal/finance-cluster/wallet-service/routes"
	"pxpat-backend/internal/finance-cluster/wallet-service/service"
	"pxpat-backend/internal/finance-cluster/wallet-service/types"
	"pxpat-backend/pkg/auth"
	configLoader "pxpat-backend/pkg/config"
	"pxpat-backend/pkg/consul"
	"pxpat-backend/pkg/consul/health"
	databaseLoader "pxpat-backend/pkg/database"
	"pxpat-backend/pkg/logger"
	"pxpat-backend/pkg/middleware/cors"

	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog/log"
)

func main() {
	clusterName := "finance"
	serviceName := "wallet"

	// 加载配置
	cfg := configLoader.Load[types.Config](configLoader.LoaderConfig{
		TypeName:    configLoader.Service,
		ClusterName: clusterName,
		ServiceName: serviceName,
		UseRedis:    false,
	})

	// 初始化日志系统
	if err := logger.InitLogger(cfg.Log, serviceName); err != nil {
		log.Fatal().Err(err).Msg("Failed to initialize logger")
	}

	// 获取服务专用日志器
	log.Info().Msg("Wallet service starting...")

	// 初始化数据库
	db, err := databaseLoader.ConnectDB(cfg.Database)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to initialize database")
	}
	log.Info().Msg("Database connected successfully")

	// 自动迁移数据库表结构
	if err := db.AutoMigrate(
		&model.PointAccount{},
		&model.CryptoWallet{},
		&model.TokenBalance{},
		&model.AirdropRecord{},
	); err != nil {
		log.Fatal().Err(err).Msg("Database migration failed")
	}
	log.Info().Msg("Database migration completed")

	// 初始化仓储层
	pointAccountRepo := impl.NewPointAccountRepository(db)
	walletRepo := impl.NewCryptoWalletRepository(db)
	tokenBalanceRepo := impl.NewTokenBalanceRepository(db)
	airdropRepo := impl.NewAirdropRecordRepository(db)

	// 初始化服务层
	walletService := service.NewWalletService(pointAccountRepo, walletRepo, tokenBalanceRepo, airdropRepo)
	airdropService := service.NewAirdropService(walletRepo, airdropRepo)

	// 初始化处理器
	walletHandler := handler.NewWalletHandler(walletService)
	airdropHandler := handler.NewAirdropHandler(airdropService)

	// 初始化RabbitMQ消息处理
	var multiConsumer *pkgMessaging.MultiConsumer

	// 初始化消息多消费器
	if cfg.RabbitMQ.URL != "" {
		multiConsumer, err = messaging.CreateWalletServiceMultiConsumer(cfg.RabbitMQ.URL, walletService)
		if err != nil {
			log.Warn().Err(err).Msg("Failed to initialize MQ multi consumer")
			log.Info().Msg("Continuing without MQ message consumption...")
		} else {
			log.Info().Msg("MQ multi consumer initialized successfully")
			defer multiConsumer.Close()

			// 创建上下文用于优雅关闭
			ctx, cancel := context.WithCancel(context.Background())
			defer cancel()

			// 启动消息消费
			go func() {
				if err := multiConsumer.StartConsuming(ctx); err != nil {
					log.Error().Err(err).Msg("Error starting MQ multi consumer")
				}
			}()

			// 监听系统信号以优雅关闭消费者
			go func() {
				sigChan := make(chan os.Signal, 1)
				signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)
				<-sigChan
				log.Info().Msg("Received shutdown signal, stopping MQ multi consumer...")
				cancel()
			}()
		}
	} else {
		log.Info().Msg("RabbitMQ URL not configured, skipping MQ consumer initialization")
	}

	// 初始化JWT管理器
	jwtManager := auth.NewJWTManager(cfg.JWT.SecretKey, cfg.JWT.Expiration, cfg.JWT.Issuer)
	log.Info().Msg("JWT manager initialized")

	// 设置Gin模式
	gin.SetMode(cfg.Server.Mode)
	log.Info().Str("mode", cfg.Server.Mode).Msg("Gin mode set")

	// 创建路由
	router := gin.Default()

	// 添加中间件
	router.Use(cors.CORSMiddleware(cfg.Security.CORS))
	log.Info().Msg("Middleware configured")

	// 注册路由
	routes.RegisterRoutes(router, jwtManager, walletHandler, airdropHandler)
	log.Info().Msg("Routes registered")

	// 初始化Consul管理器
	consulManager, err := consul.NewManager(&cfg.Consul)
	if err != nil {
		log.Fatal().Err(err).Msg("Consul管理器初始化失败")
	}

	// 启动Consul管理器
	ctx := context.Background()
	if err := consulManager.Start(ctx); err != nil {
		log.Fatal().Err(err).Msg("Consul管理器启动失败")
	}

	// 确保在程序退出时停止Consul管理器
	defer func() {
		if err := consulManager.Stop(); err != nil {
			log.Error().Err(err).Msg("停止Consul管理器失败")
		}
	}()

	// 初始化健康检查处理器
	healthHandler := consul.NewHealthHandler(consulManager, "1.0.0")

	// 添加数据库健康检查
	healthHandler.AddChecker(health.NewDatabaseHealthChecker("database", func() error {
		sqlDB, err := db.DB()
		if err != nil {
			return err
		}
		return sqlDB.Ping()
	}))

	// 注册健康检查路由
	healthHandler.RegisterHealthRoutes(router)

	// 启动服务器
	log.Info().Int("port", cfg.Server.Port).Msg("Starting wallet service server")
	cmd.GraceStartAndClose(cfg.Server, router)
}
