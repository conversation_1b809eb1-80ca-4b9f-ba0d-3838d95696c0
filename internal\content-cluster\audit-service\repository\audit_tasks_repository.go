package repository

import (
	"context"
	"github.com/rs/zerolog/log"
	"gorm.io/gorm"
	"pxpat-backend/internal/content-cluster/audit-service/model"
)

type AuditTasksRepository struct {
	db *gorm.DB
}

func NewAuditTasksRepository(db *gorm.DB) *AuditTasksRepository {
	return &AuditTasksRepository{
		db: db,
	}
}

func (r *AuditTasksRepository) CreateAuditTask(ctx context.Context, task *model.AuditTasks) error {
	return r.db.WithContext(ctx).Create(task).Error
}

// GetAuditTaskByVideoID 根据内容ID获取审核任务
func (r *AuditTasksRepository) GetAuditTaskByVideoID(ctx context.Context, videoID uint) (*model.AuditTasks, error) {
	var task model.AuditTasks
	err := r.db.WithContext(ctx).
		Where("content_ksuid = ? AND status = ?", videoID, "pending").
		First(&task).Error
	if err != nil {
		return nil, err
	}
	return &task, nil
}

// CreateAuditUserLog 创建审核用户日志
func (r *AuditTasksRepository) CreateAuditUserLog(ctx context.Context, log *model.AuditUserLogs) error {
	return r.db.WithContext(ctx).Create(log).Error
}

// GetAuditLogsByTaskIDGroupByLevel 获取指定AuditTaskID的审核日志按Level分组
func (r *AuditTasksRepository) GetAuditLogsByTaskIDGroupByLevel(ctx context.Context, taskID uint64) (string, error) {
	log.Info().
		Uint64("task_id", taskID).
		Msg("开始获取审核日志按Level分组")

	var logs struct {
		Level string
		Count int
	}
	err := r.db.WithContext(ctx).
		Model(&model.AuditUserLogs{}).
		Select("level", "COUNT(*) AS count"). // 不要包含 id
		Where("audit_task_id = ?", taskID).
		Group("level").
		Order("count DESC").
		Limit(1).
		Scan(&logs).Error
	if err != nil {
		log.Error().
			Err(err).
			Uint64("task_id", taskID).
			Msg("获取审核日志按Level分组失败")
		return "", err
	}

	log.Info().
		Uint64("task_id", taskID).
		Str("level", logs.Level).
		Msg("获取审核日志按Level分组成功")

	return logs.Level, nil
}

// CheckUserAlreadyVoted 检查用户是否已经对该任务投过票
func (r *AuditTasksRepository) CheckUserAlreadyVoted(ctx context.Context, taskID uint64, ksUID string) (bool, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&model.AuditUserLogs{}).
		Where("audit_task_id = ? AND user_ksuid = ?", taskID, ksUID).
		Count(&count).Error
	if err != nil {
		return false, err
	}
	return count > 0, nil
}

// GetAuditTaskByID 根据ID获取审核任务（事务）
func (r *AuditTasksRepository) GetAuditTaskByID(ctx context.Context, tx *gorm.DB, taskID uint64) (*model.AuditTasks, error) {
	var task model.AuditTasks
	err := tx.WithContext(ctx).
		Set("gorm:query_option", "FOR UPDATE").
		Where("id = ?", taskID).
		First(&task).Error
	if err != nil {
		return nil, err
	}
	return &task, nil
}

// GetAuditTaskByIDWithoutTx 根据ID获取审核任务（非事务）
func (r *AuditTasksRepository) GetAuditTaskByIDWithoutTx(ctx context.Context, taskID uint64) (*model.AuditTasks, error) {
	var task model.AuditTasks
	err := r.db.WithContext(ctx).
		Preload("AuditorLogs"). // 预加载审核员记录
		Where("id = ?", taskID).
		First(&task).Error
	if err != nil {
		return nil, err
	}
	return &task, nil
}

// UpdateAuditTaskVotes 更新审核任务投票数据（事务）
func (r *AuditTasksRepository) UpdateAuditTaskVotes(ctx context.Context, tx *gorm.DB, task *model.AuditTasks) error {
	return tx.WithContext(ctx).Save(task).Error
}

// GetRandomPendingTaskByTypeExcludingUser 随机获取指定类型的待审核任务，排除用户已审核过的
func (r *AuditTasksRepository) GetRandomPendingTaskByTypeExcludingUser(ctx context.Context, contentType string, userKSUID string) (*model.AuditTasks, error) {
	var task model.AuditTasks

	// 使用子查询排除用户已经投票过的任务
	subQuery := r.db.Model(&model.AuditUserLogs{}).
		Select("audit_task_id").
		Where("user_ksuid = ?", userKSUID)

	err := r.db.WithContext(ctx).
		Preload("AuditorLogs"). // 预加载审核员记录
		Where("status = ? AND content_type = ? AND id NOT IN (?)", "pending", contentType, subQuery).
		Order("RANDOM()"). // PostgreSQL随机排序
		First(&task).Error

	if err != nil {
		return nil, err
	}
	return &task, nil
}

// GetPendingAuditTasksExcludingUser 获取用户未审核的待审核任务列表和总数
func (r *AuditTasksRepository) GetPendingAuditTasksExcludingUser(ctx context.Context, userKSUID string, contentType string, offset, limit int) ([]model.AuditTasks, int64, error) {
	var tasks []model.AuditTasks
	var total int64

	// 构建基础查询条件
	query := r.db.WithContext(ctx).Model(&model.AuditTasks{}).Where("status = ?", "pending")

	query = query.Where("content_type = ?", contentType)

	// 使用子查询排除用户已经投票过的任务
	subQuery := r.db.Model(&model.AuditUserLogs{}).
		Select("audit_task_id").
		Where("creator_ksuid = ?", userKSUID)

	query = query.Where("id NOT IN (?)", subQuery)

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取分页数据，按创建时间倒序排列（最新的在前面），并预加载审核员记录
	err := query.Preload("AuditorLogs").
		Order("created_at DESC").
		Offset(offset).
		Limit(limit).
		Find(&tasks).Error

	if err != nil {
		return nil, 0, err
	}

	return tasks, total, nil
}

// GetAuditLogsByTaskID 根据任务ID获取审核员日志
func (r *AuditTasksRepository) GetAuditLogsByTaskID(ctx context.Context, taskID uint64) ([]model.AuditUserLogs, error) {
	var logs []model.AuditUserLogs
	err := r.db.WithContext(ctx).
		Where("audit_task_id = ?", taskID).
		Order("created_at ASC").
		Find(&logs).Error
	if err != nil {
		log.Error().
			Err(err).
			Uint64("task_id", taskID).
			Msg("获取审核员日志失败")
		return nil, err
	}
	return logs, nil
}
