package repository

import (
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/redis/go-redis/v9"
	"github.com/stretchr/testify/require"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"

	"pxpat-backend/internal/content-cluster/content-management-service/model"
	"pxpat-backend/pkg/cache"
)

// TestDB 测试数据库辅助结构
type TestDB struct {
	DB    *gorm.DB
	Redis *redis.Client
	Cache cache.Manager
}

// SetupTestDB 设置测试数据库
func SetupTestDB(t *testing.T) *TestDB {
	// 创建内存SQLite数据库
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Silent), // 静默模式，避免测试时输出SQL日志
	})
	require.NoError(t, err)

	// 自动迁移测试表
	err = db.AutoMigrate(
		&model.OperationLog{},
		&model.ContentCache{},
		&model.ManagementConfig{},
	)
	require.NoError(t, err)

	// 创建测试Redis客户端（使用miniredis或mock）
	rdb := redis.NewClient(&redis.Options{
		Addr: "localhost:6379",
		DB:   15, // 使用测试专用的DB
	})

	// 测试Redis连接，如果失败则跳过需要Redis的测试
	ctx, cancel := context.WithTimeout(context.Background(), 2*time.Second)
	defer cancel()
	
	if err := rdb.Ping(ctx).Err(); err != nil {
		t.Skipf("Redis不可用，跳过需要Redis的测试: %v", err)
	}

	// 清空测试Redis数据库
	rdb.FlushDB(ctx)

	// 创建缓存管理器
	cacheManager := cache.NewRedisManager(rdb)

	return &TestDB{
		DB:    db,
		Redis: rdb,
		Cache: cacheManager,
	}
}

// TeardownTestDB 清理测试数据库
func (tdb *TestDB) TeardownTestDB() {
	if tdb.Redis != nil {
		tdb.Redis.FlushDB(context.Background())
		tdb.Redis.Close()
	}
	
	if tdb.DB != nil {
		sqlDB, _ := tdb.DB.DB()
		if sqlDB != nil {
			sqlDB.Close()
		}
	}
}

// CreateTestOperationLog 创建测试操作日志
func (tdb *TestDB) CreateTestOperationLog(userKSUID, operation string) *model.OperationLog {
	log := &model.OperationLog{
		UserKSUID:     userKSUID,
		Operation:     operation,
		ResourceType:  "content",
		ResourceKSUID: fmt.Sprintf("content_%d", time.Now().UnixNano()),
		Details: map[string]interface{}{
			"test": true,
		},
		IPAddress: "127.0.0.1",
		UserAgent: "test-agent",
		Status:    "success",
		CreatedAt: time.Now(),
	}

	result := tdb.DB.Create(log)
	if result.Error != nil {
		panic(result.Error)
	}

	return log
}

// CreateTestContentCache 创建测试内容缓存
func (tdb *TestDB) CreateTestContentCache(contentKSUID, contentType string) *model.ContentCache {
	cache := &model.ContentCache{
		ContentKSUID: contentKSUID,
		ContentType:  contentType,
		CacheData: map[string]interface{}{
			"title":       "测试内容",
			"description": "测试描述",
			"status":      "published",
		},
		ExpiresAt: time.Now().Add(5 * time.Minute),
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	result := tdb.DB.Create(cache)
	if result.Error != nil {
		panic(result.Error)
	}

	return cache
}

// CreateTestManagementConfig 创建测试管理配置
func (tdb *TestDB) CreateTestManagementConfig(key, category string, value interface{}) *model.ManagementConfig {
	config := &model.ManagementConfig{
		ConfigKey:   key,
		ConfigValue: value,
		Category:    category,
		Description: fmt.Sprintf("测试配置: %s", key),
		IsActive:    true,
		CreatedBy:   "test_user",
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	result := tdb.DB.Create(config)
	if result.Error != nil {
		panic(result.Error)
	}

	return config
}

// AssertOperationLogExists 断言操作日志存在
func (tdb *TestDB) AssertOperationLogExists(t *testing.T, userKSUID, operation string) {
	var count int64
	err := tdb.DB.Model(&model.OperationLog{}).
		Where("user_ksuid = ? AND operation = ?", userKSUID, operation).
		Count(&count).Error
	
	require.NoError(t, err)
	require.Greater(t, count, int64(0), "操作日志应该存在")
}

// AssertContentCacheExists 断言内容缓存存在
func (tdb *TestDB) AssertContentCacheExists(t *testing.T, contentKSUID string) {
	var cache model.ContentCache
	err := tdb.DB.Where("content_ksuid = ?", contentKSUID).First(&cache).Error
	require.NoError(t, err, "内容缓存应该存在")
}

// AssertManagementConfigExists 断言管理配置存在
func (tdb *TestDB) AssertManagementConfigExists(t *testing.T, configKey string) {
	var config model.ManagementConfig
	err := tdb.DB.Where("config_key = ?", configKey).First(&config).Error
	require.NoError(t, err, "管理配置应该存在")
}

// CleanupTestData 清理测试数据
func (tdb *TestDB) CleanupTestData() {
	// 清理数据库表
	tdb.DB.Exec("DELETE FROM operation_logs")
	tdb.DB.Exec("DELETE FROM content_caches")
	tdb.DB.Exec("DELETE FROM management_configs")

	// 清理Redis缓存
	if tdb.Redis != nil {
		tdb.Redis.FlushDB(context.Background())
	}
}

// MockCacheManager 模拟缓存管理器（用于不依赖Redis的测试）
type MockCacheManager struct {
	data map[string]interface{}
}

// NewMockCacheManager 创建模拟缓存管理器
func NewMockCacheManager() *MockCacheManager {
	return &MockCacheManager{
		data: make(map[string]interface{}),
	}
}

func (m *MockCacheManager) Set(ctx context.Context, key string, value interface{}, expiration time.Duration) error {
	m.data[key] = value
	return nil
}

func (m *MockCacheManager) Get(ctx context.Context, key string, dest interface{}) error {
	if value, exists := m.data[key]; exists {
		// 简单的类型断言，实际使用中可能需要更复杂的序列化/反序列化
		switch v := dest.(type) {
		case *string:
			if str, ok := value.(string); ok {
				*v = str
			}
		case *int:
			if i, ok := value.(int); ok {
				*v = i
			}
		case *map[string]interface{}:
			if m, ok := value.(map[string]interface{}); ok {
				*v = m
			}
		}
		return nil
	}
	return fmt.Errorf("key not found: %s", key)
}

func (m *MockCacheManager) Delete(ctx context.Context, key string) error {
	delete(m.data, key)
	return nil
}

func (m *MockCacheManager) Exists(ctx context.Context, key string) (bool, error) {
	_, exists := m.data[key]
	return exists, nil
}

func (m *MockCacheManager) Clear(ctx context.Context) error {
	m.data = make(map[string]interface{})
	return nil
}

func (m *MockCacheManager) Keys(ctx context.Context, pattern string) ([]string, error) {
	var keys []string
	for key := range m.data {
		keys = append(keys, key)
	}
	return keys, nil
}
