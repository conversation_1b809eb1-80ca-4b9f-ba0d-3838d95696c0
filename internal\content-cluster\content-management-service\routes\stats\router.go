package stats

import (
	"time"

	"github.com/gin-gonic/gin"

	"pxpat-backend/internal/content-cluster/content-management-service/external/handler"
	"pxpat-backend/internal/content-cluster/content-management-service/middleware"
)

// RegisterStatsRoutes 注册统计分析路由
func RegisterStatsRoutes(
	group *gin.RouterGroup,
	statsHandler *handler.StatsHandler,
	authMiddleware *middleware.AuthMiddleware,
	permissionMiddleware *middleware.PermissionMiddleware,
	rateLimitMiddleware *middleware.RateLimitMiddleware,
) {
	// 注册外部API路由
	RegisterExternalRoutes(group, statsHandler, authMiddleware, permissionMiddleware, rateLimitMiddleware)

	// 注册内部API路由
	RegisterInternalRoutes(group, statsHandler, authMiddleware, permissionMiddleware, rateLimitMiddleware)
}

// RegisterExternalRoutes 注册外部统计API路由
func RegisterExternalRoutes(
	group *gin.RouterGroup,
	statsHandler *handler.StatsHandler,
	authMiddleware *middleware.AuthMiddleware,
	permissionMiddleware *middleware.PermissionMiddleware,
	rateLimitMiddleware *middleware.RateLimitMiddleware,
) {
	// 统计分析路由组
	stats := group.Group("/stats")
	stats.Use(authMiddleware.RequireAuth())
	stats.Use(permissionMiddleware.RequirePermission("stats:read"))
	stats.Use(rateLimitMiddleware.APIRateLimit(50, time.Minute)) // 每分钟50次
	{
		// 总体统计
		overview := stats.Group("/overview")
		{
			overview.GET("", statsHandler.GetOverviewStats)
			overview.GET("/content-types", statsHandler.GetContentTypeStats)
		}

		// 用户统计
		users := stats.Group("/users")
		{
			users.GET("", statsHandler.GetUserStats)
			users.GET("/active", statsHandler.GetActiveUsers)
			users.GET("/:user_ksuid", statsHandler.GetUserStats) // 单个用户统计
		}

		// 趋势分析
		trends := stats.Group("/trends")
		{
			trends.GET("", statsHandler.GetContentTrends)
			trends.GET("/popular", statsHandler.GetPopularContents)
			trends.GET("/growth", statsHandler.GetContentTrends) // 增长趋势
		}

		// 分类统计
		categories := stats.Group("/categories")
		{
			categories.GET("", statsHandler.GetCategoryStats)
			categories.GET("/:category_id", statsHandler.GetCategoryStats) // 单个分类统计
		}

		// 标签统计
		tags := stats.Group("/tags")
		{
			tags.GET("", statsHandler.GetTagStats)
			tags.GET("/popular", statsHandler.GetTagStats) // 热门标签
		}

		// 交互统计
		interactions := stats.Group("/interactions")
		{
			interactions.GET("", statsHandler.GetInteractionStats)
			interactions.GET("/overall", statsHandler.GetOverallInteractionStats)
			interactions.GET("/content/:content_ksuid", statsHandler.GetInteractionStats) // 单个内容交互统计
		}

		// 仪表板统计
		dashboard := stats.Group("/dashboard")
		{
			dashboard.GET("", statsHandler.GetDashboardStats)
			dashboard.GET("/realtime", statsHandler.GetDashboardStats) // 实时统计
		}

		// 报告管理
		reports := stats.Group("/reports")
		{
			reports.POST("", statsHandler.GenerateReport)
			reports.GET("", statsHandler.GetReport) // 获取报告列表
			reports.GET("/:report_id", statsHandler.GetReport)
			reports.DELETE("/:report_id", statsHandler.GetReport) // 删除报告
		}

		// 按内容类型的详细统计
		videos := stats.Group("/videos")
		{
			videos.GET("", statsHandler.GetOverviewStats) // 视频统计
			videos.GET("/duration", statsHandler.GetOverviewStats) // 时长统计
			videos.GET("/resolution", statsHandler.GetOverviewStats) // 分辨率统计
		}

		// 预留小说统计路由
		novels := stats.Group("/novels")
		{
			// TODO: 实现小说统计接口
			// novels.GET("", statsHandler.GetNovelStats)
			// novels.GET("/chapters", statsHandler.GetChapterStats)
			// novels.GET("/words", statsHandler.GetWordStats)
		}

		// 预留音乐统计路由
		music := stats.Group("/music")
		{
			// TODO: 实现音乐统计接口
			// music.GET("", statsHandler.GetMusicStats)
			// music.GET("/duration", statsHandler.GetMusicDurationStats)
			// music.GET("/genres", statsHandler.GetMusicGenreStats)
		}
	}
}

// RegisterInternalRoutes 注册内部统计API路由
func RegisterInternalRoutes(
	group *gin.RouterGroup,
	statsHandler *handler.StatsHandler,
	authMiddleware *middleware.AuthMiddleware,
	permissionMiddleware *middleware.PermissionMiddleware,
	rateLimitMiddleware *middleware.RateLimitMiddleware,
) {
	// 内部统计API路由组
	internal := group.Group("/internal/stats")
	{
		// 内部服务间调用（需要服务认证）
		// TODO: 实现服务间认证中间件
		// internal.Use(serviceAuthMiddleware.RequireServiceAuth())
		{
			// 统计数据同步接口
			// internal.POST("/sync", statsHandler.SyncStats)

			// 统计缓存刷新接口
			// internal.POST("/cache/refresh", statsHandler.RefreshStatsCache)

			// 批量统计查询接口
			// internal.POST("/batch/query", statsHandler.BatchQueryStats)

			// 实时统计更新接口
			// internal.POST("/realtime/update", statsHandler.UpdateRealtimeStats)
		}
	}
}
