# Grafana数据源配置
# 自动配置Prometheus数据源

apiVersion: 1

datasources:
  # Prometheus主数据源
  - name: Prometheus
    type: prometheus
    access: proxy
    url: http://prometheus:9090
    isDefault: true
    editable: true
    jsonData:
      httpMethod: POST
      manageAlerts: true
      prometheusType: Prometheus
      prometheusVersion: 2.45.0
      cacheLevel: 'High'
      disableRecordingRules: false
      incrementalQueryOverlapWindow: 10m
      exemplarTraceIdDestinations:
        - name: trace_id
          datasourceUid: jaeger
          url: http://jaeger:16686/trace/$${__value.raw}
    secureJsonData: {}

  # Jaeger链路追踪数据源
  - name: Jaeger
    type: jaeger
    uid: jaeger
    access: proxy
    url: http://jaeger:16686
    editable: true
    jsonData:
      tracesToLogs:
        datasourceUid: 'loki'
        tags: ['job', 'instance', 'pod', 'namespace']
        mappedTags: [{ key: 'service.name', value: 'service' }]
        mapTagNamesEnabled: false
        spanStartTimeShift: '1h'
        spanEndTimeShift: '1h'
        filterByTraceID: false
        filterBySpanID: false
      tracesToMetrics:
        datasourceUid: 'prometheus'
        tags: [{ key: 'service.name', value: 'service' }, { key: 'job' }]
        queries:
          - name: 'Sample query'
            query: 'sum(rate(traces_spanmetrics_latency_bucket{$$__tags}[5m]))'
      serviceMap:
        datasourceUid: 'prometheus'
      nodeGraph:
        enabled: true
    secureJsonData: {}

  # Loki日志数据源（可选）
  - name: Loki
    type: loki
    uid: loki
    access: proxy
    url: http://loki:3100
    editable: true
    jsonData:
      derivedFields:
        - datasourceUid: jaeger
          matcherRegex: "trace_id=(\\w+)"
          name: TraceID
          url: "$${__value.raw}"
        - datasourceUid: prometheus
          matcherRegex: "user_id=(\\w+)"
          name: UserID
          url: "/explore?left=[\"now-1h\",\"now\",\"Prometheus\",{\"expr\":\"user_requests_total{user_id=\\\"$${__value.raw}\\\"}\"}]"
    secureJsonData: {}

  # TestData数据源（用于测试和演示）
  - name: TestData
    type: testdata
    uid: testdata
    access: proxy
    editable: true
    jsonData: {}
    secureJsonData: {}
