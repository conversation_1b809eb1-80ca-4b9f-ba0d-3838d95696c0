# Prometheus配置文件
# 用于监控内容管理服务和相关组件

global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    cluster: 'content-cluster'
    environment: 'production'

# 告警规则文件
rule_files:
  - "alert_rules.yml"

# 告警管理器配置
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

# 抓取配置
scrape_configs:
  # Prometheus自身监控
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
    scrape_interval: 30s
    metrics_path: /metrics

  # 内容管理服务监控
  - job_name: 'content-management-service'
    static_configs:
      - targets: ['content-management-service:12010']
    scrape_interval: 15s
    metrics_path: /metrics
    scrape_timeout: 10s
    honor_labels: true
    params:
      format: ['prometheus']
    relabel_configs:
      - source_labels: [__address__]
        target_label: __param_target
      - source_labels: [__param_target]
        target_label: instance
      - target_label: __address__
        replacement: content-management-service:12010

  # PostgreSQL监控
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres-exporter:9187']
    scrape_interval: 30s
    metrics_path: /metrics

  # Redis监控
  - job_name: 'redis'
    static_configs:
      - targets: ['redis-exporter:9121']
    scrape_interval: 30s
    metrics_path: /metrics

  # Node Exporter监控（系统指标）
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 30s
    metrics_path: /metrics

  # cAdvisor监控（容器指标）
  - job_name: 'cadvisor'
    static_configs:
      - targets: ['cadvisor:8080']
    scrape_interval: 30s
    metrics_path: /metrics

  # Consul监控
  - job_name: 'consul'
    static_configs:
      - targets: ['consul:8500']
    scrape_interval: 30s
    metrics_path: /v1/agent/metrics
    params:
      format: ['prometheus']

  # Jaeger监控
  - job_name: 'jaeger'
    static_configs:
      - targets: ['jaeger:14269']
    scrape_interval: 30s
    metrics_path: /metrics

  # 服务发现配置（从Consul自动发现服务）
  - job_name: 'consul-services'
    consul_sd_configs:
      - server: 'consul:8500'
        datacenter: 'dc1'
        services: []
    relabel_configs:
      # 只监控有prometheus标签的服务
      - source_labels: [__meta_consul_tags]
        regex: '.*,prometheus,.*'
        action: keep
      # 使用服务名作为job标签
      - source_labels: [__meta_consul_service]
        target_label: job
      # 使用服务ID作为instance标签
      - source_labels: [__meta_consul_service_id]
        target_label: instance
      # 添加环境标签
      - source_labels: [__meta_consul_datacenter]
        target_label: datacenter

# 远程写入配置（可选，用于长期存储）
# remote_write:
#   - url: "http://remote-storage:9201/write"
#     queue_config:
#       max_samples_per_send: 1000
#       max_shards: 200
#       capacity: 2500

# 远程读取配置（可选）
# remote_read:
#   - url: "http://remote-storage:9201/read"
