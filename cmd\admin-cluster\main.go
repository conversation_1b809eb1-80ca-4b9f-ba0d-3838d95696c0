package main

import (
	"github.com/gin-gonic/gin"
	"log"
	"net/http"
	"pxpat-backend/internal/admin-cluster/gateway/types"
	configLoader "pxpat-backend/pkg/config"

	"pxpat-backend/internal/admin-cluster/gateway/router"
	"pxpat-backend/internal/admin-cluster/gateway/service"
	"pxpat-backend/pkg/middleware/cors"
)

// @title Admin Monitor Gateway API
// @version 1.0
// @description 管理与监控集群网关服务
// @BasePath /api/v1

func main() {
	// 1. 加载网关配置
	cfg := configLoader.Load[types.Config](configLoader.LoaderConfig{
		TypeName:    configLoader.Gateway,
		ClusterName: "admin-monitor",
		UseRedis:    false,
	})

	// 2. 初始化服务管理器(负责启动和管理集群内服务)
	svcManager := service.NewServiceManager(cfg)

	// 3. 启动内部服务(可以是进程内启动或服务发现)
	if err := svcManager.StartServices(); err != nil {
		log.Fatalf("Failed to start intra services: %v", err)
	}

	// 4. 设置网关路由
	r := gin.Default()

	r.Use(cors.CORSMiddleware(cfg.Security.Cors))

	// 注册API路由
	router.RegisterRoutes(r, svcManager)

	// 5. 启动网关服务
	server := &http.Server{
		Addr:    cfg.Server.Address,
		Handler: r,
	}

	log.Printf("Admin Monitor Gateway started on %s", cfg.Server.Address)
	if err := server.ListenAndServe(); err != nil {
		log.Fatalf("Gateway server failed: %v", err)
	}
}
