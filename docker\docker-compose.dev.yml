# 开发环境 Docker Compose 配置
version: '3.8'

services:
  # 内容管理服务 - 开发模式
  content-management-service:
    build:
      context: ..
      dockerfile: docker/Dockerfile.content-management
      target: builder  # 使用构建阶段，便于调试
    container_name: content-management-service-dev
    restart: unless-stopped
    ports:
      - "12010:12010"
      - "2345:2345"  # Delve调试端口
    environment:
      # 开发环境配置
      - GO_ENV=development
      - GIN_MODE=debug
      
      # 数据库配置
      - DB_HOST=postgres-dev
      - DB_PORT=5432
      - DB_USER=content_user
      - DB_PASSWORD=content_password
      - DB_NAME=content_management_dev
      - DB_SSL_MODE=disable
      
      # Redis配置
      - REDIS_HOST=redis-dev
      - REDIS_PORT=6379
      - REDIS_PASSWORD=
      - REDIS_DB=0
      
      # JWT配置
      - JWT_SECRET_KEY=dev-jwt-secret-key
      - JWT_TOKEN_DURATION=24h
      - JWT_REFRESH_DURATION=168h
      
      # 外部服务配置（Mock服务）
      - VIDEO_SERVICE_URL=http://mock-video-service:12001
      - INTERACTION_SERVICE_URL=http://mock-interaction-service:12002
      
      # 日志配置
      - LOG_LEVEL=debug
      - LOG_FORMAT=text
    volumes:
      - ../:/app:cached  # 挂载源代码，支持热重载
      - ./logs:/app/logs
    depends_on:
      - postgres-dev
      - redis-dev
    networks:
      - content-dev-network
    command: ["go", "run", "./cmd/content-cluster/content-management-service/main.go"]

  # PostgreSQL 数据库 - 开发环境
  postgres-dev:
    image: postgres:15-alpine
    container_name: content-postgres-dev
    restart: unless-stopped
    environment:
      - POSTGRES_DB=content_management_dev
      - POSTGRES_USER=content_user
      - POSTGRES_PASSWORD=content_password
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
      - ./init-scripts:/docker-entrypoint-initdb.d:ro
    ports:
      - "5433:5432"  # 避免与生产环境端口冲突
    networks:
      - content-dev-network

  # Redis 缓存 - 开发环境
  redis-dev:
    image: redis:7-alpine
    container_name: content-redis-dev
    restart: unless-stopped
    command: redis-server --appendonly yes
    volumes:
      - redis_dev_data:/data
    ports:
      - "6380:6379"  # 避免与生产环境端口冲突
    networks:
      - content-dev-network

  # Mock 视频服务
  mock-video-service:
    image: mockserver/mockserver:5.15.0
    container_name: mock-video-service
    restart: unless-stopped
    environment:
      - MOCKSERVER_PROPERTY_FILE=/config/mockserver.properties
      - MOCKSERVER_INITIALIZATION_JSON_PATH=/config/video-service-expectations.json
    volumes:
      - ./mock-services:/config:ro
    ports:
      - "12001:1080"
    networks:
      - content-dev-network

  # Mock 交互服务
  mock-interaction-service:
    image: mockserver/mockserver:5.15.0
    container_name: mock-interaction-service
    restart: unless-stopped
    environment:
      - MOCKSERVER_PROPERTY_FILE=/config/mockserver.properties
      - MOCKSERVER_INITIALIZATION_JSON_PATH=/config/interaction-service-expectations.json
    volumes:
      - ./mock-services:/config:ro
    ports:
      - "12002:1080"
    networks:
      - content-dev-network

  # Redis Commander - Redis管理界面
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: redis-commander-dev
    restart: unless-stopped
    environment:
      - REDIS_HOSTS=local:redis-dev:6379
    ports:
      - "8081:8081"
    depends_on:
      - redis-dev
    networks:
      - content-dev-network

  # pgAdmin - PostgreSQL管理界面
  pgadmin:
    image: dpage/pgadmin4:7
    container_name: pgadmin-dev
    restart: unless-stopped
    environment:
      - PGADMIN_DEFAULT_EMAIL=<EMAIL>
      - PGADMIN_DEFAULT_PASSWORD=admin123
      - PGADMIN_CONFIG_SERVER_MODE=False
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    ports:
      - "8080:80"
    depends_on:
      - postgres-dev
    networks:
      - content-dev-network

  # Mailhog - 邮件测试工具
  mailhog:
    image: mailhog/mailhog:v1.0.1
    container_name: mailhog-dev
    restart: unless-stopped
    ports:
      - "1025:1025"  # SMTP端口
      - "8025:8025"  # Web界面端口
    networks:
      - content-dev-network

# 开发网络配置
networks:
  content-dev-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# 开发数据卷配置
volumes:
  postgres_dev_data:
    driver: local
  redis_dev_data:
    driver: local
  pgadmin_data:
    driver: local
