package routes

import (
	"github.com/gin-gonic/gin"
	"pxpat-backend/internal/finance-cluster/token-service/handler"
	"pxpat-backend/internal/finance-cluster/token-service/routes/analytics"
	"pxpat-backend/internal/finance-cluster/token-service/routes/governance"
	"pxpat-backend/internal/finance-cluster/token-service/routes/ranking"
	"pxpat-backend/internal/finance-cluster/token-service/routes/staking"
	"pxpat-backend/internal/finance-cluster/token-service/routes/token"
	"pxpat-backend/internal/finance-cluster/token-service/routes/transaction"
	"pxpat-backend/internal/finance-cluster/token-service/service"
	"github.com/rs/zerolog"
)

// RegisterRoutes 注册所有路由
func RegisterRoutes(
	router *gin.Engine,
	stakingService *service.StakingService,
	leaderboardService *service.LeaderboardService,
	tokenService *service.TokenService,
	governanceService *service.GovernanceService,
	transactionService *service.TransactionService,
	analyticsService *service.AnalyticsService,
	rankingService *service.RankingService,
	contentService *service.ContentService,
	websocketService *service.WebSocketService,
	syncService *service.SyncService,
	logger zerolog.Logger,
) {
	// 创建所有Handler
	httpHandler := handler.NewHTTPHandler(
		stakingService,
		leaderboardService,
		tokenService,
		governanceService,
		transactionService,
		nil, // analyticsHandler 将在下面创建
		websocketService,
		syncService,
		logger,
	)

	var tokenHandler *handler.TokenHandler
	if tokenService != nil {
		tokenHandler = handler.NewTokenHandler(tokenService, logger)
	}

	var governanceHandler *handler.GovernanceHandler
	if governanceService != nil {
		governanceHandler = handler.NewGovernanceHandler(governanceService, logger)
	}

	var transactionHandler *handler.TransactionHandler
	if transactionService != nil {
		transactionHandler = handler.NewTransactionHandler(transactionService, logger)
	}

	var analyticsHandler *handler.AnalyticsHandler
	if analyticsService != nil {
		analyticsHandler = handler.NewAnalyticsHandler(analyticsService, websocketService, logger)
	}

	var rankingHandler *handler.RankingHandler
	if rankingService != nil {
		rankingHandler = handler.NewRankingHandler(rankingService, logger)
	}

	var contentHandler *handler.ContentHandler
	if contentService != nil {
		contentHandler = handler.NewContentHandler(contentService, logger)
	}

	// 健康检查
	router.GET("/health", httpHandler.HealthCheck)
	router.GET("/metrics", httpHandler.Metrics)

	// API v1 路由组
	v1 := router.Group("/api/v1")

	// 注册各功能域路由
	staking.RegisterStakingRouter(v1, httpHandler)

	if tokenHandler != nil {
		token.RegisterTokenRouter(v1, tokenHandler)
	}

	if governanceHandler != nil {
		governance.RegisterGovernanceRouter(v1, governanceHandler)
	}

	if transactionHandler != nil {
		transaction.RegisterTransactionRouter(v1, transactionHandler)
	}

	if analyticsHandler != nil {
		analytics.RegisterAnalyticsRouter(v1, analyticsHandler)
	}

	if rankingHandler != nil {
		ranking.RegisterRankingRouter(v1, rankingHandler)
	}

	if contentHandler != nil {
		handler.RegisterContentRoutes(v1, contentHandler)
	}

	// 注册WebSocket处理器
	if websocketService != nil {
		v1.GET("/ws", websocketService.HandleWebSocket)
	}
}
