package main

import (
	"context"
	"fmt"
	"pxpat-backend/cmd"
	"pxpat-backend/internal/content-cluster/audit-service/client"
	externalHandler "pxpat-backend/internal/content-cluster/audit-service/external/handler"
	externalService "pxpat-backend/internal/content-cluster/audit-service/external/service"
	serviceHandler "pxpat-backend/internal/content-cluster/audit-service/intra/handler"
	serviceService "pxpat-backend/internal/content-cluster/audit-service/intra/service"
	publisher "pxpat-backend/internal/content-cluster/audit-service/messaging/publisher"
	"pxpat-backend/internal/content-cluster/audit-service/migrations"
	"pxpat-backend/internal/content-cluster/audit-service/repository"
	"pxpat-backend/internal/content-cluster/audit-service/routes"
	"pxpat-backend/internal/content-cluster/audit-service/types"
	"pxpat-backend/pkg/auth"
	configLoader "pxpat-backend/pkg/config"
	"pxpat-backend/pkg/consul"
	"pxpat-backend/pkg/consul/health"
	DBLoader "pxpat-backend/pkg/database"
	"pxpat-backend/pkg/logger"
	"pxpat-backend/pkg/middleware/cors"

	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog"
	"go.uber.org/fx"
	"gorm.io/gorm"

	"github.com/rs/zerolog/log"
)

// 提供配置
func provideConfig() *types.Config {
	clusterName := "content"
	serviceName := "audit"

	return configLoader.Load[types.Config](configLoader.LoaderConfig{
		TypeName:    configLoader.Service,
		ClusterName: clusterName,
		ServiceName: serviceName,
		UseRedis:    false,
	})
}

// 提供日志器
func provideLogger(cfg *types.Config) zerolog.Logger {
	serviceName := "audit"

	if err := logger.InitLogger(cfg.Log, serviceName); err != nil {
		log.Fatal().Err(err).Msg("Failed to initialize logger")
	}

	log.Info().Msg("Audit service starting...")
	return log.Logger
}

// 提供数据库连接
func provideDatabase(cfg *types.Config) (*gorm.DB, error) {
	db, err := DBLoader.ConnectDB(cfg.Database)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to connect to database")
		return nil, err
	}
	log.Info().Msg("Database connected successfully")

	// 自动创建数据库
	migrations.AutoMigrate(db)
	log.Info().Msg("Database migration completed")

	return db, nil
}

// 提供JWT管理器
func provideJWTManager(cfg *types.Config) *auth.Manager {
	jwtManager := auth.NewJWTManager(cfg.JWT.SecretKey, cfg.JWT.Expiration, cfg.JWT.Issuer)
	log.Info().Msg("JWT manager initialized")
	return &jwtManager
}

// 提供Repository
func provideAuditTasksRepository(db *gorm.DB) *repository.AuditTasksRepository {
	auditTasksRepository := repository.NewAuditTasksRepository(db)
	log.Info().Msg("Repository initialized")
	return auditTasksRepository
}

// 提供Content服务客户端
func provideContentServiceClient(cfg *types.Config) client.ContentServiceClient {
	contentServiceClient := client.NewContentServiceClient(client.ContentServiceConfig{
		BaseURL: fmt.Sprintf("http://%s:%d", cfg.Server.AllServiceList.VideoService.Host, cfg.Server.AllServiceList.VideoService.Port),
		Timeout: cfg.Server.AllServiceList.VideoService.Timeout,
	})
	log.Info().Msg("Content service client initialized")
	return contentServiceClient
}

// 提供User服务客户端
func provideUserServiceClient(cfg *types.Config) client.UserServiceClient {
	userServiceClient := client.NewUserServiceClient(client.UserServiceConfig{
		BaseURL: fmt.Sprintf("http://%s:%d", cfg.Server.AllServiceList.UserService.Host, cfg.Server.AllServiceList.UserService.Port),
		Timeout: cfg.Server.AllServiceList.UserService.Timeout,
	})
	log.Info().Msg("User service client initialized")
	return userServiceClient
}

// 提供Token服务客户端
func provideTokenServiceClient(cfg *types.Config) client.TokenServiceClient {
	tokenServiceClient := client.NewTokenServiceClient(client.TokenServiceConfig{
		BaseURL: fmt.Sprintf("http://%s:%d", cfg.Server.AllServiceList.TokenService.Host, cfg.Server.AllServiceList.TokenService.Port),
		Timeout: cfg.Server.AllServiceList.TokenService.Timeout,
	})
	log.Info().Msg("Token service client initialized")
	return tokenServiceClient
}

// 提供MQ发布器
func provideMQPublisher(cfg *types.Config) *publisher.Publisher {
	var mqPublisher *publisher.Publisher
	if cfg.RabbitMQ.URL != "" {
		var err error
		mqPublisher, err = publisher.NewPublisher(cfg.RabbitMQ.URL)
		if err != nil {
			log.Warn().Err(err).Msg("Failed to initialize MQ publisher")
			log.Info().Msg("Continuing without MQ message publishing...")
		} else {
			log.Info().Msg("MQ publisher initialized successfully")
		}
	} else {
		log.Info().Msg("RabbitMQ URL not configured, skipping MQ publisher initialization")
	}
	return mqPublisher
}

// 提供Finance服务客户端
func provideFinanceServiceClient(cfg *types.Config) client.FinanceServiceClient {
	log.Info().
		Str("points_host", cfg.Server.AllServiceList.PointsService.Host).
		Int("points_port", cfg.Server.AllServiceList.PointsService.Port).
		Dur("points_timeout", cfg.Server.AllServiceList.PointsService.Timeout).
		Msg("Points service configuration loaded")

	financeServiceClient := client.NewFinanceServiceClient(client.FinanceServiceConfig{
		BaseURL: fmt.Sprintf("http://%s:%d", cfg.Server.AllServiceList.PointsService.Host, cfg.Server.AllServiceList.PointsService.Port),
		Timeout: cfg.Server.AllServiceList.PointsService.Timeout,
	})
	return financeServiceClient
}

// 提供服务层
func provideServices(
	auditTasksRepository *repository.AuditTasksRepository,
	contentServiceClient client.ContentServiceClient,
	userServiceClient client.UserServiceClient,
	tokenServiceClient client.TokenServiceClient,
	mqPublisher *publisher.Publisher,
	db *gorm.DB,
) (*externalService.ExternalAuditTasksService, *serviceService.InternalAuditTasksService) {
	externalAuditService := externalService.NewExternalAuditTasksService(auditTasksRepository, contentServiceClient, userServiceClient, tokenServiceClient, mqPublisher, db)
	serviceAuditService := serviceService.NewInternalAuditTasksService(auditTasksRepository, contentServiceClient, mqPublisher, db)
	log.Info().Msg("Services initialized successfully")
	return externalAuditService, serviceAuditService
}

// 提供处理器层
func provideHandlers(
	externalAuditService *externalService.ExternalAuditTasksService,
	serviceAuditService *serviceService.InternalAuditTasksService,
	financeServiceClient client.FinanceServiceClient,
) (*externalHandler.AuditTasksHandler, *serviceHandler.ServiceAuditHandler) {
	externalAuditHandler := externalHandler.NewAuditTasksHandler(externalAuditService, financeServiceClient)
	serviceAuditHandler := serviceHandler.NewServiceAuditHandler(serviceAuditService)
	log.Info().Msg("Handlers initialized successfully")
	return externalAuditHandler, serviceAuditHandler
}

// 提供Consul管理器
func provideConsulManager(cfg *types.Config) (*consul.Manager, error) {
	consulManager, err := consul.NewManager(&cfg.Consul)
	if err != nil {
		log.Fatal().Err(err).Msg("Consul管理器初始化失败")
		return nil, err
	}
	return consulManager, nil
}

// 提供健康检查处理器
func provideHealthHandler(consulManager *consul.Manager, db *gorm.DB) *consul.HealthHandler {
	healthHandler := consul.NewHealthHandler(consulManager, "1.0.0")

	// 添加数据库健康检查
	healthHandler.AddChecker(health.NewDatabaseHealthChecker("database", func() error {
		sqlDB, err := db.DB()
		if err != nil {
			return err
		}
		return sqlDB.Ping()
	}))

	return healthHandler
}

// 提供Gin引擎
func provideGinEngine(
	cfg *types.Config,
	jwtManager *auth.Manager,
	externalAuditHandler *externalHandler.AuditTasksHandler,
	serviceAuditHandler *serviceHandler.ServiceAuditHandler,
	healthHandler *consul.HealthHandler,
) *gin.Engine {
	// 设置Gin模式
	gin.SetMode(cfg.Server.Mode)
	log.Info().Str("mode", cfg.Server.Mode).Msg("Gin mode set")

	// 创建服务
	router := gin.Default()
	router.Use(cors.CORSMiddleware(cfg.Security.Cors))
	log.Info().Msg("Middleware configured")

	routes.RegisterRoutes(router, jwtManager, externalAuditHandler, serviceAuditHandler)
	log.Info().Msg("Routes registered")

	// 注册健康检查路由
	healthHandler.RegisterHealthRoutes(router)

	return router
}

// 应用生命周期管理
func manageLifecycle(
	lc fx.Lifecycle,
	cfg *types.Config,
	consulManager *consul.Manager,
	mqPublisher *publisher.Publisher,
	ginEngine *gin.Engine,
) {
	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) error {
			// 启动Consul管理器
			if err := consulManager.Start(ctx); err != nil {
				log.Fatal().Err(err).Msg("Consul管理器启动失败")
				return err
			}
			log.Info().Msg("Consul管理器启动成功")

			// 启动HTTP服务器（在goroutine中）
			go func() {
				log.Info().Int("port", cfg.Server.Port).Msg("Starting audit service server")
				cmd.GraceStartAndClose(cfg.Server, ginEngine)
			}()

			return nil
		},
		OnStop: func(ctx context.Context) error {
			// 停止Consul管理器
			if err := consulManager.Stop(); err != nil {
				log.Error().Err(err).Msg("停止Consul管理器失败")
			} else {
				log.Info().Msg("Consul管理器停止成功")
			}

			// 关闭MQ发布器
			if mqPublisher != nil {
				if err := mqPublisher.Close(); err != nil {
					log.Error().Err(err).Msg("关闭MQ发布器失败")
				} else {
					log.Info().Msg("MQ发布器关闭成功")
				}
			}

			return nil
		},
	})
}

func main() {
	app := fx.New(
		// 提供依赖
		fx.Provide(
			provideConfig,
			provideDatabase,
			provideJWTManager,
			provideAuditTasksRepository,
			provideContentServiceClient,
			provideUserServiceClient,
			provideMQPublisher,
			provideFinanceServiceClient,
			provideServices,
			provideHandlers,
			provideConsulManager,
			provideHealthHandler,
			provideGinEngine,
		),
		// 调用生命周期管理
		fx.Invoke(
			provideLogger,
			manageLifecycle,
		),
	)

	// 运行应用
	app.Run()
}
