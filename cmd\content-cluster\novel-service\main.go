package main

import (
	"context"
	"fmt"
	"github.com/robfig/cron/v3"
	"time"

	"pxpat-backend/cmd"
	"pxpat-backend/internal/content-cluster/novel-service/client"
	retryCron "pxpat-backend/internal/content-cluster/novel-service/cron"
	"pxpat-backend/internal/content-cluster/novel-service/external/service"
	intraService "pxpat-backend/internal/content-cluster/novel-service/intra/service"
	"pxpat-backend/internal/content-cluster/novel-service/messaging"
	"pxpat-backend/internal/content-cluster/novel-service/messaging/publisher"
	"pxpat-backend/internal/content-cluster/novel-service/middleware"
	"pxpat-backend/internal/content-cluster/novel-service/migrations"
	"pxpat-backend/internal/content-cluster/novel-service/repository"
	"pxpat-backend/internal/content-cluster/novel-service/routes"
	"pxpat-backend/internal/content-cluster/novel-service/types"
	"pxpat-backend/internal/content-cluster/novel-service/utils"
	"pxpat-backend/pkg/auth"
	"pxpat-backend/pkg/cache"
	configLoader "pxpat-backend/pkg/config"
	"pxpat-backend/pkg/consul"
	"pxpat-backend/pkg/consul/health"
	DBLoader "pxpat-backend/pkg/database"
	"pxpat-backend/pkg/logger"
	"pxpat-backend/pkg/middleware/cors"
	"pxpat-backend/pkg/storage"

	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
	"github.com/rs/zerolog"
	"github.com/rs/zerolog/log"
	"go.uber.org/fx"
	"gorm.io/gorm"
)

// 提供配置
func provideConfig() *types.Config {
	clusterName := "content"
	serviceName := "novel"

	return configLoader.Load[types.Config](configLoader.LoaderConfig{
		TypeName:    configLoader.Service,
		ClusterName: clusterName,
		ServiceName: serviceName,
		UseRedis:    true,
	})
}

// 提供日志器
func provideLogger(cfg *types.Config) {
	serviceName := "novel"

	if err := logger.InitLogger(cfg.Log, serviceName); err != nil {
		log.Fatal().Err(err).Msg("Failed to initialize logger")
	}

	log.Info().Msg("Novel service starting...")
}

// 提供数据库连接
func provideDatabase(cfg *types.Config) (*gorm.DB, error) {
	db, err := DBLoader.ConnectDB(cfg.Database)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to initialize database")
		return nil, err
	}
	log.Info().Msg("Database connected successfully")

	err = migrations.Migrate(db)
	if err != nil {
		log.Fatal().Err(err).Msg("Database migration failed")
		return nil, err
	}
	log.Info().Msg("Database migration completed")

	return db, nil
}

// 提供Redis连接
func provideRedis(cfg *types.Config) (*redis.Client, error) {
	rdb, err := DBLoader.ConnectRedis(cfg.Redis)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to initialize Redis")
		return nil, err
	}
	log.Info().Msg("Redis connected successfully")
	return rdb, nil
}

// 提供缓存管理器
func provideCacheManager(rdb *redis.Client) (cache.Manager, error) {
	cacheManager, err := cache.NewManager(rdb)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to initialize cache manager")
		return nil, err
	}
	log.Info().Msg("Cache manager initialized")
	return cacheManager, nil
}

// 提供用户服务客户端
func provideUserServiceClient(cfg *types.Config) client.UserServiceClient {
	userServiceClient := client.NewUserServiceClient(client.UserServiceConfig{
		BaseURL: fmt.Sprintf("http://%s:%d", cfg.Server.AllServiceList.UserService.Host, cfg.Server.AllServiceList.UserService.Port),
		Timeout: cfg.Server.AllServiceList.UserService.Timeout,
	})
	log.Info().Msg("User service client initialized")
	return userServiceClient
}

// 提供Repository层
func provideRepositories(db *gorm.DB) (
	repository.NovelRepository,
	repository.ChapterRepository,
	repository.CategoryRepository,
	repository.TagRepository,
	repository.NovelCommentRepository,
	repository.ChapterCommentRepository,
	repository.DonationRepository,
) {
	novelRepo := repository.NewNovelRepository(db)
	chapterRepo := repository.NewChapterRepository(db)
	categoryRepo := repository.NewCategoryRepository(db)
	tagRepo := repository.NewTagRepository(db)
	novelCommentRepo := repository.NewNovelCommentRepository(db)
	chapterCommentRepo := repository.NewChapterCommentRepository(db)
	donationRepo := repository.NewDonationRepository(db)

	log.Info().Msg("Repositories initialized")
	return novelRepo, chapterRepo, categoryRepo, tagRepo, novelCommentRepo, chapterCommentRepo, donationRepo
}

// 提供存储客户端
func provideStorageClient(cfg *types.Config) storage.StorageClient {
	var storageClient storage.StorageClient
	if cfg.Storage.Minio.Provider != "" {
		var err error
		storageClient, err = storage.NewStorageClient(cfg.Storage.Minio)
		if err != nil {
			log.Warn().Err(err).Msg("Failed to initialize storage client")
			log.Info().Msg("Continuing without storage client...")
		} else {
			log.Info().Str("provider", cfg.Storage.Minio.Provider).Msg("Storage client initialized successfully")
		}
	} else {
		log.Info().Msg("Storage provider not configured, skipping storage client initialization")
	}
	return storageClient
}

// 提供MQ发布器
func provideMQPublisher(cfg *types.Config) *publisher.Publisher {
	var mqPublisher *publisher.Publisher
	if cfg.RabbitMQ.URL != "" {
		var err error
		mqPublisher, err = publisher.NewPublisher(cfg.RabbitMQ.URL)
		if err != nil {
			log.Warn().Err(err).Msg("Failed to initialize MQ publisher")
			log.Info().Msg("Continuing without MQ message publishing...")
		} else {
			log.Info().Msg("MQ publisher initialized successfully")
		}
	} else {
		log.Info().Msg("RabbitMQ URL not configured, skipping MQ publisher initialization")
	}
	return mqPublisher
}

// 提供内容存储服务客户端
func provideContentStorageServiceClient(cfg *types.Config, logger zerolog.Logger) client.ContentStorageServiceClient {
	logger.Info().
		Str("storage_host", cfg.Server.AllServiceList.ContentStorageService.Host).
		Int("storage_port", cfg.Server.AllServiceList.ContentStorageService.Port).
		Dur("storage_timeout", cfg.Server.AllServiceList.ContentStorageService.Timeout).
		Msg("Storage service configuration loaded")

	contentStorageServiceConfig := client.ContentStorageServiceConfig{
		BaseURL: fmt.Sprintf("http://%s:%d", cfg.Server.AllServiceList.ContentStorageService.Host, cfg.Server.AllServiceList.ContentStorageService.Port),
		Timeout: cfg.Server.AllServiceList.ContentStorageService.Timeout,
	}

	contentStorageClient := client.NewContentStorageServiceClient(contentStorageServiceConfig)
	return contentStorageClient
}

// 提供支付服务客户端
func providePaymentServiceClient(cfg *types.Config, logger zerolog.Logger) client.PaymentServiceClient {
	var paymentServiceClient client.PaymentServiceClient
	if cfg.Payment.Enabled {
		paymentServiceClient = client.NewPaymentServiceClient(client.PaymentServiceConfig{
			BaseURL: fmt.Sprintf("http://%s:%d", cfg.Server.AllServiceList.PointsService.Host, cfg.Server.AllServiceList.PointsService.Port),
			Timeout: cfg.Server.AllServiceList.PointsService.Timeout,
		})
		logger.Info().Msg("Payment service client initialized")
	} else {
		logger.Info().Msg("Payment service disabled, skipping payment client initialization")
	}
	return paymentServiceClient
}

// 提供审核服务客户端
func provideAuditServiceClient(cfg *types.Config, logger zerolog.Logger) client.AuditServiceClient {
	auditServiceClient := client.NewAuditServiceClient(client.AuditServiceConfig{
		BaseURL: fmt.Sprintf("http://%s:%d", cfg.Server.AllServiceList.AuditService.Host, cfg.Server.AllServiceList.AuditService.Port),
		Timeout: 30 * time.Second,
	})
	logger.Info().Msg("Audit service client initialized")
	return auditServiceClient
}

// 提供服务层
func provideServices(
	novelRepo repository.NovelRepository,
	chapterRepo repository.ChapterRepository,
	categoryRepo repository.CategoryRepository,
	tagRepo repository.TagRepository,
	novelCommentRepo repository.NovelCommentRepository,
	chapterCommentRepo repository.ChapterCommentRepository,
	userServiceClient client.UserServiceClient,
	contentStorageClient client.ContentStorageServiceClient,
	auditServiceClient client.AuditServiceClient,
	paymentServiceClient client.PaymentServiceClient,
	logger zerolog.Logger,
) (
	*service.NovelService,
	*service.ChapterService,
	*service.CategoryService,
	*service.TagService,
	*service.NovelCommentService,
	*service.ChapterCommentService,
) {
	novelService := service.NewNovelService(novelRepo, chapterRepo, categoryRepo, tagRepo, userServiceClient, contentStorageClient, auditServiceClient)
	chapterService := service.NewChapterService(chapterRepo, novelRepo, userServiceClient, contentStorageClient, auditServiceClient, paymentServiceClient)
	categoryService := service.NewCategoryService(categoryRepo)
	tagService := service.NewTagService(tagRepo)
	novelCommentService := service.NewNovelCommentService(novelCommentRepo, novelRepo)
	chapterCommentService := service.NewChapterCommentService(chapterCommentRepo, chapterRepo, novelRepo)

	logger.Info().Msg("Services initialized successfully")
	return novelService, chapterService, categoryService, tagService, novelCommentService, chapterCommentService
}

// 提供JWT管理器
func provideJWTManager(cfg *types.Config, logger zerolog.Logger) *auth.Manager {
	jwtManager := auth.NewJWTManager(cfg.JWT.SecretKey, cfg.JWT.Expiration, cfg.JWT.Issuer)
	logger.Info().Msg("JWT manager initialized")
	return &jwtManager
}

// 提供权限中间件
func providePermissionMiddleware(jwtManager *auth.Manager, logger zerolog.Logger) *middleware.PermissionMiddleware {
	permissionMiddleware := middleware.NewPermissionMiddleware(jwtManager)
	logger.Info().Msg("Permission middleware initialized")
	return permissionMiddleware
}

// 提供内部服务层
func provideInternalServices(
	novelRepo repository.NovelRepository,
	chapterRepo repository.ChapterRepository,
	categoryRepo repository.CategoryRepository,
	tagRepo repository.TagRepository,
	logger zerolog.Logger,
) (
	*intraService.NovelService,
	*intraService.ChapterService,
	*intraService.CategoryService,
	*intraService.TagService,
) {
	internalNovelService := intraService.NewNovelService(novelRepo, chapterRepo)
	internalChapterService := intraService.NewChapterService(chapterRepo, novelRepo)
	internalCategoryService := intraService.NewCategoryService(categoryRepo)
	internalTagService := intraService.NewTagService(tagRepo)

	logger.Info().Msg("Internal services initialized")
	return internalNovelService, internalChapterService, internalCategoryService, internalTagService
}

// 提供定时任务管理器
func provideCronManager(
	cfg *types.Config,
	donationRepo repository.DonationRepository,
	chapterRepo repository.ChapterRepository,
	cacheManager cache.Manager,
	logger zerolog.Logger,
) *cron.Cron {
	cronManager := cron.New(cron.WithSeconds())

	// 初始化打赏排行榜更新定时任务
	if cfg.Novel.Donation.Enabled && cfg.Novel.Donation.RankEnabled {
		donationRankCron := retryCron.NewDonationRankCron(donationRepo)

		// 每小时更新一次排行榜
		_, err := cronManager.AddFunc(cfg.Novel.Donation.RankUpdateCron, donationRankCron.UpdateRanks)
		if err != nil {
			logger.Error().Err(err).Msg("添加打赏排行榜更新定时任务失败")
		} else {
			logger.Info().Msg("打赏排行榜更新定时任务添加成功")
		}
	}

	// 每天清理过期的阅读记录缓存
	readRecordCleanupCron := retryCron.NewReadRecordCleanupCron(chapterRepo, cacheManager)
	_, err := cronManager.AddFunc("@daily", readRecordCleanupCron.CleanupExpiredRecords)
	if err != nil {
		logger.Error().Err(err).Msg("添加阅读记录清理定时任务失败")
	} else {
		logger.Info().Msg("阅读记录清理定时任务添加成功")
	}

	logger.Info().Msg("定时任务管理器初始化完成")
	return cronManager
}

// 提供Consul管理器
func provideConsulManager(cfg *types.Config, logger zerolog.Logger) (*consul.Manager, error) {
	consulManager, err := consul.NewManager(&cfg.Consul)
	if err != nil {
		logger.Fatal().Err(err).Msg("Consul管理器初始化失败")
		return nil, err
	}
	return consulManager, nil
}

// 提供健康检查处理器
func provideHealthHandler(consulManager *consul.Manager, db *gorm.DB, logger zerolog.Logger) *consul.HealthHandler {
	consulHealthHandler := consul.NewHealthHandler(consulManager, "1.0.0")

	// 添加数据库健康检查
	consulHealthHandler.AddChecker(health.NewDatabaseHealthChecker("database", func() error {
		sqlDB, err := db.DB()
		if err != nil {
			return err
		}
		return sqlDB.Ping()
	}))

	return consulHealthHandler
}

// 提供Gin引擎
func provideGinEngine(
	cfg *types.Config,
	novelService *service.NovelService,
	chapterService *service.ChapterService,
	categoryService *service.CategoryService,
	tagService *service.TagService,
	novelCommentService *service.NovelCommentService,
	chapterCommentService *service.ChapterCommentService,
	jwtManager *auth.Manager,
	internalNovelService *intraService.NovelService,
	internalChapterService *intraService.ChapterService,
	internalCategoryService *intraService.CategoryService,
	internalTagService *intraService.TagService,
	healthHandler *consul.HealthHandler,
	db *gorm.DB,
	rdb *redis.Client,
	logger zerolog.Logger,
) *gin.Engine {
	// 设置Gin模式
	gin.SetMode(cfg.Server.Mode)
	logger.Info().Str("mode", cfg.Server.Mode).Msg("Gin mode set")

	// 创建路由
	router := gin.New()

	// 添加改进的中间件
	router.Use(middleware.ErrorHandler())  // 统一错误处理
	router.Use(middleware.RequestLogger()) // 请求日志
	router.Use(cors.CORSMiddleware(cfg.Security.CORS))
	logger.Info().Msg("Enhanced middleware configured")

	// 注册路由
	routes.RegisterRoutes(
		router,
		novelService,
		chapterService,
		categoryService,
		tagService,
		novelCommentService,
		chapterCommentService,
		jwtManager,
		internalNovelService,
		internalChapterService,
		internalCategoryService,
		internalTagService,
	)
	logger.Info().Msg("Routes registered")

	// 设置健康检查
	healthChecker := utils.NewHealthChecker()
	healthChecker.AddCheck("database", func() error {
		sqlDB, err := db.DB()
		if err != nil {
			return err
		}
		return sqlDB.Ping()
	})
	healthChecker.AddCheck("redis", func() error {
		return rdb.Ping(context.Background()).Err()
	})

	// 添加健康检查路由
	router.GET("/health", func(c *gin.Context) {
		results := healthChecker.CheckHealth()
		healthy := healthChecker.IsHealthy()

		status := 200
		if !healthy {
			status = 503
		}

		c.JSON(status, gin.H{
			"status":  map[bool]string{true: "healthy", false: "unhealthy"}[healthy],
			"checks":  results,
			"service": "novel-service",
		})
	})

	// 注册健康检查路由
	healthHandler.RegisterHealthRoutes(router)

	return router
}

// 应用生命周期管理
func manageLifecycle(
	lc fx.Lifecycle,
	cfg *types.Config,
	consulManager *consul.Manager,
	cronManager *cron.Cron,
	mqPublisher *publisher.Publisher,
	novelRepo repository.NovelRepository,
	chapterRepo repository.ChapterRepository,
	db *gorm.DB,
	rdb *redis.Client,
	ginEngine *gin.Engine,
) {
	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) error {
			// 启动Consul管理器
			if err := consulManager.Start(ctx); err != nil {
				log.Fatal().Err(err).Msg("Consul管理器启动失败")
				return err
			}
			log.Info().Msg("Consul管理器启动成功")

			// 启动定时任务
			cronManager.Start()
			log.Info().Msg("定时任务启动成功")

			// 初始化RabbitMQ多消费者（用于接收审核结果等）
			if cfg.RabbitMQ.URL != "" {
				go func() {
					// 创建多消费者
					multiConsumer, err := messaging.CreateNovelServiceMultiConsumer(cfg.RabbitMQ.URL, novelRepo, chapterRepo)
					if err != nil {
						log.Warn().Err(err).Msg("Failed to initialize MQ multi consumer")
						log.Info().Msg("Continuing without MQ message consumption...")
					} else if multiConsumer != nil {
						log.Info().Msg("MQ multi consumer initialized successfully")
						defer multiConsumer.Close()

						// 启动消费者
						if err := multiConsumer.StartConsuming(ctx); err != nil {
							log.Error().Err(err).Msg("Error starting MQ multi consumer")
						}
					} else {
						log.Info().Msg("MQ multi consumer not implemented yet")
					}
				}()
			} else {
				log.Info().Msg("RabbitMQ URL not configured, skipping MQ consumer initialization")
			}

			// 启动HTTP服务器（在goroutine中）
			go func() {
				log.Info().Int("port", cfg.Server.Port).Msg("Starting novel service server with enhanced error handling")
				cmd.GraceStartAndClose(cfg.Server, ginEngine)
			}()

			return nil
		},
		OnStop: func(ctx context.Context) error {
			// 停止定时任务
			cronManager.Stop()
			log.Info().Msg("定时任务停止成功")

			// 停止Consul管理器
			if err := consulManager.Stop(); err != nil {
				log.Error().Err(err).Msg("停止Consul管理器失败")
			} else {
				log.Info().Msg("Consul管理器停止成功")
			}

			// 关闭数据库连接
			if sqlDB, err := db.DB(); err == nil {
				if err := sqlDB.Close(); err != nil {
					log.Error().Err(err).Msg("关闭数据库连接失败")
				} else {
					log.Info().Msg("数据库连接关闭成功")
				}
			}

			// 关闭Redis连接
			if err := rdb.Close(); err != nil {
				log.Error().Err(err).Msg("关闭Redis连接失败")
			} else {
				log.Info().Msg("Redis连接关闭成功")
			}

			// 关闭MQ发布器
			if mqPublisher != nil {
				if err := mqPublisher.Close(); err != nil {
					log.Error().Err(err).Msg("关闭MQ发布器失败")
				} else {
					log.Info().Msg("MQ发布器关闭成功")
				}
			}

			return nil
		},
	})
}

func main() {
	app := fx.New(
		// 提供依赖
		fx.Provide(
			provideConfig,
			provideDatabase,
			provideRedis,
			provideCacheManager,
			provideUserServiceClient,
			provideRepositories,
			provideStorageClient,
			provideMQPublisher,
			provideContentStorageServiceClient,
			providePaymentServiceClient,
			provideAuditServiceClient,
			provideServices,
			provideJWTManager,
			providePermissionMiddleware,
			provideInternalServices,
			provideCronManager,
			provideConsulManager,
			provideHealthHandler,
			provideGinEngine,
		),
		// 调用生命周期管理
		fx.Invoke(
			provideLogger,
			manageLifecycle,
		),
	)

	// 运行应用
	app.Run()
}
