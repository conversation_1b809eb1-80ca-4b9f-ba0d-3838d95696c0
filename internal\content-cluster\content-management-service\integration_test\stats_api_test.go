package integration_test

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"pxpat-backend/pkg/errors"
	globalTypes "pxpat-backend/pkg/types"
)

func TestStatsAPI_GetOverviewStats(t *testing.T) {
	server := SetupTestServer(t)
	defer server.TeardownTestServer()

	// 生成测试JWT Token
	token, err := server.GenerateTestJWT("user_1", "admin")
	require.NoError(t, err)

	tests := []struct {
		name           string
		url            string
		useAuth        bool
		expectedStatus int
		expectedCode   int
	}{
		{
			name:           "获取总体统计-有认证",
			url:            "/api/v1/management/stats/overview",
			useAuth:        true,
			expectedStatus: http.StatusOK,
			expectedCode:   errors.SUCCESS,
		},
		{
			name:           "获取总体统计-无认证",
			url:            "/api/v1/management/stats/overview",
			useAuth:        false,
			expectedStatus: http.StatusUnauthorized,
			expectedCode:   errors.UNAUTHORIZED,
		},
		{
			name:           "获取内容类型统计",
			url:            "/api/v1/management/stats/overview/content-types",
			useAuth:        true,
			expectedStatus: http.StatusOK,
			expectedCode:   errors.SUCCESS,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req, err := http.NewRequest("GET", server.GetBaseURL()+tt.url, nil)
			require.NoError(t, err)

			if tt.useAuth {
				req.Header.Set("Authorization", "Bearer "+token)
			}

			client := &http.Client{}
			resp, err := client.Do(req)
			require.NoError(t, err)
			defer resp.Body.Close()

			assert.Equal(t, tt.expectedStatus, resp.StatusCode)

			var response globalTypes.GlobalResponse
			err = json.NewDecoder(resp.Body).Decode(&response)
			require.NoError(t, err)

			assert.Equal(t, tt.expectedCode, response.Code)

			if tt.expectedStatus == http.StatusOK {
				assert.NotNil(t, response.Data)
			}
		})
	}
}

func TestStatsAPI_GetUserStats(t *testing.T) {
	server := SetupTestServer(t)
	defer server.TeardownTestServer()

	// 生成测试JWT Token
	token, err := server.GenerateTestJWT("user_1", "admin")
	require.NoError(t, err)

	tests := []struct {
		name           string
		url            string
		expectedStatus int
		expectedCode   int
	}{
		{
			name:           "获取用户统计",
			url:            "/api/v1/management/stats/users",
			expectedStatus: http.StatusOK,
			expectedCode:   errors.SUCCESS,
		},
		{
			name:           "获取活跃用户统计",
			url:            "/api/v1/management/stats/users/active",
			expectedStatus: http.StatusOK,
			expectedCode:   errors.SUCCESS,
		},
		{
			name:           "获取特定用户统计",
			url:            "/api/v1/management/stats/users/user_123",
			expectedStatus: http.StatusOK,
			expectedCode:   errors.SUCCESS,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req, err := http.NewRequest("GET", server.GetBaseURL()+tt.url, nil)
			require.NoError(t, err)
			req.Header.Set("Authorization", "Bearer "+token)

			client := &http.Client{}
			resp, err := client.Do(req)
			require.NoError(t, err)
			defer resp.Body.Close()

			assert.Equal(t, tt.expectedStatus, resp.StatusCode)

			var response globalTypes.GlobalResponse
			err = json.NewDecoder(resp.Body).Decode(&response)
			require.NoError(t, err)

			assert.Equal(t, tt.expectedCode, response.Code)
			assert.NotNil(t, response.Data)
		})
	}
}

func TestStatsAPI_GetTrendStats(t *testing.T) {
	server := SetupTestServer(t)
	defer server.TeardownTestServer()

	// 生成测试JWT Token
	token, err := server.GenerateTestJWT("user_1", "admin")
	require.NoError(t, err)

	tests := []struct {
		name           string
		url            string
		expectedStatus int
		expectedCode   int
	}{
		{
			name:           "获取内容趋势",
			url:            "/api/v1/management/stats/trends",
			expectedStatus: http.StatusOK,
			expectedCode:   errors.SUCCESS,
		},
		{
			name:           "获取热门内容",
			url:            "/api/v1/management/stats/trends/popular",
			expectedStatus: http.StatusOK,
			expectedCode:   errors.SUCCESS,
		},
		{
			name:           "获取增长趋势",
			url:            "/api/v1/management/stats/trends/growth",
			expectedStatus: http.StatusOK,
			expectedCode:   errors.SUCCESS,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req, err := http.NewRequest("GET", server.GetBaseURL()+tt.url, nil)
			require.NoError(t, err)
			req.Header.Set("Authorization", "Bearer "+token)

			client := &http.Client{}
			resp, err := client.Do(req)
			require.NoError(t, err)
			defer resp.Body.Close()

			assert.Equal(t, tt.expectedStatus, resp.StatusCode)

			var response globalTypes.GlobalResponse
			err = json.NewDecoder(resp.Body).Decode(&response)
			require.NoError(t, err)

			assert.Equal(t, tt.expectedCode, response.Code)
			assert.NotNil(t, response.Data)
		})
	}
}

func TestStatsAPI_GetInteractionStats(t *testing.T) {
	server := SetupTestServer(t)
	defer server.TeardownTestServer()

	// 生成测试JWT Token
	token, err := server.GenerateTestJWT("user_1", "admin")
	require.NoError(t, err)

	tests := []struct {
		name           string
		url            string
		expectedStatus int
		expectedCode   int
	}{
		{
			name:           "获取交互统计",
			url:            "/api/v1/management/stats/interactions",
			expectedStatus: http.StatusOK,
			expectedCode:   errors.SUCCESS,
		},
		{
			name:           "获取整体交互统计",
			url:            "/api/v1/management/stats/interactions/overall",
			expectedStatus: http.StatusOK,
			expectedCode:   errors.SUCCESS,
		},
		{
			name:           "获取特定内容交互统计",
			url:            "/api/v1/management/stats/interactions/content/video_123",
			expectedStatus: http.StatusOK,
			expectedCode:   errors.SUCCESS,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req, err := http.NewRequest("GET", server.GetBaseURL()+tt.url, nil)
			require.NoError(t, err)
			req.Header.Set("Authorization", "Bearer "+token)

			client := &http.Client{}
			resp, err := client.Do(req)
			require.NoError(t, err)
			defer resp.Body.Close()

			assert.Equal(t, tt.expectedStatus, resp.StatusCode)

			var response globalTypes.GlobalResponse
			err = json.NewDecoder(resp.Body).Decode(&response)
			require.NoError(t, err)

			assert.Equal(t, tt.expectedCode, response.Code)
			assert.NotNil(t, response.Data)
		})
	}
}

func TestStatsAPI_GetDashboardStats(t *testing.T) {
	server := SetupTestServer(t)
	defer server.TeardownTestServer()

	// 生成测试JWT Token
	token, err := server.GenerateTestJWT("user_1", "admin")
	require.NoError(t, err)

	tests := []struct {
		name           string
		url            string
		expectedStatus int
		expectedCode   int
	}{
		{
			name:           "获取仪表板统计",
			url:            "/api/v1/management/stats/dashboard",
			expectedStatus: http.StatusOK,
			expectedCode:   errors.SUCCESS,
		},
		{
			name:           "获取实时统计",
			url:            "/api/v1/management/stats/dashboard/realtime",
			expectedStatus: http.StatusOK,
			expectedCode:   errors.SUCCESS,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req, err := http.NewRequest("GET", server.GetBaseURL()+tt.url, nil)
			require.NoError(t, err)
			req.Header.Set("Authorization", "Bearer "+token)

			client := &http.Client{}
			resp, err := client.Do(req)
			require.NoError(t, err)
			defer resp.Body.Close()

			assert.Equal(t, tt.expectedStatus, resp.StatusCode)

			var response globalTypes.GlobalResponse
			err = json.NewDecoder(resp.Body).Decode(&response)
			require.NoError(t, err)

			assert.Equal(t, tt.expectedCode, response.Code)
			assert.NotNil(t, response.Data)
		})
	}
}

func TestStatsAPI_ReportManagement(t *testing.T) {
	server := SetupTestServer(t)
	defer server.TeardownTestServer()

	// 生成测试JWT Token
	token, err := server.GenerateTestJWT("user_1", "admin")
	require.NoError(t, err)

	// 测试生成报告
	t.Run("生成报告", func(t *testing.T) {
		requestBody := map[string]interface{}{
			"report_type": "content_overview",
			"start_time":  "2024-01-01T00:00:00Z",
			"end_time":    "2024-01-31T23:59:59Z",
			"parameters": map[string]interface{}{
				"content_types": []string{"video"},
			},
		}

		jsonBody, _ := json.Marshal(requestBody)
		req, err := http.NewRequest("POST", server.GetBaseURL()+"/api/v1/management/stats/reports", bytes.NewBuffer(jsonBody))
		require.NoError(t, err)
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("Authorization", "Bearer "+token)

		client := &http.Client{}
		resp, err := client.Do(req)
		require.NoError(t, err)
		defer resp.Body.Close()

		assert.Equal(t, http.StatusOK, resp.StatusCode)

		var response globalTypes.GlobalResponse
		err = json.NewDecoder(resp.Body).Decode(&response)
		require.NoError(t, err)

		assert.Equal(t, errors.SUCCESS, response.Code)
		assert.NotNil(t, response.Data)
	})

	// 测试获取报告列表
	t.Run("获取报告列表", func(t *testing.T) {
		req, err := http.NewRequest("GET", server.GetBaseURL()+"/api/v1/management/stats/reports", nil)
		require.NoError(t, err)
		req.Header.Set("Authorization", "Bearer "+token)

		client := &http.Client{}
		resp, err := client.Do(req)
		require.NoError(t, err)
		defer resp.Body.Close()

		assert.Equal(t, http.StatusOK, resp.StatusCode)

		var response globalTypes.GlobalResponse
		err = json.NewDecoder(resp.Body).Decode(&response)
		require.NoError(t, err)

		assert.Equal(t, errors.SUCCESS, response.Code)
		assert.NotNil(t, response.Data)
	})

	// 测试获取特定报告
	t.Run("获取特定报告", func(t *testing.T) {
		reportID := "report_123"
		url := fmt.Sprintf("%s/api/v1/management/stats/reports/%s", server.GetBaseURL(), reportID)
		
		req, err := http.NewRequest("GET", url, nil)
		require.NoError(t, err)
		req.Header.Set("Authorization", "Bearer "+token)

		client := &http.Client{}
		resp, err := client.Do(req)
		require.NoError(t, err)
		defer resp.Body.Close()

		// 由于是模拟数据，可能返回404或200，这里主要测试API可访问性
		assert.True(t, resp.StatusCode == http.StatusOK || resp.StatusCode == http.StatusNotFound)

		var response globalTypes.GlobalResponse
		err = json.NewDecoder(resp.Body).Decode(&response)
		require.NoError(t, err)

		// 验证响应格式正确
		assert.True(t, response.Code == errors.SUCCESS || response.Code == errors.RESOURCE_NOT_FOUND)
	})
}
