package main

import (
	"context"
	"fmt"

	"pxpat-backend/cmd"
	"pxpat-backend/internal/content-cluster/video-service/client"
	externalService "pxpat-backend/internal/content-cluster/video-service/external/service"
	intraService "pxpat-backend/internal/content-cluster/video-service/intra/service"
	"pxpat-backend/internal/content-cluster/video-service/messaging"
	"pxpat-backend/internal/content-cluster/video-service/messaging/publisher"
	"pxpat-backend/internal/content-cluster/video-service/middleware"
	"pxpat-backend/internal/content-cluster/video-service/migrations"
	"pxpat-backend/internal/content-cluster/video-service/repository"
	"pxpat-backend/internal/content-cluster/video-service/routes"
	"pxpat-backend/internal/content-cluster/video-service/types"
	"pxpat-backend/pkg/auth"
	"pxpat-backend/pkg/cache"
	configLoader "pxpat-backend/pkg/config"
	"pxpat-backend/pkg/consul"
	"pxpat-backend/pkg/consul/health"
	DBLoader "pxpat-backend/pkg/database"
	"pxpat-backend/pkg/logger"
	pkgMessaging "pxpat-backend/pkg/messaging"
	"pxpat-backend/pkg/middleware/cors"
	"pxpat-backend/pkg/storage"

	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
	"github.com/rs/zerolog/log"
	"go.uber.org/fx"
	"gorm.io/gorm"
)

// userServiceClientAdapter 用户服务客户端适配器
type userServiceClientAdapter struct {
	baseURL string
}

// GetUserLevel 获取用户等级信息
func (a *userServiceClientAdapter) GetUserLevel(ctx context.Context, userID string) (*middleware.UserLevelInfo, error) {
	// 这里应该实现实际的HTTP调用，暂时返回默认值
	return &middleware.UserLevelInfo{
		UserLevel: 3,
		PCLevel:   1,
		Region:    "CN",
	}, nil
}

// GetUserRoles 获取用户角色
func (a *userServiceClientAdapter) GetUserRoles(ctx context.Context, userID string) ([]middleware.Role, error) {
	// 这里应该实现实际的HTTP调用，暂时返回默认值
	return []middleware.Role{
		{
			ID:       1,
			Type:     "creator",
			Level:    3,
			Region:   "CN",
			IsActive: true,
		},
	}, nil
}

// GetUserPermissions 获取用户权限
func (a *userServiceClientAdapter) GetUserPermissions(ctx context.Context, userID string) ([]string, error) {
	// 这里应该实现实际的HTTP调用，暂时返回默认权限
	return []string{"content:create", "content:publish", "content:edit"}, nil
}

// 提供配置
func provideConfig() *types.Config {
	clusterName := "content"
	serviceName := "video"

	return configLoader.Load[types.Config](configLoader.LoaderConfig{
		TypeName:    configLoader.Service,
		ClusterName: clusterName,
		ServiceName: serviceName,
		UseRedis:    true,
	})
}

// 提供日志器
func provideLogger(cfg *types.Config) {
	serviceName := "video"

	if err := logger.InitLogger(cfg.Log, serviceName); err != nil {
		log.Fatal().Err(err).Msg("Failed to initialize logger")
	}

	log.Info().Msg("Content intra starting...")
}

// 提供数据库连接
func provideDatabase(cfg *types.Config) (*gorm.DB, error) {
	db, err := DBLoader.ConnectDB(cfg.Database)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to initialize database")
		return nil, err
	}
	log.Info().Msg("Database connected successfully")

	err = migrations.AutoMigrate(db)
	if err != nil {
		log.Fatal().Err(err).Msg("Database migration failed")
		return nil, err
	}
	log.Info().Msg("Database migration completed")
	return db, nil
}

// 提供Redis连接
func provideRedis(cfg *types.Config) (*redis.Client, error) {
	rdb, err := DBLoader.ConnectRedis(cfg.Redis)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to initialize Redis")
		return nil, err
	}
	log.Info().Msg("Redis connected successfully")
	return rdb, nil
}

// 提供缓存管理器
func provideCacheManager(rdb *redis.Client) (cache.Manager, error) {
	cacheManager, err := cache.NewManager(rdb)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to initialize cache manager")
		return nil, err
	}
	log.Info().Msg("Cache manager initialized")
	return cacheManager, nil
}

// 提供用户服务客户端
func provideUserServiceClient(cfg *types.Config) client.UserServiceClient {
	userServiceClient := client.NewUserServiceClient(client.UserServiceConfig{
		BaseURL: fmt.Sprintf("http://%s:%d", cfg.Server.AllServiceList.UserService.Host, cfg.Server.AllServiceList.UserService.Port),
		Timeout: cfg.Server.AllServiceList.UserService.Timeout,
	})
	log.Info().Msg("User service client initialized")
	return userServiceClient
}

// 提供交互服务客户端
func provideInteractionServiceClient(cfg *types.Config) client.InteractionServiceClient {
	interactionServiceClient := client.NewInteractionServiceClient(client.InteractionServiceConfig{
		BaseURL: fmt.Sprintf("http://%s:%d", cfg.Server.AllServiceList.InteractionService.Host, cfg.Server.AllServiceList.InteractionService.Port),
		Timeout: cfg.Server.AllServiceList.InteractionService.Timeout,
	})
	log.Info().Msg("Interaction service client initialized")
	return interactionServiceClient
}

// 提供Repository层
func provideRepositories(
	db *gorm.DB,
	rdb *redis.Client,
	cacheManager cache.Manager,
	userServiceClient client.UserServiceClient,
) (
	*repository.ContentRepository,
	*repository.CategoryRepository,
	*repository.TagRepository,
	*repository.CommentRepository,
	*repository.ComplaintRepository,
	*repository.UserCreationRetryRepository,
	repository.ContentUserRoleRepository,
	*repository.PublishStatsRepository,
) {
	contentRepo := repository.NewContentRepository(db, rdb, cacheManager, userServiceClient)
	categoryRepo := repository.NewCategoryRepository(db)
	tagRepo := repository.NewTagRepository(db)
	commentRepo := repository.NewCommentRepository(db, rdb, cacheManager)
	complaintRepo := repository.NewComplaintRepository(db)
	retryRepo := repository.NewUserCreationRetryRepository(db)
	contentUserRoleRepo := repository.NewContentCollaboratorRepository(db)
	publishStatsRepo := repository.NewPublishStatsRepository(db)

	log.Info().Msg("Repositories initialized")
	return contentRepo, categoryRepo, tagRepo, commentRepo, complaintRepo, retryRepo, contentUserRoleRepo, publishStatsRepo
}

// 提供存储客户端
func provideStorageClient(cfg *types.Config) storage.StorageClient {
	var storageClient storage.StorageClient
	if cfg.Storage.Minio.Provider != "" {
		var err error
		storageClient, err = storage.NewStorageClient(cfg.Storage.Minio)
		if err != nil {
			log.Warn().Err(err).Msg("Failed to initialize storage client")
			log.Info().Msg("Continuing without storage client...")
		} else {
			log.Info().Str("provider", cfg.Storage.Minio.Provider).Msg("Storage client initialized successfully")
		}
	} else {
		log.Info().Msg("Storage provider not configured, skipping storage client initialization")
	}
	return storageClient
}

// 提供MQ发布器
func provideMQPublisher(cfg *types.Config) *publisher.Publisher {
	var mqPublisher *publisher.Publisher
	if cfg.RabbitMQ.URL != "" {
		var err error
		mqPublisher, err = publisher.NewPublisher(cfg.RabbitMQ.URL)
		if err != nil {
			log.Warn().Err(err).Msg("Failed to initialize MQ publisher")
			log.Info().Msg("Continuing without MQ message publishing...")
		} else {
			log.Info().Msg("MQ publisher initialized successfully")
		}
	} else {
		log.Info().Msg("RabbitMQ URL not configured, skipping MQ publisher initialization")
	}
	return mqPublisher
}

// 提供MQ多消费者
func provideMQMultiConsumer(
	cfg *types.Config,
	contentRepo *repository.ContentRepository,
	auditServiceClient client.AuditServiceClient,
	contentService *intraService.ContentService,
) *pkgMessaging.MultiConsumer {
	var multiConsumer *pkgMessaging.MultiConsumer
	if cfg.RabbitMQ.URL != "" {
		var err error
		multiConsumer, err = messaging.CreateContentServiceMultiConsumer(
			cfg.RabbitMQ.URL,
			contentRepo,
			auditServiceClient,
			contentService,
		)
		if err != nil {
			log.Warn().Err(err).Msg("Failed to initialize MQ multi consumer")
			log.Info().Msg("Continuing without MQ message consumption...")
		} else {
			log.Info().Msg("MQ multi consumer initialized successfully")
		}
	} else {
		log.Info().Msg("RabbitMQ URL not configured, skipping MQ consumer initialization")
	}
	return multiConsumer
}

// 提供Consul管理器
func provideConsulManager(cfg *types.Config) (*consul.Manager, error) {
	consulManager, err := consul.NewManager(&cfg.Consul)
	if err != nil {
		log.Fatal().Err(err).Msg("Consul管理器初始化失败")
		return nil, err
	}
	return consulManager, nil
}

// 提供健康检查处理器
func provideHealthHandler(consulManager *consul.Manager, db *gorm.DB, rdb *redis.Client) *consul.HealthHandler {
	healthHandler := consul.NewHealthHandler(consulManager, "1.0.0")

	// 添加数据库健康检查
	healthHandler.AddChecker(health.NewDatabaseHealthChecker("database", func() error {
		sqlDB, err := db.DB()
		if err != nil {
			return err
		}
		return sqlDB.Ping()
	}))

	// 添加Redis健康检查
	healthHandler.AddChecker(health.NewRedisHealthChecker("redis", func() error {
		return rdb.Ping(context.Background()).Err()
	}))

	return healthHandler
}

// 提供JWT管理器
func provideJWTManager(cfg *types.Config) *auth.Manager {
	jwtManager := auth.NewJWTManager(cfg.JWT.SecretKey, cfg.JWT.Expiration, "pxpat-video")
	log.Info().Msg("JWT manager initialized")
	return &jwtManager
}

// 提供权限中间件
func providePermissionMiddleware(
	cacheManager cache.Manager,
	cfg *types.Config,
) *middleware.PermissionMiddleware {
	userServiceURL := fmt.Sprintf("http://%s:%d", cfg.Server.AllServiceList.UserService.Host, cfg.Server.AllServiceList.UserService.Port)
	// 创建一个简单的用户服务客户端适配器
	userServiceClient := &userServiceClientAdapter{
		baseURL: userServiceURL,
	}
	permissionMiddleware := middleware.NewPermissionMiddleware(userServiceClient, cacheManager, userServiceURL)
	log.Info().Msg("Permission middleware initialized")
	return permissionMiddleware
}

// 提供审核服务客户端
func provideAuditServiceClient(cfg *types.Config) client.AuditServiceClient {
	auditServiceClient := client.NewAuditServiceClient(client.AuditServiceConfig{
		BaseURL: fmt.Sprintf("http://%s:%d", cfg.Server.AllServiceList.AuditService.Host, cfg.Server.AllServiceList.AuditService.Port),
		Timeout: cfg.Server.AllServiceList.AuditService.Timeout,
	})
	log.Info().Msg("Audit service client initialized")
	return auditServiceClient
}

// 提供External服务层
func provideExternalServices(
	contentRepo *repository.ContentRepository,
	categoryRepo *repository.CategoryRepository,
	tagRepo *repository.TagRepository,
	commentRepo *repository.CommentRepository,
	complaintRepo *repository.ComplaintRepository,
	contentUserRoleRepo repository.ContentUserRoleRepository,
	publishStatsRepo *repository.PublishStatsRepository,
	mqPublisher *publisher.Publisher,
	storageClient storage.StorageClient,
	userServiceClient client.UserServiceClient,
	interactionServiceClient client.InteractionServiceClient,
	auditServiceClient client.AuditServiceClient,
	cfg *types.Config,
	db *gorm.DB,
) (
	*externalService.ContentService,
	*externalService.CategoryService,
	*externalService.TagService,
	*externalService.CommentService,
	*externalService.ComplaintService,
	*externalService.CollaborationService,
	*externalService.PublishStatsService,
) {
	// 创建存储服务客户端配置
	storageServiceConfig := client.ContentStorageServiceConfig{
		BaseURL: fmt.Sprintf("http://%s:%d", cfg.Server.AllServiceList.ContentStorageService.Host, cfg.Server.AllServiceList.ContentStorageService.Port),
		Timeout: cfg.Server.AllServiceList.ContentStorageService.Timeout,
	}

	// 创建External服务
	contentService := externalService.NewContentService(
		contentRepo,
		categoryRepo,
		contentUserRoleRepo,
		mqPublisher,
		storageClient,
		cfg.Storage.Minio.UrlExpiry,
		storageServiceConfig,
		userServiceClient,
		interactionServiceClient,
	)

	categoryService := externalService.NewCategoryService(categoryRepo)
	tagService := externalService.NewTagService(tagRepo)
	commentService := externalService.NewCommentService(commentRepo, contentRepo, userServiceClient, mqPublisher)
	complaintService := externalService.NewComplaintService(complaintRepo, contentRepo, commentRepo)
	collaborationService := externalService.NewCollaborationService(contentRepo, contentUserRoleRepo, mqPublisher, auditServiceClient)
	publishStatsService := externalService.NewPublishStatsService(publishStatsRepo, userServiceClient)

	log.Info().Msg("External services initialized")
	return contentService, categoryService, tagService, commentService, complaintService, collaborationService, publishStatsService
}

// 提供Internal服务层
func provideInternalServices(
	contentRepo *repository.ContentRepository,
	categoryRepo *repository.CategoryRepository,
	tagRepo *repository.TagRepository,
	commentRepo *repository.CommentRepository,
	contentUserRoleRepo repository.ContentUserRoleRepository,
	userServiceClient client.UserServiceClient,
	retryRepo *repository.UserCreationRetryRepository,
	publishStatsRepo *repository.PublishStatsRepository,
) (
	*intraService.ContentService,
	*intraService.CategoryService,
	*intraService.TagService,
	*intraService.CommentService,
) {
	// 创建Internal服务
	contentService := intraService.NewContentService(contentRepo, tagRepo, contentUserRoleRepo, userServiceClient, retryRepo, publishStatsRepo)
	categoryService := intraService.NewCategoryService(categoryRepo)
	tagService := intraService.NewTagService(tagRepo)
	commentService := intraService.NewCommentService(commentRepo, contentRepo)

	log.Info().Msg("Internal services initialized")
	return contentService, categoryService, tagService, commentService
}

// 提供Gin引擎并注册路由
func provideGinEngine(
	cfg *types.Config,
	healthHandler *consul.HealthHandler,
	// External services
	contentService *externalService.ContentService,
	categoryService *externalService.CategoryService,
	tagService *externalService.TagService,
	commentService *externalService.CommentService,
	complaintService *externalService.ComplaintService,
	collaborationService *externalService.CollaborationService,
	publishStatsService *externalService.PublishStatsService,
	// Internal services
	internalContentService *intraService.ContentService,
	internalCategoryService *intraService.CategoryService,
	internalTagService *intraService.TagService,
	internalCommentService *intraService.CommentService,
	// Middleware and auth
	permissionMiddleware *middleware.PermissionMiddleware,
	jwtManager *auth.Manager,
) *gin.Engine {
	gin.SetMode(cfg.Server.Mode)
	log.Info().Str("mode", cfg.Server.Mode).Msg("Gin mode set")

	router := gin.Default()
	router.Use(cors.CORSMiddleware(cfg.Security.CORS))
	log.Info().Msg("Middleware configured")

	// 注册健康检查路由
	healthHandler.RegisterHealthRoutes(router)

	// 注册业务路由
	routes.RegisterRoutes(
		router,
		contentService,
		categoryService,
		tagService,
		commentService,
		complaintService,
		collaborationService,
		publishStatsService,
		permissionMiddleware,
		jwtManager,
		internalContentService,
		internalCategoryService,
		internalTagService,
		internalCommentService,
	)

	log.Info().Msg("Routes registered successfully")
	return router
}

// 应用生命周期管理
func manageLifecycle(
	lc fx.Lifecycle,
	cfg *types.Config,
	consulManager *consul.Manager,
	mqPublisher *publisher.Publisher,
	mqMultiConsumer *pkgMessaging.MultiConsumer,
	ginEngine *gin.Engine,
) {
	lc.Append(fx.Hook{
		OnStart: func(_ context.Context) error {
			ctx := context.Background()
			// 启动Consul管理器
			if err := consulManager.Start(ctx); err != nil {
				log.Fatal().Err(err).Msg("Consul管理器启动失败")
				return err
			}
			log.Info().Msg("Consul管理器启动成功")

			// 启动MQ多消费者（在goroutine中，使用新的context避免deadline问题）
			if mqMultiConsumer != nil {
				go func() {
					// 创建一个新的context，不继承fx OnStart的deadline
					log.Info().Msg("Starting MQ multi consumer")
					if err := mqMultiConsumer.StartConsuming(ctx); err != nil {
						log.Error().Err(err).Msg("Error starting MQ multi consumer")
					}
				}()
			}

			// 启动HTTP服务器（在goroutine中）
			go func() {
				log.Info().Int("port", cfg.Server.Port).Msg("Starting video service server")
				cmd.GraceStartAndClose(cfg.Server, ginEngine)
			}()
			return nil
		},
		OnStop: func(ctx context.Context) error {
			// 停止Consul管理器
			if err := consulManager.Stop(); err != nil {
				log.Error().Err(err).Msg("停止Consul管理器失败")
			} else {
				log.Info().Msg("Consul管理器停止成功")
			}

			// 关闭MQ多消费者
			if mqMultiConsumer != nil {
				if err := mqMultiConsumer.Close(); err != nil {
					log.Error().Err(err).Msg("关闭MQ多消费者失败")
				} else {
					log.Info().Msg("MQ多消费者关闭成功")
				}
			}

			// 关闭MQ发布器
			if mqPublisher != nil {
				if err := mqPublisher.Close(); err != nil {
					log.Error().Err(err).Msg("关闭MQ发布器失败")
				} else {
					log.Info().Msg("MQ发布器关闭成功")
				}
			}

			log.Info().Msg("Video service stopped")
			return nil
		},
	})
}

func main() {
	app := fx.New(
		// 提供依赖
		fx.Provide(
			provideConfig,
			provideDatabase,
			provideRedis,
			provideCacheManager,
			provideUserServiceClient,
			provideInteractionServiceClient,
			provideRepositories,
			provideStorageClient,
			provideMQPublisher,
			provideMQMultiConsumer,
			provideConsulManager,
			provideHealthHandler,
			provideJWTManager,
			providePermissionMiddleware,
			provideAuditServiceClient,
			provideExternalServices,
			provideInternalServices,
			provideGinEngine,
		),
		// 调用生命周期管理
		fx.Invoke(
			provideLogger,
			manageLifecycle,
		),
	)

	// 运行应用
	app.Run()
}
