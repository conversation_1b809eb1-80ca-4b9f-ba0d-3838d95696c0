package utils

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"pxpat-backend/internal/content-cluster/content-management-service/types"
)

func TestContentConverter_VideoToBaseContent(t *testing.T) {
	converter := NewContentConverter()

	tests := []struct {
		name     string
		video    *types.VideoContent
		expected *types.BaseContent
		wantErr  bool
	}{
		{
			name: "正常视频转换",
			video: &types.VideoContent{
				KSUID:       "video_123",
				Title:       "测试视频",
				Description: "这是一个测试视频",
				UserKSUID:   "user_123",
				Status:      "published",
				CategoryID:  1,
				Tags:        []string{"测试", "视频"},
				CreatedAt:   time.Now(),
				UpdatedAt:   time.Now(),
				Duration:    300,
				Resolution:  "1920x1080",
				FileSize:    1024000,
			},
			expected: &types.BaseContent{
				KSUID:       "video_123",
				ContentType: "video",
				Title:       "测试视频",
				Description: "这是一个测试视频",
				UserKSUID:   "user_123",
				Status:      "published",
				CategoryID:  1,
				Tags:        []string{"测试", "视频"},
			},
			wantErr: false,
		},
		{
			name: "空视频对象",
			video: nil,
			expected: nil,
			wantErr: true,
		},
		{
			name: "缺少必要字段的视频",
			video: &types.VideoContent{
				KSUID: "",
				Title: "",
			},
			expected: nil,
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := converter.VideoToBaseContent(tt.video)

			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, result)
			} else {
				require.NoError(t, err)
				require.NotNil(t, result)
				assert.Equal(t, tt.expected.KSUID, result.KSUID)
				assert.Equal(t, tt.expected.ContentType, result.ContentType)
				assert.Equal(t, tt.expected.Title, result.Title)
				assert.Equal(t, tt.expected.Description, result.Description)
				assert.Equal(t, tt.expected.UserKSUID, result.UserKSUID)
				assert.Equal(t, tt.expected.Status, result.Status)
				assert.Equal(t, tt.expected.CategoryID, result.CategoryID)
				assert.Equal(t, tt.expected.Tags, result.Tags)
			}
		})
	}
}

func TestContentConverter_BaseContentToVideoWithDetails(t *testing.T) {
	converter := NewContentConverter()

	tests := []struct {
		name        string
		baseContent *types.BaseContent
		videoData   map[string]interface{}
		expected    *types.VideoContentWithDetails
		wantErr     bool
	}{
		{
			name: "正常基础内容转换为详细视频",
			baseContent: &types.BaseContent{
				KSUID:       "video_123",
				ContentType: "video",
				Title:       "测试视频",
				Description: "这是一个测试视频",
				UserKSUID:   "user_123",
				Status:      "published",
				CategoryID:  1,
				Tags:        []string{"测试", "视频"},
				CreatedAt:   time.Now(),
				UpdatedAt:   time.Now(),
			},
			videoData: map[string]interface{}{
				"duration":   300,
				"resolution": "1920x1080",
				"file_size":  1024000,
				"views":      1000,
				"likes":      50,
				"favorites":  20,
			},
			expected: &types.VideoContentWithDetails{
				KSUID:       "video_123",
				Title:       "测试视频",
				Description: "这是一个测试视频",
				UserKSUID:   "user_123",
				Status:      "published",
				CategoryID:  1,
				Tags:        []string{"测试", "视频"},
				Duration:    300,
				Resolution:  "1920x1080",
				FileSize:    1024000,
				InteractionStats: types.InteractionStats{
					Views:     1000,
					Likes:     50,
					Favorites: 20,
				},
			},
			wantErr: false,
		},
		{
			name:        "空基础内容",
			baseContent: nil,
			videoData:   nil,
			expected:    nil,
			wantErr:     true,
		},
		{
			name: "非视频类型内容",
			baseContent: &types.BaseContent{
				KSUID:       "novel_123",
				ContentType: "novel",
				Title:       "测试小说",
			},
			videoData: map[string]interface{}{},
			expected:  nil,
			wantErr:   true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := converter.BaseContentToVideoWithDetails(tt.baseContent, tt.videoData)

			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, result)
			} else {
				require.NoError(t, err)
				require.NotNil(t, result)
				assert.Equal(t, tt.expected.KSUID, result.KSUID)
				assert.Equal(t, tt.expected.Title, result.Title)
				assert.Equal(t, tt.expected.Description, result.Description)
				assert.Equal(t, tt.expected.UserKSUID, result.UserKSUID)
				assert.Equal(t, tt.expected.Status, result.Status)
				assert.Equal(t, tt.expected.CategoryID, result.CategoryID)
				assert.Equal(t, tt.expected.Tags, result.Tags)
				assert.Equal(t, tt.expected.Duration, result.Duration)
				assert.Equal(t, tt.expected.Resolution, result.Resolution)
				assert.Equal(t, tt.expected.FileSize, result.FileSize)
				assert.Equal(t, tt.expected.InteractionStats.Views, result.InteractionStats.Views)
				assert.Equal(t, tt.expected.InteractionStats.Likes, result.InteractionStats.Likes)
				assert.Equal(t, tt.expected.InteractionStats.Favorites, result.InteractionStats.Favorites)
			}
		})
	}
}

func TestContentConverter_BatchVideoToBaseContent(t *testing.T) {
	converter := NewContentConverter()

	videos := []*types.VideoContent{
		{
			KSUID:       "video_1",
			Title:       "视频1",
			Description: "描述1",
			UserKSUID:   "user_1",
			Status:      "published",
			CategoryID:  1,
			Tags:        []string{"tag1"},
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		},
		{
			KSUID:       "video_2",
			Title:       "视频2",
			Description: "描述2",
			UserKSUID:   "user_2",
			Status:      "draft",
			CategoryID:  2,
			Tags:        []string{"tag2"},
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		},
	}

	results, err := converter.BatchVideoToBaseContent(videos)

	require.NoError(t, err)
	require.Len(t, results, 2)

	// 验证第一个结果
	assert.Equal(t, "video_1", results[0].KSUID)
	assert.Equal(t, "video", results[0].ContentType)
	assert.Equal(t, "视频1", results[0].Title)
	assert.Equal(t, "user_1", results[0].UserKSUID)

	// 验证第二个结果
	assert.Equal(t, "video_2", results[1].KSUID)
	assert.Equal(t, "video", results[1].ContentType)
	assert.Equal(t, "视频2", results[1].Title)
	assert.Equal(t, "user_2", results[1].UserKSUID)
}

func TestContentConverter_BatchVideoToBaseContent_WithError(t *testing.T) {
	converter := NewContentConverter()

	videos := []*types.VideoContent{
		{
			KSUID:     "video_1",
			Title:     "视频1",
			UserKSUID: "user_1",
		},
		nil, // 这会导致错误
		{
			KSUID:     "video_3",
			Title:     "视频3",
			UserKSUID: "user_3",
		},
	}

	results, err := converter.BatchVideoToBaseContent(videos)

	assert.Error(t, err)
	assert.Nil(t, results)
	assert.Contains(t, err.Error(), "转换第2个视频失败")
}

func TestContentConverter_ValidateContentType(t *testing.T) {
	converter := NewContentConverter()

	tests := []struct {
		name        string
		contentType string
		expected    bool
	}{
		{"有效的视频类型", "video", true},
		{"有效的小说类型", "novel", true},
		{"有效的音乐类型", "music", true},
		{"无效的类型", "invalid", false},
		{"空类型", "", false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := converter.ValidateContentType(tt.contentType)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestContentConverter_GetSupportedContentTypes(t *testing.T) {
	converter := NewContentConverter()

	types := converter.GetSupportedContentTypes()

	assert.Contains(t, types, "video")
	assert.Contains(t, types, "novel")
	assert.Contains(t, types, "music")
	assert.Len(t, types, 3)
}
