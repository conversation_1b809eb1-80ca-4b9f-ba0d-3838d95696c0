package main

import (
	"context"
	"fmt"
	"go.uber.org/fx"
	"os"
	"path/filepath"
	"pxpat-backend/internal/storage-cluster/content-processing-service/messaging"
	"pxpat-backend/internal/storage-cluster/content-processing-service/migrations"
	"pxpat-backend/internal/storage-cluster/content-processing-service/processor/video"
	"pxpat-backend/internal/storage-cluster/content-processing-service/queue"
	"pxpat-backend/internal/storage-cluster/content-processing-service/repository"
	"pxpat-backend/internal/storage-cluster/content-processing-service/service"
	"pxpat-backend/internal/storage-cluster/content-processing-service/types"
	"pxpat-backend/internal/storage-cluster/content-processing-service/worker"
	configLoader "pxpat-backend/pkg/config"
	"pxpat-backend/pkg/consul"
	"pxpat-backend/pkg/consul/health"
	"pxpat-backend/pkg/database"
	"pxpat-backend/pkg/logger"
	pkgMessaging "pxpat-backend/pkg/messaging"
	"pxpat-backend/pkg/opentelemetry"
	"pxpat-backend/pkg/storage"
	"time"

	"github.com/redis/go-redis/v9"
	"github.com/rs/zerolog/log"
	"gorm.io/gorm"
)

// 提供配置
func provideConfig() *types.Config {
	clusterName := "storage"
	serviceName := "media-processing"

	return configLoader.Load[types.Config](configLoader.LoaderConfig{
		TypeName:    configLoader.Service,
		ClusterName: clusterName,
		ServiceName: serviceName,
		UseRedis:    true,
	})
}

// 提供日志器
func provideLogger(cfg *types.Config) error {
	serviceName := "media-processing"

	// 初始化日志系统
	if err := logger.InitLogger(cfg.Log, serviceName); err != nil {
		log.Fatal().Msgf("日志系统初始化失败: %v", err)
		return err
	}

	log.Info().Msg("Media processing service starting...")
	return nil
}

// 提供OpenTelemetry Provider
func provideOtelProvider(cfg *types.Config) (*opentelemetry.Provider, error) {
	otelProvider, err := opentelemetry.NewProvider(cfg.Otlp)
	if err != nil {
		log.Fatal().Err(err).Msg("OpenTelemetry 初始化失败")
		return nil, err
	}

	if otelProvider.IsTracingEnabled() {
		log.Info().Str("service_name", cfg.Otlp.Tracing.ServiceName).
			Str("exporter_type", cfg.Otlp.Tracing.ExporterType).
			Msg("链路追踪已启用")
	} else {
		log.Info().Msg("链路追踪已禁用")
	}

	if otelProvider.IsMetricsEnabled() {
		log.Info().Str("service_name", cfg.Otlp.Metrics.ServiceName).
			Str("exporter_type", cfg.Otlp.Metrics.ExporterType).
			Msg("指标收集已启用")
	} else {
		log.Info().Msg("指标收集已禁用")
	}

	return otelProvider, nil
}

// 提供数据库连接
func provideDatabase(cfg *types.Config) (*gorm.DB, error) {
	// 创建必要的目录
	createRequiredDirectories(cfg)

	log.Info().Msg("Attempting to connect to database...")
	db, err := database.ConnectDB(cfg.Database)
	if err != nil {
		log.Fatal().Msgf("数据库连接失败: %v", err)
		return nil, err
	}
	log.Info().Msg("Database connected successfully")

	// 执行数据库迁移
	log.Info().Msg("Running database migrations...")
	if err := migrations.AutoMigrate(db); err != nil {
		log.Fatal().Msgf("数据库迁移失败: %v", err)
		return nil, err
	}
	log.Info().Msg("Database migrations completed successfully")

	return db, nil
}

// 提供Redis客户端
func provideRedisClient(cfg *types.Config) (*redis.Client, error) {
	log.Info().Msg("Attempting to connect to Redis...")
	redisClient, err := database.ConnectRedis(cfg.Redis)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to connect to Redis")
		return nil, err
	}
	log.Info().Msg("Redis client connected successfully")
	return redisClient, nil
}

// 提供存储客户端
func provideStorageClient(cfg *types.Config) (storage.StorageClient, error) {
	log.Info().Msg("Attempting to initialize MinIO client...")
	minioClient, err := storage.NewStorageClient(cfg.Storage.MinIO)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to initialize MinIO client")
		return nil, err
	}
	log.Info().Msg("MinIO client initialized successfully")
	return minioClient, nil
}

// 提供Redis队列
func provideRedisQueue(redisClient *redis.Client) (*queue.RedisQueue, error) {
	redisQueue, err := queue.NewRedisQueue(redisClient)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to initialize Redis queue")
		return nil, err
	}
	log.Info().Msg("Redis queue initialized successfully")
	return redisQueue, nil
}

// 提供Repository
func provideTaskRepository(db *gorm.DB) repository.VideoProcessingTaskRepository {
	return repository.NewVideoProcessingTaskRepository(db)
}

func provideMediaResultRepository(db *gorm.DB) repository.VideoProcessingResultRepository {
	return repository.NewVideoProcessingResultRepository(db)
}

// 提供Service
func provideMediaResultService(mediaResultRepo repository.VideoProcessingResultRepository) service.VideoProcessingResultService {
	return service.NewVideoProcessingResultService(mediaResultRepo)
}

func provideMediaProcessingService(
	redisQueue *queue.RedisQueue,
	redisClient *redis.Client,
	taskRepo repository.VideoProcessingTaskRepository,
	mediaResultRepo repository.VideoProcessingResultRepository,
	mediaResultService service.VideoProcessingResultService,
) service.VideoProcessingService {
	return service.NewVideoProcessingService(redisQueue, redisClient, taskRepo, mediaResultRepo, mediaResultService)
}

// 提供FFmpeg处理器
func provideFFmpegProcessor(
	cfg *types.Config,
	storageClient storage.StorageClient,
	redisClient *redis.Client,
	taskRepo repository.VideoProcessingTaskRepository,
	mediaResultRepo repository.VideoProcessingResultRepository,
) *video.FFmpegProcessor {
	return video.NewFFmpegProcessor(cfg, storageClient, redisClient, taskRepo, mediaResultRepo)
}

// 提供MQ发布器
func provideMQPublisher(cfg *types.Config) (*pkgMessaging.Publisher, error) {
	mqPublisher, err := pkgMessaging.NewPublisherBuilder(cfg.RabbitMQ.URL).
		WithTopicExchange(pkgMessaging.MEDIA_PROCESSING_EXCHANGE).
		Build()
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to initialize MQ publisher")
		return nil, err
	}
	log.Info().Msg("MQ publisher initialized successfully")
	return mqPublisher, nil
}

// 提供MQ多消费者
func provideMQMultiConsumer(cfg *types.Config, mediaProcessingService service.VideoProcessingService) (*pkgMessaging.MultiConsumer, error) {
	multiConsumer, err := messaging.CreateMediaProcessingMultiConsumer(cfg.RabbitMQ.URL, mediaProcessingService)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to initialize MQ multi consumer")
		return nil, err
	}
	log.Info().Msg("MQ multi consumer initialized successfully")
	return multiConsumer, nil
}

// 提供工作器
func provideWorker(
	cfg *types.Config,
	redisQueue *queue.RedisQueue,
	storageClient storage.StorageClient,
	ffmpegProcessor *video.FFmpegProcessor,
	mqPublisher *pkgMessaging.Publisher,
	taskRepo repository.VideoProcessingTaskRepository,
	mediaResultService service.VideoProcessingResultService,
) *worker.Worker {
	return worker.NewWorker(cfg, redisQueue, storageClient, ffmpegProcessor, mqPublisher, taskRepo, mediaResultService)
}

// 提供Consul管理器
func provideConsulManager(cfg *types.Config) (*consul.Manager, error) {
	consulManager, err := consul.NewManager(&cfg.Consul)
	if err != nil {
		log.Fatal().Err(err).Msg("Consul管理器初始化失败")
		return nil, err
	}
	return consulManager, nil
}

// 提供健康检查处理器
func provideHealthHandler(consulManager *consul.Manager, db *gorm.DB) *consul.HealthHandler {
	healthHandler := consul.NewHealthHandler(consulManager, "1.0.0")

	// 添加数据库健康检查
	healthHandler.AddChecker(health.NewDatabaseHealthChecker("database", func() error {
		sqlDB, err := db.DB()
		if err != nil {
			return err
		}
		return sqlDB.Ping()
	}))

	return healthHandler
}

// 应用生命周期管理
func manageLifecycle(
	lc fx.Lifecycle,
	cfg *types.Config,
	otelProvider *opentelemetry.Provider,
	consulManager *consul.Manager,
	mediaWorker *worker.Worker,
	multiConsumer *pkgMessaging.MultiConsumer,
	mqPublisher *pkgMessaging.Publisher,
	taskRepo repository.VideoProcessingTaskRepository,
	redisQueue *queue.RedisQueue,
) {
	var cancel context.CancelFunc

	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) error {
			// 启动时恢复任务状态
			log.Info().Msg("Starting task status recovery...")
			if err := recoverTaskStatus(taskRepo, redisQueue); err != nil {
				log.Warn().Err(err).Msg("Task status recovery failed")
			} else {
				log.Info().Msg("Task status recovery completed successfully")
			}

			// 启动Consul管理器
			if err := consulManager.Start(context.Background()); err != nil {
				log.Fatal().Err(err).Msg("Consul管理器启动失败")
				return err
			}
			log.Info().Msg("Consul管理器启动成功")

			// 启动工作器
			workerCount := cfg.MediaProcessing.FFmpeg.MaxWorkers
			if workerCount <= 0 {
				workerCount = 4 // 默认4个工作器
			}
			log.Info().Int("worker_count", workerCount).Msg("Starting media workers")
			mediaWorker.Start(workerCount)

			// 创建可取消的上下文用于 MQ 消费者
			var consumerCtx context.Context
			consumerCtx, cancel = context.WithCancel(context.Background())

			// 启动消费者
			go func() {
				if err := multiConsumer.StartConsuming(consumerCtx); err != nil {
					log.Error().Err(err).Msg("Error starting MQ multi consumer")
				}
			}()

			// 启动心跳服务
			go startHeartbeatService()

			return nil
		},
		OnStop: func(ctx context.Context) error {
			log.Info().Msg("Received shutdown signal, starting graceful shutdown...")

			// 1. 取消 MQ 消费者的上下文
			if cancel != nil {
				log.Info().Msg("Stopping MQ consumer...")
				cancel()
			}

			// 2. 停止工作器
			log.Info().Msg("Stopping media workers...")
			mediaWorker.Stop()

			// 3. 关闭 MQ 消费者
			log.Info().Msg("Closing MQ multi consumer...")
			if err := multiConsumer.Close(); err != nil {
				log.Warn().Err(err).Msg("Error closing MQ multi consumer")
			}

			// 4. 关闭 MQ 发布器
			log.Info().Msg("Closing MQ publisher...")
			if err := mqPublisher.Close(); err != nil {
				log.Warn().Err(err).Msg("Error closing MQ publisher")
			}

			// 5. 停止Consul管理器
			log.Info().Msg("Stopping Consul manager...")
			if err := consulManager.Stop(); err != nil {
				log.Warn().Err(err).Msg("Error stopping Consul manager")
			}

			// 6. 关闭OpenTelemetry
			if err := otelProvider.Shutdown(ctx); err != nil {
				log.Error().Err(err).Msg("关闭 OpenTelemetry 失败")
			} else {
				log.Info().Msg("OpenTelemetry 关闭成功")
			}

			log.Info().Msg("Graceful shutdown completed")
			return nil
		},
	})
}

// 启动心跳服务
func startHeartbeatService() {
	heartbeatTicker := time.NewTicker(30 * time.Second)
	defer heartbeatTicker.Stop()

	for {
		select {
		case <-heartbeatTicker.C:
			// 发送心跳包
			log.Info().Msg("Sending heartbeat...")
			status := map[string]interface{}{
				"service":   "media-processing",
				"status":    "healthy",
				"timestamp": time.Now().Unix(),
			}
			log.Info().Interface("heartbeat", status).Msg("Heartbeat sent")
		}
	}
}

func main() {
	app := fx.New(
		// 提供依赖
		fx.Provide(
			provideConfig,
			provideOtelProvider,
			provideDatabase,
			provideRedisClient,
			provideStorageClient,
			provideRedisQueue,
			provideTaskRepository,
			provideMediaResultRepository,
			provideMediaResultService,
			provideMediaProcessingService,
			provideFFmpegProcessor,
			provideMQPublisher,
			provideMQMultiConsumer,
			provideWorker,
			provideConsulManager,
			provideHealthHandler,
		),
		// 调用生命周期管理
		fx.Invoke(
			provideLogger,
			manageLifecycle,
		),
	)

	// 运行应用
	app.Run()
}

// recoverTaskStatus 恢复任务状态
func recoverTaskStatus(taskRepo repository.VideoProcessingTaskRepository, redisQueue *queue.RedisQueue) error {
	ctx := context.Background()

	// 1. 将运行中的任务重置为待处理状态
	log.Info().Msg("Resetting running tasks to pending...")
	if err := taskRepo.ResetRunningTasksToPending(ctx); err != nil {
		return fmt.Errorf("failed to reset running tasks: %w", err)
	}

	// 2. 获取所有待处理的任务并重新加载到Redis
	log.Info().Msg("Loading pending tasks to Redis...")
	pendingTasks, err := taskRepo.GetPendingTasksForRecovery(ctx)
	if err != nil {
		return fmt.Errorf("failed to get pending tasks: %w", err)
	}

	// 3. 将待处理任务推送到Redis队列
	var successCount, failCount int
	for _, task := range pendingTasks {
		if err := redisQueue.PushTask(ctx, task); err != nil {
			log.Warn().Err(err).Uint64("task_id", task.ID).Msg("Failed to push task to Redis")
			failCount++
		} else {
			successCount++
		}
	}

	log.Info().Int("success_count", successCount).Int("fail_count", failCount).Msg("Task recovery completed")
	return nil
}

// createRequiredDirectories 创建必要的目录
func createRequiredDirectories(cfg *types.Config) {
	// 获取当前工作目录作为基础路径
	workDir, err := os.Getwd()
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to get current working directory")
	}

	// 设置默认路径（如果配置中的路径为空或无效）
	tempDir := cfg.Storage.TempDir
	if tempDir == "" {
		tempDir = filepath.Join(workDir, "temp", "media-processing")
		log.Printf("Using default temp directory: %s", tempDir)
	}

	outputDir := cfg.Storage.OutputDir
	if outputDir == "" {
		outputDir = filepath.Join(workDir, "output", "media-processing")
		log.Printf("Using default output directory: %s", outputDir)
	}

	// 更新配置中的路径（确保后续代码使用正确的路径）
	cfg.Storage.TempDir = tempDir
	cfg.Storage.OutputDir = outputDir

	dirs := []string{
		tempDir,
		outputDir,
	}

	for _, dir := range dirs {
		if err := os.MkdirAll(dir, 0755); err != nil {
			log.Fatal().Err(err).Msgf("Failed to create directory %s", dir)
		}
		log.Printf("Created directory: %s", dir)
	}
}
