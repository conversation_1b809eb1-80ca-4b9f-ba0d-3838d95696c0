name: Docker Build and Push

on:
  push:
    branches: [ main, develop ]
    tags: [ 'v*' ]
  pull_request:
    branches: [ main ]

env:
  REGISTRY: ghcr.io
  BASE_IMAGE_NAME: pxpat-backend

jobs:
  # 构建所有服务的Docker镜像
  build-matrix:
    name: Build Docker Images
    runs-on: ubuntu-latest
    strategy:
      matrix:
        service:
          - name: content-management-service
            dockerfile: docker/Dockerfile.content-management
            context: .
            path: internal/content-cluster/content-management-service
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
    
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
    
    - name: Log in to Container Registry
      if: github.event_name != 'pull_request'
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}
    
    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.REGISTRY }}/${{ env.BASE_IMAGE_NAME }}/${{ matrix.service.name }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=semver,pattern={{version}}
          type=semver,pattern={{major}}.{{minor}}
          type=semver,pattern={{major}}
          type=sha,prefix={{branch}}-
          type=raw,value=latest,enable={{is_default_branch}}
        labels: |
          org.opencontainers.image.title=${{ matrix.service.name }}
          org.opencontainers.image.description=PXPAT Backend ${{ matrix.service.name }}
          org.opencontainers.image.vendor=PXPAT Team
    
    - name: Check for service changes
      id: changes
      uses: dorny/paths-filter@v2
      with:
        filters: |
          service:
            - '${{ matrix.service.path }}/**'
            - '${{ matrix.service.dockerfile }}'
            - 'go.mod'
            - 'go.sum'
            - 'pkg/**'
    
    - name: Build and push Docker image
      if: steps.changes.outputs.service == 'true' || github.event_name == 'push'
      uses: docker/build-push-action@v5
      with:
        context: ${{ matrix.service.context }}
        file: ${{ matrix.service.dockerfile }}
        push: ${{ github.event_name != 'pull_request' }}
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha,scope=${{ matrix.service.name }}
        cache-to: type=gha,mode=max,scope=${{ matrix.service.name }}
        platforms: linux/amd64,linux/arm64
        build-args: |
          BUILDTIME=${{ fromJSON(steps.meta.outputs.json).labels['org.opencontainers.image.created'] }}
          VERSION=${{ fromJSON(steps.meta.outputs.json).labels['org.opencontainers.image.version'] }}
          REVISION=${{ fromJSON(steps.meta.outputs.json).labels['org.opencontainers.image.revision'] }}
    
    - name: Run Trivy vulnerability scanner
      if: steps.changes.outputs.service == 'true' || github.event_name == 'push'
      uses: aquasecurity/trivy-action@master
      with:
        image-ref: ${{ env.REGISTRY }}/${{ env.BASE_IMAGE_NAME }}/${{ matrix.service.name }}:${{ github.sha }}
        format: 'sarif'
        output: 'trivy-results-${{ matrix.service.name }}.sarif'
    
    - name: Upload Trivy scan results
      if: steps.changes.outputs.service == 'true' || github.event_name == 'push'
      uses: github/codeql-action/upload-sarif@v2
      with:
        sarif_file: 'trivy-results-${{ matrix.service.name }}.sarif'
        category: 'trivy-${{ matrix.service.name }}'

  # 多架构构建测试
  multi-arch-test:
    name: Multi-Architecture Build Test
    runs-on: ubuntu-latest
    if: github.event_name == 'pull_request'
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
    
    - name: Set up QEMU
      uses: docker/setup-qemu-action@v3
    
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
    
    - name: Test multi-arch build
      uses: docker/build-push-action@v5
      with:
        context: .
        file: docker/Dockerfile.content-management
        platforms: linux/amd64,linux/arm64
        push: false
        cache-from: type=gha
        cache-to: type=gha,mode=max

  # 镜像安全扫描
  security-scan:
    name: Security Scan
    runs-on: ubuntu-latest
    needs: build-matrix
    if: github.event_name != 'pull_request'
    
    steps:
    - name: Run Snyk to check Docker image for vulnerabilities
      uses: snyk/actions/docker@master
      env:
        SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
      with:
        image: ${{ env.REGISTRY }}/${{ env.BASE_IMAGE_NAME }}/content-management-service:${{ github.sha }}
        args: --severity-threshold=high
    
    - name: Upload Snyk results
      uses: github/codeql-action/upload-sarif@v2
      if: always()
      with:
        sarif_file: snyk.sarif

  # 清理旧镜像
  cleanup:
    name: Cleanup Old Images
    runs-on: ubuntu-latest
    needs: build-matrix
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    
    steps:
    - name: Delete old images
      uses: actions/delete-package-versions@v4
      with:
        package-name: 'pxpat-backend/content-management-service'
        package-type: 'container'
        min-versions-to-keep: 10
        delete-only-untagged-versions: true

  # 部署通知
  notify:
    name: Notify Deployment
    runs-on: ubuntu-latest
    needs: [build-matrix, security-scan]
    if: always() && github.event_name == 'push'
    
    steps:
    - name: Notify Slack
      uses: 8398a7/action-slack@v3
      with:
        status: ${{ job.status }}
        channel: '#ci-cd'
        webhook_url: ${{ secrets.SLACK_WEBHOOK }}
        fields: repo,message,commit,author,action,eventName,ref,workflow
        custom_payload: |
          {
            attachments: [{
              color: '${{ job.status }}' === 'success' ? 'good' : '${{ job.status }}' === 'failure' ? 'danger' : 'warning',
              blocks: [{
                type: 'section',
                text: {
                  type: 'mrkdwn',
                  text: `Docker build for *${{ github.repository }}* \n*${{ job.status }}*: ${{ github.event.head_commit.message }}`
                }
              }]
            }]
          }
