package repository

import (
	"context"
	"pxpat-backend/internal/user-cluster/complaint-service/model"
)

// RightsVerificationRepository 权益认证数据访问层接口
type RightsVerificationRepository interface {
	// 基础CRUD操作
	Create(ctx context.Context, verification *model.RightsVerification) error
	GetByKSUID(ctx context.Context, rightsKSUID string) (*model.RightsVerification, error)
	GetByID(ctx context.Context, id uint) (*model.RightsVerification, error)
	Update(ctx context.Context, verification *model.RightsVerification) error
	Delete(ctx context.Context, rightsKSUID string) error

	// 查询操作
	GetByUserKSUID(ctx context.Context, userKSUID string, page, pageSize int) ([]*model.RightsVerification, int64, error)
	GetByStatus(ctx context.Context, status model.RightsStatus, page, pageSize int) ([]*model.RightsVerification, int64, error)
	GetByType(ctx context.Context, rightsType model.RightsType, page, pageSize int) ([]*model.RightsVerification, int64, error)
	
	// 复合查询
	GetWithFilters(ctx context.Context, filters RightsFilters) ([]*model.RightsVerification, int64, error)

	// 统计操作
	CountByUserKSUID(ctx context.Context, userKSUID string) (int64, error)
	CountByStatus(ctx context.Context, status model.RightsStatus) (int64, error)
	CountByType(ctx context.Context, rightsType model.RightsType) (int64, error)

	// 业务操作
	UpdateStatus(ctx context.Context, rightsKSUID string, status model.RightsStatus, reviewerKSUID, reviewNote, rejectReason string) error
	GetPendingVerifications(ctx context.Context, page, pageSize int) ([]*model.RightsVerification, int64, error)
	GetExpiredVerifications(ctx context.Context) ([]*model.RightsVerification, error)

	// 获取数据库实例，用于事务处理
	GetDB() interface{}
}

// CopyrightRepository 著作权数据访问层接口
type CopyrightRepository interface {
	// 基础CRUD操作
	Create(ctx context.Context, copyright *model.Copyright) error
	GetByID(ctx context.Context, id uint) (*model.Copyright, error)
	Update(ctx context.Context, copyright *model.Copyright) error
	Delete(ctx context.Context, id uint) error

	// 查询操作
	GetByRightsKSUID(ctx context.Context, rightsKSUID string) ([]*model.Copyright, error)
	GetByType(ctx context.Context, copyrightType model.CopyrightType) ([]*model.Copyright, error)
	GetByCountry(ctx context.Context, countryCode string) ([]*model.Copyright, error)

	// 批量操作
	BatchCreate(ctx context.Context, copyrights []*model.Copyright) error
	BatchDelete(ctx context.Context, rightsKSUID string) error

	// 获取数据库实例，用于事务处理
	GetDB() interface{}
}

// TrademarkRepository 商标权数据访问层接口
type TrademarkRepository interface {
	// 基础CRUD操作
	Create(ctx context.Context, trademark *model.Trademark) error
	GetByID(ctx context.Context, id uint) (*model.Trademark, error)
	Update(ctx context.Context, trademark *model.Trademark) error
	Delete(ctx context.Context, id uint) error

	// 查询操作
	GetByRightsKSUID(ctx context.Context, rightsKSUID string) ([]*model.Trademark, error)
	GetByCategory(ctx context.Context, categoryNumber int) ([]*model.Trademark, error)
	GetByCountry(ctx context.Context, countryCode string) ([]*model.Trademark, error)

	// 批量操作
	BatchCreate(ctx context.Context, trademarks []*model.Trademark) error
	BatchDelete(ctx context.Context, rightsKSUID string) error

	// 获取数据库实例，用于事务处理
	GetDB() interface{}
}

// PersonalityRightRepository 人格权数据访问层接口
type PersonalityRightRepository interface {
	// 基础CRUD操作
	Create(ctx context.Context, personalityRight *model.PersonalityRight) error
	GetByID(ctx context.Context, id uint) (*model.PersonalityRight, error)
	Update(ctx context.Context, personalityRight *model.PersonalityRight) error
	Delete(ctx context.Context, id uint) error

	// 查询操作
	GetByRightsKSUID(ctx context.Context, rightsKSUID string) ([]*model.PersonalityRight, error)
	GetByType(ctx context.Context, personalityRightsType model.PersonalityRightsType) ([]*model.PersonalityRight, error)

	// 批量操作
	BatchCreate(ctx context.Context, personalityRights []*model.PersonalityRight) error
	BatchDelete(ctx context.Context, rightsKSUID string) error

	// 获取数据库实例，用于事务处理
	GetDB() interface{}
}

// RightsEvidenceRepository 权益证明文件数据访问层接口
type RightsEvidenceRepository interface {
	// 基础CRUD操作
	Create(ctx context.Context, evidence *model.RightsEvidence) error
	GetByID(ctx context.Context, id uint) (*model.RightsEvidence, error)
	Update(ctx context.Context, evidence *model.RightsEvidence) error
	Delete(ctx context.Context, id uint) error

	// 查询操作
	GetByRightsKSUID(ctx context.Context, rightsKSUID string) ([]*model.RightsEvidence, error)
	GetByRelatedKSUID(ctx context.Context, relatedKSUID string) ([]*model.RightsEvidence, error)
	GetByEvidenceType(ctx context.Context, evidenceType string) ([]*model.RightsEvidence, error)

	// 批量操作
	BatchCreate(ctx context.Context, evidences []*model.RightsEvidence) error
	BatchDelete(ctx context.Context, ids []uint) error
	DeleteByRightsKSUID(ctx context.Context, rightsKSUID string) error
	DeleteByRelatedKSUID(ctx context.Context, relatedKSUID string) error

	// 统计操作
	CountByRightsKSUID(ctx context.Context, rightsKSUID string) (int64, error)
	GetTotalSizeByRightsKSUID(ctx context.Context, rightsKSUID string) (int64, error)

	// 获取数据库实例，用于事务处理
	GetDB() interface{}
}

// RightsFilters 权益认证查询过滤条件
type RightsFilters struct {
	Page         int                // 页码
	PageSize     int                // 每页数量
	Type         model.RightsType   // 权益类型
	Status       model.RightsStatus // 状态
	StartDate    string             // 开始日期
	EndDate      string             // 结束日期
	SortBy       string             // 排序字段
	SortOrder    string             // 排序方向
	UserKSUID    string             // 用户KSUID
	ReviewerKSUID string            // 审核人KSUID
	IsAgent      *bool              // 是否代理
}

// 错误定义
var (
	ErrRightsVerificationNotFound = &RepositoryError{Message: "rights verification not found"}
	ErrRightsVerificationExists   = &RepositoryError{Message: "rights verification already exists"}
	ErrCopyrightNotFound          = &RepositoryError{Message: "copyright not found"}
	ErrTrademarkNotFound          = &RepositoryError{Message: "trademark not found"}
	ErrPersonalityRightNotFound   = &RepositoryError{Message: "personality right not found"}
	ErrRightsEvidenceNotFound     = &RepositoryError{Message: "rights evidence not found"}
	ErrInvalidRightsStatus        = &RepositoryError{Message: "invalid rights status"}
	ErrInvalidRightsType          = &RepositoryError{Message: "invalid rights type"}
	ErrRightsLimitExceeded        = &RepositoryError{Message: "rights limit exceeded"}
)
