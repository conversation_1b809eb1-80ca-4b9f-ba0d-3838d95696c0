package migrations

import (
	"fmt"
	"pxpat-backend/internal/content-cluster/video-service/model"

	"github.com/rs/zerolog/log"
	"gorm.io/gorm"
)

// AutoMigrate 自动迁移数据库表结构
func AutoMigrate(db *gorm.DB) error {
	log.Info().Msg("开始执行数据库迁移")

	// 定义需要迁移的模型
	models := []interface{}{
		&model.VideoContentTags{},
		&model.Tag{},
		&model.Category{},
		&model.Collaborator{}, // 新增内容用户角色关联表
		&model.Content{},
		&model.Comment{},
		&model.CommentLike{}, // 评论点赞表（包含喜欢和不喜欢）
		&model.Complaint{},   // 投诉表
		&model.UserCreationRetryTask{},
		&model.PublishStats{}, // 发布视频统计表
	}

	// 执行自动迁移 - 使用正确的顺序避免循环依赖
	for _, model_temp := range models {
		if err := db.AutoMigrate(model_temp); err != nil {
			log.Error().
				Err(err).
				Str("model", fmt.Sprintf("%T", model_temp)).
				Msg("迁移模型失败")
			return fmt.Errorf("迁移模型 %T 失败: %w", model_temp, err)
		}
		log.Info().
			Str("model", fmt.Sprintf("%T", model_temp)).
			Msg("模型迁移成功")
	}

	// 创建索引
	if err := createIndexes(db); err != nil {
		log.Error().
			Err(err).
			Msg("创建索引失败")
		return fmt.Errorf("创建索引失败: %w", err)
	}

	// 插入初始数据
	if err := seedData(db); err != nil {
		log.Error().
			Err(err).
			Msg("插入初始数据失败")
		return fmt.Errorf("插入初始数据失败: %w", err)
	}

	log.Info().Msg("数据库迁移完成")
	return nil
}

// createIndexes 创建数据库索引
func createIndexes(db *gorm.DB) error {
	log.Info().Msg("开始创建数据库索引")

	// 视频内容表索引 (video_contents)
	indexes := []string{
		"CREATE INDEX IF NOT EXISTS idx_video_contents_user_status ON video_contents(user_ksuid, status)",
		"CREATE INDEX IF NOT EXISTS idx_video_contents_type_rating ON video_contents(type, rating)",
		"CREATE INDEX IF NOT EXISTS idx_video_contents_created_at_published ON video_contents(created_at DESC) WHERE status = 'published'",
		"CREATE INDEX IF NOT EXISTS idx_video_contents_view_count ON video_contents(view_count DESC)",
		"CREATE INDEX IF NOT EXISTS idx_video_contents_created_at ON video_contents(created_at DESC)",
		"CREATE INDEX IF NOT EXISTS idx_video_contents_category_status ON video_contents(category_id, status)",
		"CREATE INDEX IF NOT EXISTS idx_video_contents_publish_at ON video_contents(publish_at)",
		"CREATE INDEX IF NOT EXISTS idx_video_contents_media_result ON video_contents(media_result_id)",
		"CREATE INDEX IF NOT EXISTS idx_video_contents_audit_task ON video_contents(audit_task_id)",
		"CREATE INDEX IF NOT EXISTS idx_video_contents_collaborators ON video_contents(has_collaborators, all_approved)",
		"CREATE INDEX IF NOT EXISTS idx_video_contents_like_count ON video_contents(like_count DESC)",
		"CREATE INDEX IF NOT EXISTS idx_video_contents_favorite_count ON video_contents(favorite_count DESC)",

		// 视频分类表索引 (video_categories)
		"CREATE INDEX IF NOT EXISTS idx_video_categories_parent_sort ON video_categories(parent_id, sort_order)",
		"CREATE INDEX IF NOT EXISTS idx_video_categories_level ON video_categories(level)",
		"CREATE INDEX IF NOT EXISTS idx_video_categories_sort_order ON video_categories(sort_order)",
		"CREATE INDEX IF NOT EXISTS idx_video_categories_active ON video_categories(is_active)",

		// 视频标签表索引 (video_tags)
		"CREATE INDEX IF NOT EXISTS idx_video_tags_type ON video_tags(type)",
		"CREATE INDEX IF NOT EXISTS idx_video_tags_use_count ON video_tags(use_count DESC)",

		// 视频内容标签关联表索引 (video_content_tags)
		"CREATE INDEX IF NOT EXISTS idx_video_content_tags_content ON video_content_tags(content_ksuid)",
		"CREATE INDEX IF NOT EXISTS idx_video_content_tags_tag ON video_content_tags(tag_id)",

		// 视频协作者表索引 (video_collaborators)
		"CREATE INDEX IF NOT EXISTS idx_video_collaborators_video ON video_collaborators(content_ksuid)",
		"CREATE INDEX IF NOT EXISTS idx_video_collaborators_user ON video_collaborators(user_ksuid)",
		"CREATE INDEX IF NOT EXISTS idx_video_collaborators_accepted ON video_collaborators(is_accepted)",
		"CREATE INDEX IF NOT EXISTS idx_video_collaborators_uploader ON video_collaborators(is_uploader)",
		"CREATE INDEX IF NOT EXISTS idx_video_collaborators_role_order ON video_collaborators(role_order)",

		// 视频评论表索引 (video_comments)
		"CREATE INDEX IF NOT EXISTS idx_video_comments_video_status ON video_comments(content_ksuid, status)",
		"CREATE INDEX IF NOT EXISTS idx_video_comments_user_status ON video_comments(user_ksuid, status)",
		"CREATE INDEX IF NOT EXISTS idx_video_comments_parent ON video_comments(parent_comment_ksuid)",
		"CREATE INDEX IF NOT EXISTS idx_video_comments_root ON video_comments(root_comment_ksuid)",
		"CREATE INDEX IF NOT EXISTS idx_video_comments_created_at ON video_comments(created_at DESC)",
		"CREATE INDEX IF NOT EXISTS idx_video_comments_like_count ON video_comments(like_count DESC)",
		"CREATE INDEX IF NOT EXISTS idx_video_comments_reply_to ON video_comments(reply_to_user_ksuid)",

		// 评论点赞表索引 (video_comment_likes) - 包含喜欢和不喜欢
		"CREATE INDEX IF NOT EXISTS idx_video_comment_likes_comment ON video_comment_likes(comment_ksuid)",
		"CREATE INDEX IF NOT EXISTS idx_video_comment_likes_user ON video_comment_likes(user_ksuid)",
		"CREATE INDEX IF NOT EXISTS idx_video_comment_likes_type ON video_comment_likes(type)",
		"CREATE INDEX IF NOT EXISTS idx_video_comment_likes_comment_type ON video_comment_likes(comment_ksuid, type)",
		"CREATE UNIQUE INDEX IF NOT EXISTS idx_video_comment_likes_unique ON video_comment_likes(comment_ksuid, user_ksuid)",

		// 视频点赞表索引 (video_likes) - 包含喜欢和不喜欢
		"CREATE INDEX IF NOT EXISTS idx_video_likes_video ON video_likes(content_ksuid)",
		"CREATE INDEX IF NOT EXISTS idx_video_likes_user ON video_likes(user_ksuid)",
		"CREATE INDEX IF NOT EXISTS idx_video_likes_type ON video_likes(type)",
		"CREATE INDEX IF NOT EXISTS idx_video_likes_content_type ON video_likes(content_ksuid, type)",
		"CREATE INDEX IF NOT EXISTS idx_video_likes_created_at ON video_likes(created_at DESC)",
		"CREATE UNIQUE INDEX IF NOT EXISTS idx_video_likes_unique ON video_likes(content_ksuid, user_ksuid)",

		// 视频收藏表索引 (video_favorites)
		"CREATE INDEX IF NOT EXISTS idx_video_favorites_video ON video_favorites(content_ksuid)",
		"CREATE INDEX IF NOT EXISTS idx_video_favorites_user ON video_favorites(user_ksuid)",
		"CREATE INDEX IF NOT EXISTS idx_video_favorites_created_at ON video_favorites(created_at DESC)",
		"CREATE UNIQUE INDEX IF NOT EXISTS idx_video_favorites_unique ON video_favorites(content_ksuid, user_ksuid)",

		// 用户创建重试任务表索引 (video_user_creation_retry_tasks)
		"CREATE INDEX IF NOT EXISTS idx_video_retry_tasks_video ON video_user_creation_retry_tasks(content_ksuid)",
		"CREATE INDEX IF NOT EXISTS idx_video_retry_tasks_status ON video_user_creation_retry_tasks(status)",
		"CREATE INDEX IF NOT EXISTS idx_video_retry_tasks_person ON video_user_creation_retry_tasks(person_name, person_type)",
		"CREATE INDEX IF NOT EXISTS idx_video_retry_tasks_next_retry ON video_user_creation_retry_tasks(next_retry_at)",
		"CREATE INDEX IF NOT EXISTS idx_video_retry_tasks_status_retry ON video_user_creation_retry_tasks(status, next_retry_at)",

		// 发布视频统计表索引 (video_publish_stats)
		//"CREATE UNIQUE INDEX IF NOT EXISTS idx_video_publish_stats_user_unique ON video_publish_stats(user_ksuid) WHERE deleted_at IS NULL",
		//"CREATE INDEX IF NOT EXISTS idx_video_publish_stats_category ON video_publish_stats(main_category)",
		//"CREATE INDEX IF NOT EXISTS idx_video_publish_stats_user_category ON video_publish_stats(user_ksuid, main_category)",
		//"CREATE INDEX IF NOT EXISTS idx_video_publish_stats_created_at ON video_publish_stats(created_at DESC)",
		//"CREATE INDEX IF NOT EXISTS idx_video_publish_stats_followers_count ON video_publish_stats(followers_count DESC)",

	}

	for _, indexSQL := range indexes {
		if err := db.Exec(indexSQL).Error; err != nil {
			log.Warn().
				Err(err).
				Str("sql", indexSQL).
				Msg("创建索引失败")
			// 继续执行其他索引，不中断迁移过程
		} else {
			log.Debug().
				Str("sql", indexSQL).
				Msg("索引创建成功")
		}
	}

	log.Info().Msg("数据库索引创建完成")
	return nil
}

// seedData 插入初始数据
func seedData(db *gorm.DB) error {
	log.Info().Msg("开始插入初始数据")

	// 检查是否已有数据，避免重复插入
	var categoryCount int64
	db.Model(&model.Category{}).Count(&categoryCount)
	if categoryCount > 0 {
		log.Info().
			Int64("count", categoryCount).
			Msg("数据库已有数据，跳过初始数据插入")
		return nil
	}

	// 创建默认分类 - 使用简化分类体系
	log.Info().Msg("开始插入默认分类")
	InsertCategories(db)

	log.Info().Msg("开始插入默认标签")
	InsertTags(db)

	log.Info().Msg("初始数据插入完成")
	return nil
}
