package main

import (
	"context"
	"fmt"
	"go.uber.org/fx"
	"pxpat-backend/cmd"
	"pxpat-backend/internal/content-cluster/help-service/routes"
	"pxpat-backend/internal/content-cluster/help-service/types"
	"pxpat-backend/pkg/consul"
	"pxpat-backend/pkg/consul/health"
	"pxpat-backend/pkg/middleware/cors"
	metricsMiddleware "pxpat-backend/pkg/middleware/metrics"
	tracingMiddleware "pxpat-backend/pkg/middleware/tracing"
	"pxpat-backend/pkg/opentelemetry"

	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog/log"
	"gorm.io/gorm"

	"pxpat-backend/internal/content-cluster/help-service/handler"
	"pxpat-backend/internal/content-cluster/help-service/migrations"
	"pxpat-backend/internal/content-cluster/help-service/repository"
	"pxpat-backend/internal/content-cluster/help-service/service"
	configLoader "pxpat-backend/pkg/config"
	"pxpat-backend/pkg/database"
	"pxpat-backend/pkg/logger"
)

// 提供配置
func provideConfig() *types.Config {
	clusterName := "content"
	serviceName := "help"

	return configLoader.Load[types.Config](configLoader.LoaderConfig{
		TypeName:    configLoader.Service,
		ClusterName: clusterName,
		ServiceName: serviceName,
		UseRedis:    false,
	})
}

// 提供日志器
func provideLogger(cfg *types.Config) error {
	serviceName := "help"

	// 初始化日志系统
	if err := logger.InitLogger(cfg.Log, serviceName); err != nil {
		log.Fatal().Msgf("日志系统初始化失败: %v", err)
		return err
	}

	return nil
}

// 提供OpenTelemetry Provider
func provideOtelProvider(cfg *types.Config) (*opentelemetry.Provider, error) {
	otelProvider, err := opentelemetry.NewProvider(cfg.Otlp)
	if err != nil {
		log.Fatal().Err(err).Msg("OpenTelemetry 初始化失败")
		return nil, err
	}

	if otelProvider.IsTracingEnabled() {
		log.Info().Str("service_name", cfg.Otlp.Tracing.ServiceName).
			Str("exporter_type", cfg.Otlp.Tracing.ExporterType).
			Msg("链路追踪已启用")
	} else {
		log.Info().Msg("链路追踪已禁用")
	}

	if otelProvider.IsMetricsEnabled() {
		log.Info().Str("service_name", cfg.Otlp.Metrics.ServiceName).
			Str("exporter_type", cfg.Otlp.Metrics.ExporterType).
			Msg("指标收集已启用")
	} else {
		log.Info().Msg("指标收集已禁用")
	}

	return otelProvider, nil
}

// 提供数据库连接
func provideDatabase(cfg *types.Config) (*gorm.DB, error) {
	db, err := database.ConnectDB(cfg.Database)
	if err != nil {
		log.Fatal().Msgf("数据库连接失败: %v", err)
		return nil, err
	}

	// 数据库迁移
	if err := migrations.AutoMigrate(db); err != nil {
		log.Fatal().Msgf("数据库迁移失败: %v", err)
		return nil, err
	}

	return db, nil
}

// 提供Repository
func provideHelpRepository(db *gorm.DB) *repository.HelpRepository {
	return repository.NewHelpRepository(db)
}

// 提供Service
func provideHelpService(helpRepo *repository.HelpRepository) *service.HelpService {
	return service.NewHelpService(helpRepo)
}

// 提供Handler
func provideHelpHandler(helpService *service.HelpService) *handler.HelpHandler {
	return handler.NewHelpHandler(helpService)
}

// 提供Consul管理器
func provideConsulManager(cfg *types.Config) (*consul.Manager, error) {
	consulManager, err := consul.NewManager(&cfg.Consul)
	if err != nil {
		log.Fatal().Err(err).Msg("Consul管理器初始化失败")
		return nil, err
	}
	return consulManager, nil
}

// 提供健康检查处理器
func provideHealthHandler(consulManager *consul.Manager, db *gorm.DB) *consul.HealthHandler {
	healthHandler := consul.NewHealthHandler(consulManager, "1.0.0")

	// 添加数据库健康检查
	healthHandler.AddChecker(health.NewDatabaseHealthChecker("database", func() error {
		sqlDB, err := db.DB()
		if err != nil {
			return err
		}
		return sqlDB.Ping()
	}))

	return healthHandler
}

// 提供Gin引擎
func provideGinEngine(
	cfg *types.Config,
	otelProvider *opentelemetry.Provider,
	healthHandler *consul.HealthHandler,
	helpHandler *handler.HelpHandler,
) *gin.Engine {
	r := gin.Default()

	// 添加中间件
	r.Use(cors.CORSMiddleware(cfg.Security.Cors))

	// 添加指标收集中间件
	if otelProvider.IsMetricsEnabled() {
		r.Use(metricsMiddleware.Middleware(otelProvider.MetricsProvider(), cfg.Otlp.Metrics.ServiceName))
	}

	// 添加链路追踪中间件
	if otelProvider.IsTracingEnabled() {
		r.Use(tracingMiddleware.Middleware(otelProvider.TracingProvider(), cfg.Otlp.Tracing.ServiceName))
	}

	// 注册健康检查路由
	healthHandler.RegisterHealthRoutes(r)

	// 注册帮助中心路由
	routes.RegisterRoutes(r, helpHandler)

	return r
}

// 应用生命周期管理
func manageLifecycle(
	lc fx.Lifecycle,
	cfg *types.Config,
	otelProvider *opentelemetry.Provider,
	consulManager *consul.Manager,
	ginEngine *gin.Engine,
) {
	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) error {
			// 启动Consul管理器

			if err := consulManager.Start(context.Background()); err != nil {
				fmt.Println("here")
				log.Fatal().Err(err).Msg("Consul管理器启动失败")
				return err
			}
			log.Info().Msg("Consul管理器启动成功")

			// 启动HTTP服务器（在goroutine中）
			go func() {
				log.Info().Msg("帮助中心服务启动中...")
				cmd.GraceStartAndClose(cfg.Server, ginEngine)
			}()

			return nil
		},
		OnStop: func(ctx context.Context) error {
			// 停止Consul管理器
			if err := consulManager.Stop(); err != nil {
				log.Error().Err(err).Msg("停止Consul管理器失败")
			} else {
				log.Info().Msg("Consul管理器停止成功")
			}

			// 关闭OpenTelemetry
			if err := otelProvider.Shutdown(ctx); err != nil {
				log.Error().Err(err).Msg("关闭 OpenTelemetry 失败")
			} else {
				log.Info().Msg("OpenTelemetry 关闭成功")
			}

			return nil
		},
	})
}

func main() {
	app := fx.New(
		// 提供依赖
		fx.Provide(
			provideConfig,
			provideOtelProvider,
			provideDatabase,
			provideHelpRepository,
			provideHelpService,
			provideHelpHandler,
			provideConsulManager,
			provideHealthHandler,
			provideGinEngine,
		),
		// 调用生命周期管理
		fx.Invoke(
			provideLogger,
			manageLifecycle,
		),
	)

	// 运行应用
	app.Run()
}
