package types

import (
	"time"
)

// ContentType 内容类型枚举
type ContentType string

const (
	ContentTypeVideo ContentType = "video"
	ContentTypeNovel ContentType = "novel"
	ContentTypeMusic ContentType = "music"
)

// ContentStatus 内容状态枚举
type ContentStatus string

const (
	ContentStatusDraft     ContentStatus = "draft"
	ContentStatusPublished ContentStatus = "published"
	ContentStatusArchived  ContentStatus = "archived"
	ContentStatusDeleted   ContentStatus = "deleted"
)

// BaseContent 基础内容模型（只包含所有内容类型都有的字段）
type BaseContent struct {
	ContentKSUID    string    `json:"content_ksuid"`
	ContentType     string    `json:"content_type"`     // video, novel, music
	Title          string    `json:"title"`
	Description    string    `json:"description"`
	UserKSUID      string    `json:"user_ksuid"`
	Status         string    `json:"status"`           // draft, published, archived, deleted

	// 基础统计（所有内容类型都有）
	ViewCount      int64     `json:"view_count"`      // 来自内容服务
	LikeCount      int64     `json:"like_count"`      // 来自interaction-service
	CommentCount   int64     `json:"comment_count"`   // 来自内容服务
	FavoriteCount  int64     `json:"favorite_count"`  // 来自interaction-service

	// 分类和标签
	CategoryID     uint      `json:"category_id"`
	CategoryName   string    `json:"category_name"`
	Tags           []string  `json:"tags"`

	// 时间信息
	CreatedAt      time.Time `json:"created_at"`
	UpdatedAt      time.Time `json:"updated_at"`
	PublishedAt    *time.Time `json:"published_at,omitempty"`

	// 来源信息
	SourceService  string    `json:"source_service"`   // video-service, novel-service
}

// ContentWithDetails 带详细信息的内容模型
type ContentWithDetails struct {
	BaseContent
	Details interface{} `json:"details"` // 具体内容类型的详细信息
}

// BaseContentList 基础内容列表
type BaseContentList struct {
	Contents   []*BaseContent `json:"contents"`
	Total      int            `json:"total"`
	Page       int            `json:"page"`
	Limit      int            `json:"limit"`
	TotalPages int            `json:"total_pages"`
}

// ContentWithDetailsList 带详细信息的内容列表
type ContentWithDetailsList struct {
	Contents    []*ContentWithDetails `json:"contents"`
	Total       int                   `json:"total"`
	Page        int                   `json:"page"`
	Limit       int                   `json:"limit"`
	TotalPages  int                   `json:"total_pages"`
	ContentType string                `json:"content_type"`
}

// VideoDetails 视频特有的详细信息
type VideoDetails struct {
	Duration     float64 `json:"duration"`              // 时长（秒）
	Resolution   string  `json:"resolution"`            // 分辨率
	FileSize     int64   `json:"file_size"`             // 文件大小
	Format       string  `json:"format"`                // 视频格式
	Orientation  string  `json:"orientation"`           // 横屏/竖屏
	Language     string  `json:"language"`              // 语言

	// 播放相关
	PlayURL      string  `json:"play_url"`
	CoverURL     string  `json:"cover_url"`
	PreviewURL   string  `json:"preview_url"`
	KeyFramesURL string  `json:"key_frames_url"`

	// 制作信息
	VideoID      string  `json:"video_id"`
	Director     string  `json:"director"`
	Actors       []string `json:"actors"`

	// 审核信息
	AuditTaskID  uint64  `json:"audit_task_id"`
	Level        string  `json:"level"`                 // A/B/C级别
}

// NovelDetails 小说特有的详细信息
type NovelDetails struct {
	ChapterCount    int64   `json:"chapter_count"`        // 章节数
	WordCount       int64   `json:"word_count"`           // 总字数
	IsCompleted     bool    `json:"is_completed"`         // 是否完结
	SerializationStatus string `json:"serialization_status"` // 连载状态

	// 作者信息
	AuthorKSUID     string  `json:"author_ksuid"`
	Translator      string  `json:"translator,omitempty"`
	OriginalWork    string  `json:"original_work,omitempty"`

	// 分级和定价
	Rating          string  `json:"rating"`               // general, teen, mature
	IsPaid          bool    `json:"is_paid"`              // 是否付费
	Price           float64 `json:"price,omitempty"`      // 价格

	// 封面和简介
	CoverURL        string  `json:"cover_url"`
	Synopsis        string  `json:"synopsis"`             // 作品简介

	// 统计信息
	SubscriberCount int64   `json:"subscriber_count"`     // 订阅数
	RewardCount     int64   `json:"reward_count"`         // 打赏数
	AverageRating   float64 `json:"average_rating"`       // 平均评分
}

// MusicDetails 音乐特有的详细信息（预留）
type MusicDetails struct {
	Duration        float64 `json:"duration"`             // 时长（秒）
	BitRate         int     `json:"bit_rate"`             // 比特率
	SampleRate      int     `json:"sample_rate"`          // 采样率
	Quality         string  `json:"quality"`              // 音质等级
	Format          string  `json:"format"`               // 音频格式
	FileSize        int64   `json:"file_size"`            // 文件大小

	// 音乐信息
	Artist          string  `json:"artist"`               // 艺术家
	Album           string  `json:"album"`                // 专辑
	Genre           string  `json:"genre"`                // 流派
	ReleaseYear     int     `json:"release_year"`         // 发行年份

	// 播放相关
	PlayURL         string  `json:"play_url"`
	CoverURL        string  `json:"cover_url"`
	LyricsURL       string  `json:"lyrics_url,omitempty"` // 歌词文件

	// 统计信息
	PlayCount       int64   `json:"play_count"`           // 播放次数
	DownloadCount   int64   `json:"download_count"`       // 下载次数
}

// ContentFilters 内容过滤器
type ContentFilters struct {
	ContentTypes []string `json:"content_types,omitempty"` // 内容类型过滤
	Status       string   `json:"status,omitempty"`        // 状态过滤
	UserKSUID    string   `json:"user_ksuid,omitempty"`    // 用户过滤
	CategoryID   uint     `json:"category_id,omitempty"`   // 分类过滤
	Tags         []string `json:"tags,omitempty"`          // 标签过滤
	
	// 时间范围过滤
	CreatedAfter  *time.Time `json:"created_after,omitempty"`
	CreatedBefore *time.Time `json:"created_before,omitempty"`
	
	// 排序和分页
	SortBy    string `json:"sort_by,omitempty"`    // 排序字段
	SortOrder string `json:"sort_order,omitempty"` // 排序方向 asc/desc
	Page      int    `json:"page,omitempty"`       // 页码
	Limit     int    `json:"limit,omitempty"`      // 每页数量
}

// InteractionStats 交互统计（包含点赞和收藏）
type InteractionStats struct {
	ContentKSUID  string `json:"content_ksuid"`
	LikeCount     int64  `json:"like_count"`
	DislikeCount  int64  `json:"dislike_count"`
	FavoriteCount int64  `json:"favorite_count"`
}

// LikeStats 点赞统计
type LikeStats struct {
	ContentKSUID string `json:"content_ksuid"`
	LikeCount    int64  `json:"like_count"`
	DislikeCount int64  `json:"dislike_count"`
}

// FavoriteStats 收藏统计
type FavoriteStats struct {
	ContentKSUID  string `json:"content_ksuid"`
	FavoriteCount int64  `json:"favorite_count"`
}

// UserInteractionStats 用户交互统计
type UserInteractionStats struct {
	UserKSUID           string `json:"user_ksuid"`
	TotalLikesGiven     int64  `json:"total_likes_given"`
	TotalLikesReceived  int64  `json:"total_likes_received"`
	TotalFavoritesGiven int64  `json:"total_favorites_given"`
	TotalFavoritesReceived int64 `json:"total_favorites_received"`
}

// OverallInteractionStats 总体交互统计
type OverallInteractionStats struct {
	TotalLikes     int64 `json:"total_likes"`
	TotalDislikes  int64 `json:"total_dislikes"`
	TotalFavorites int64 `json:"total_favorites"`
}

// ContentTypeStats 内容类型统计
type ContentTypeStats struct {
	ContentType    string  `json:"content_type"`
	TotalCount     int64   `json:"total_count"`
	PublishedCount int64   `json:"published_count"`
	DraftCount     int64   `json:"draft_count"`
	ArchivedCount  int64   `json:"archived_count"`
	DeletedCount   int64   `json:"deleted_count"`
	TotalViews     int64   `json:"total_views"`
	TotalLikes     int64   `json:"total_likes"`
	TotalComments  int64   `json:"total_comments"`
	TotalFavorites int64   `json:"total_favorites"`
	AvgViews       float64 `json:"avg_views"`
	AvgLikes       float64 `json:"avg_likes"`
}

// TrendData 趋势数据
type TrendData struct {
	Date  string `json:"date"`
	Value int64  `json:"value"`
}

// ContentTrends 内容趋势
type ContentTrends struct {
	Period        string      `json:"period"`
	ContentCounts []TrendData `json:"content_counts"`
	ViewCounts    []TrendData `json:"view_counts"`
	LikeCounts    []TrendData `json:"like_counts"`
	CommentCounts []TrendData `json:"comment_counts"`
}

// CategoryStats 分类统计
type CategoryStats struct {
	CategoryID    uint   `json:"category_id"`
	CategoryName  string `json:"category_name"`
	ContentCount  int64  `json:"content_count"`
	TotalViews    int64  `json:"total_views"`
	TotalLikes    int64  `json:"total_likes"`
	TotalComments int64  `json:"total_comments"`
}

// TagStats 标签统计
type TagStats struct {
	TagName         string  `json:"tag_name"`
	ContentCount    int64   `json:"content_count"`
	TotalViews      int64   `json:"total_views"`
	TotalLikes      int64   `json:"total_likes"`
	PopularityScore float64 `json:"popularity_score"`
}

// PopularContent 热门内容
type PopularContent struct {
	ContentKSUID    string    `json:"content_ksuid"`
	ContentType     string    `json:"content_type"`
	Title           string    `json:"title"`
	UserKSUID       string    `json:"user_ksuid"`
	ViewCount       int64     `json:"view_count"`
	LikeCount       int64     `json:"like_count"`
	CommentCount    int64     `json:"comment_count"`
	FavoriteCount   int64     `json:"favorite_count"`
	PopularityScore float64   `json:"popularity_score"`
	CreatedAt       time.Time `json:"created_at"`
}

// ActiveUser 活跃用户
type ActiveUser struct {
	UserKSUID     string    `json:"user_ksuid"`
	ContentCount  int64     `json:"content_count"`
	TotalViews    int64     `json:"total_views"`
	TotalLikes    int64     `json:"total_likes"`
	ActivityScore float64   `json:"activity_score"`
	LastActiveAt  time.Time `json:"last_active_at"`
}
