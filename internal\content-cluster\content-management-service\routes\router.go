package routes

import (
	"time"

	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
	swaggerFiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"

	"pxpat-backend/internal/content-cluster/content-management-service/external/handler"
	"pxpat-backend/internal/content-cluster/content-management-service/middleware"
	"pxpat-backend/pkg/jwt"
	"pxpat-backend/pkg/middleware/cors"
	"pxpat-backend/pkg/middleware/recovery"
	"pxpat-backend/pkg/middleware/request_id"
	"pxpat-backend/pkg/middleware/tracing"
)

// RouterConfig 路由配置
type RouterConfig struct {
	// 处理器
	ContentHandler *handler.ContentHandler
	StatsHandler   *handler.StatsHandler
	BatchHandler   *handler.BatchHandler

	// 中间件依赖
	JWTManager jwt.Manager
	Redis      *redis.Client

	// 配置
	EnableSwagger bool
	EnableCORS    bool
	Debug         bool
}

// SetupRouter 设置路由
func SetupRouter(config RouterConfig) *gin.Engine {
	// 设置Gin模式
	if !config.Debug {
		gin.SetMode(gin.ReleaseMode)
	}

	// 创建Gin引擎
	router := gin.New()

	// 设置全局中间件
	setupGlobalMiddleware(router, config)

	// 设置API路由
	setupAPIRoutes(router, config)

	// 设置Swagger文档路由
	if config.EnableSwagger {
		setupSwaggerRoutes(router)
	}

	return router
}

// setupGlobalMiddleware 设置全局中间件
func setupGlobalMiddleware(router *gin.Engine, config RouterConfig) {
	// 1. 请求ID中间件
	router.Use(request_id.RequestID())

	// 2. 链路追踪中间件
	router.Use(tracing.Tracing())

	// 3. 恢复中间件
	router.Use(recovery.Recovery())

	// 4. CORS中间件
	if config.EnableCORS {
		router.Use(cors.CORS())
	}

	// 5. 全局限流中间件（基于IP）
	rateLimitMiddleware := middleware.NewRateLimitMiddleware(config.Redis)
	router.Use(rateLimitMiddleware.IPRateLimit(1000, time.Hour)) // 每小时1000次请求
}

// setupAPIRoutes 设置API路由
func setupAPIRoutes(router *gin.Engine, config RouterConfig) {
	// 创建中间件实例
	authMiddleware := middleware.NewAuthMiddleware(config.JWTManager)
	permissionMiddleware := middleware.NewPermissionMiddleware()
	rateLimitMiddleware := middleware.NewRateLimitMiddleware(config.Redis)

	// API v1 路由组
	v1 := router.Group("/api/v1")
	{
		// 健康检查（无需认证）
		v1.GET("/health", config.ContentHandler.HealthCheck)

		// 管理API路由组
		management := v1.Group("/management")
		{
			// 设置内容管理路由
			setupContentRoutes(management, config, authMiddleware, permissionMiddleware, rateLimitMiddleware)

			// 设置统计分析路由
			setupStatsRoutes(management, config, authMiddleware, permissionMiddleware, rateLimitMiddleware)

			// 设置批量操作路由
			setupBatchRoutes(management, config, authMiddleware, permissionMiddleware, rateLimitMiddleware)
		}
	}
}

// setupContentRoutes 设置内容管理路由
func setupContentRoutes(
	group *gin.RouterGroup,
	config RouterConfig,
	authMiddleware *middleware.AuthMiddleware,
	permissionMiddleware *middleware.PermissionMiddleware,
	rateLimitMiddleware *middleware.RateLimitMiddleware,
) {
	// 内容管理路由组
	contents := group.Group("/contents")
	{
		// 公开接口（可选认证）
		contents.GET("", 
			authMiddleware.OptionalAuth(),
			rateLimitMiddleware.APIRateLimit(100, time.Minute), // 每分钟100次
			config.ContentHandler.GetBaseContents,
		)

		// 需要认证的接口
		authenticated := contents.Group("")
		authenticated.Use(authMiddleware.RequireAuth())
		authenticated.Use(permissionMiddleware.RequirePermission("content:read"))
		{
			// 内容详情
			authenticated.GET("/:content_ksuid",
				rateLimitMiddleware.APIRateLimit(200, time.Minute), // 每分钟200次
				config.ContentHandler.GetContentWithDetails,
			)

			// 更新内容状态（需要所有权或管理权限）
			authenticated.PUT("/:content_ksuid/status",
				permissionMiddleware.RequireAnyPermission("content:write", "content:manage"),
				permissionMiddleware.RequireContentOwnership(),
				rateLimitMiddleware.APIRateLimit(50, time.Minute), // 每分钟50次
				config.ContentHandler.UpdateContentStatus,
			)

			// 删除内容（需要所有权或管理权限）
			authenticated.DELETE("/:content_ksuid",
				permissionMiddleware.RequireAnyPermission("content:delete", "content:manage"),
				permissionMiddleware.RequireContentOwnership(),
				rateLimitMiddleware.APIRateLimit(20, time.Minute), // 每分钟20次
				config.ContentHandler.DeleteContent,
			)

			// 批量操作（需要管理权限）
			batch := authenticated.Group("/batch")
			batch.Use(permissionMiddleware.RequirePermission("batch:operate"))
			batch.Use(rateLimitMiddleware.BatchOperationRateLimit(10, time.Minute)) // 每分钟10次批量操作
			{
				batch.PUT("/status", config.ContentHandler.BatchUpdateContentStatus)
				batch.POST("/delete", config.ContentHandler.BatchDeleteContents)
			}
		}
	}

	// 用户内容管理路由
	users := group.Group("/users")
	users.Use(authMiddleware.RequireAuth())
	users.Use(permissionMiddleware.RequirePermission("content:read"))
	{
		// 获取用户的所有内容
		users.GET("/:user_ksuid/contents",
			rateLimitMiddleware.APIRateLimit(100, time.Minute),
			config.ContentHandler.GetUserAllContents,
		)

		// 获取用户指定类型的内容
		users.GET("/:user_ksuid/contents/:content_type",
			rateLimitMiddleware.APIRateLimit(100, time.Minute),
			config.ContentHandler.GetUserContentsByType,
		)
	}

	// 按类型管理路由
	videos := group.Group("/videos")
	videos.Use(authMiddleware.RequireAuth())
	videos.Use(permissionMiddleware.RequirePermission("content:read"))
	{
		videos.GET("",
			rateLimitMiddleware.APIRateLimit(100, time.Minute),
			config.ContentHandler.GetVideoContents,
		)
	}
}

// setupStatsRoutes 设置统计分析路由
func setupStatsRoutes(
	group *gin.RouterGroup,
	config RouterConfig,
	authMiddleware *middleware.AuthMiddleware,
	permissionMiddleware *middleware.PermissionMiddleware,
	rateLimitMiddleware *middleware.RateLimitMiddleware,
) {
	// 统计分析路由组
	stats := group.Group("/stats")
	stats.Use(authMiddleware.RequireAuth())
	stats.Use(permissionMiddleware.RequirePermission("stats:read"))
	stats.Use(rateLimitMiddleware.APIRateLimit(50, time.Minute)) // 每分钟50次
	{
		// 总体统计
		stats.GET("/overview", config.StatsHandler.GetOverviewStats)
		stats.GET("/content-types", config.StatsHandler.GetContentTypeStats)
		
		// 用户统计
		stats.GET("/users", config.StatsHandler.GetUserStats)
		stats.GET("/users/active", config.StatsHandler.GetActiveUsers)
		
		// 趋势分析
		stats.GET("/trends", config.StatsHandler.GetContentTrends)
		stats.GET("/popular", config.StatsHandler.GetPopularContents)
		
		// 分类和标签统计
		stats.GET("/categories", config.StatsHandler.GetCategoryStats)
		stats.GET("/tags", config.StatsHandler.GetTagStats)
		
		// 交互统计
		stats.GET("/interactions", config.StatsHandler.GetInteractionStats)
		stats.GET("/interactions/overall", config.StatsHandler.GetOverallInteractionStats)
		
		// 仪表板统计
		stats.GET("/dashboard", config.StatsHandler.GetDashboardStats)
		
		// 报告生成和获取
		stats.POST("/reports", config.StatsHandler.GenerateReport)
		stats.GET("/reports/:report_id", config.StatsHandler.GetReport)
	}
}

// setupBatchRoutes 设置批量操作路由
func setupBatchRoutes(
	group *gin.RouterGroup,
	config RouterConfig,
	authMiddleware *middleware.AuthMiddleware,
	permissionMiddleware *middleware.PermissionMiddleware,
	rateLimitMiddleware *middleware.RateLimitMiddleware,
) {
	// 批量操作路由组
	batch := group.Group("/batch")
	batch.Use(authMiddleware.RequireAuth())
	batch.Use(permissionMiddleware.RequirePermission("batch:operate"))
	batch.Use(rateLimitMiddleware.BatchOperationRateLimit(5, time.Minute)) // 每分钟5次批量操作
	{
		// 批量状态操作
		batch.PUT("/status", config.BatchHandler.BatchUpdateStatus)
		batch.POST("/delete", config.BatchHandler.BatchDelete)
		batch.POST("/publish", config.BatchHandler.BatchPublish)
		batch.POST("/archive", config.BatchHandler.BatchArchive)
		
		// 批量内容操作
		batch.PUT("/category", config.BatchHandler.BatchUpdateCategory)
		batch.PUT("/tags", config.BatchHandler.BatchUpdateTags)
		batch.PUT("/ownership", config.BatchHandler.BatchTransferOwnership)
		
		// 内容搜索
		batch.POST("/search", config.BatchHandler.SearchContents)
		
		// 操作日志管理
		logs := batch.Group("/logs")
		{
			logs.GET("", config.BatchHandler.GetOperationLogs)
			logs.GET("/stats", config.BatchHandler.GetOperationLogStats)
		}
		
		// 配置管理
		configs := batch.Group("/configs")
		{
			configs.GET("", config.BatchHandler.GetConfigs)
			configs.PUT("/:config_key", config.BatchHandler.UpdateConfig)
			configs.GET("/stats", config.BatchHandler.GetConfigStats)
		}
		
		// 系统管理
		system := batch.Group("/system")
		{
			system.GET("/stats", config.BatchHandler.GetSystemStats)
			system.GET("/health", config.BatchHandler.GetServiceHealth)
		}
	}
}

// setupSwaggerRoutes 设置Swagger文档路由
func setupSwaggerRoutes(router *gin.Engine) {
	// Swagger文档路由
	router.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerFiles.Handler))
	
	// 重定向根路径到Swagger文档
	router.GET("/", func(c *gin.Context) {
		c.Redirect(302, "/swagger/index.html")
	})
}
