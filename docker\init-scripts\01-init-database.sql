-- 内容管理服务数据库初始化脚本

-- 创建数据库（如果不存在）
-- 注意：在Docker环境中，数据库已经通过环境变量创建

-- 设置时区
SET timezone = 'Asia/Shanghai';

-- 创建扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";
CREATE EXTENSION IF NOT EXISTS "btree_gin";

-- 创建操作日志表
CREATE TABLE IF NOT EXISTS operation_logs (
    id BIGSERIAL PRIMARY KEY,
    user_ksuid VARCHAR(27) NOT NULL,
    operation VARCHAR(100) NOT NULL,
    resource_type VARCHAR(50) NOT NULL,
    resource_ksuid VARCHAR(27) NOT NULL,
    details JSONB,
    ip_address INET,
    user_agent TEXT,
    status VARCHAR(20) NOT NULL DEFAULT 'success',
    error_message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- 索引
    INDEX idx_operation_logs_user_ksuid (user_ksuid),
    INDEX idx_operation_logs_operation (operation),
    INDEX idx_operation_logs_resource (resource_type, resource_ksuid),
    INDEX idx_operation_logs_created_at (created_at),
    INDEX idx_operation_logs_status (status),
    
    -- GIN索引用于JSONB查询
    INDEX idx_operation_logs_details_gin ON operation_logs USING GIN (details)
);

-- 创建内容缓存表
CREATE TABLE IF NOT EXISTS content_caches (
    id BIGSERIAL PRIMARY KEY,
    content_ksuid VARCHAR(27) NOT NULL UNIQUE,
    content_type VARCHAR(50) NOT NULL,
    cache_data JSONB NOT NULL,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- 索引
    INDEX idx_content_caches_content_ksuid (content_ksuid),
    INDEX idx_content_caches_content_type (content_type),
    INDEX idx_content_caches_expires_at (expires_at),
    INDEX idx_content_caches_created_at (created_at),
    
    -- GIN索引用于JSONB查询
    INDEX idx_content_caches_data_gin ON content_caches USING GIN (cache_data)
);

-- 创建管理配置表
CREATE TABLE IF NOT EXISTS management_configs (
    id BIGSERIAL PRIMARY KEY,
    config_key VARCHAR(100) NOT NULL UNIQUE,
    config_value JSONB NOT NULL,
    category VARCHAR(50) NOT NULL DEFAULT 'general',
    description TEXT,
    is_active BOOLEAN NOT NULL DEFAULT true,
    created_by VARCHAR(27) NOT NULL,
    updated_by VARCHAR(27),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- 索引
    INDEX idx_management_configs_key (config_key),
    INDEX idx_management_configs_category (category),
    INDEX idx_management_configs_active (is_active),
    INDEX idx_management_configs_created_at (created_at),
    
    -- GIN索引用于JSONB查询
    INDEX idx_management_configs_value_gin ON management_configs USING GIN (config_value)
);

-- 创建触发器函数：自动更新updated_at字段
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为需要的表创建触发器
CREATE TRIGGER update_content_caches_updated_at 
    BEFORE UPDATE ON content_caches 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_management_configs_updated_at 
    BEFORE UPDATE ON management_configs 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 插入默认配置数据
INSERT INTO management_configs (config_key, config_value, category, description, created_by) VALUES
('cache.content.ttl', '300', 'cache', '内容缓存TTL（秒）', 'system'),
('cache.stats.ttl', '600', 'cache', '统计缓存TTL（秒）', 'system'),
('cache.user.ttl', '900', 'cache', '用户缓存TTL（秒）', 'system'),
('batch.max_size', '1000', 'batch', '批量操作最大数量', 'system'),
('query.page_size.default', '20', 'query', '默认分页大小', 'system'),
('query.page_size.max', '100', 'query', '最大分页大小', 'system'),
('rate_limit.default.limit', '100', 'rate_limit', '默认限流次数', 'system'),
('rate_limit.default.window', '60', 'rate_limit', '默认限流窗口（秒）', 'system'),
('rate_limit.batch.limit', '10', 'rate_limit', '批量操作限流次数', 'system'),
('rate_limit.batch.window', '60', 'rate_limit', '批量操作限流窗口（秒）', 'system')
ON CONFLICT (config_key) DO NOTHING;

-- 创建分区表（用于大量数据的操作日志）
-- 按月分区操作日志表
CREATE TABLE IF NOT EXISTS operation_logs_y2024m01 PARTITION OF operation_logs
    FOR VALUES FROM ('2024-01-01') TO ('2024-02-01');

CREATE TABLE IF NOT EXISTS operation_logs_y2024m02 PARTITION OF operation_logs
    FOR VALUES FROM ('2024-02-01') TO ('2024-03-01');

CREATE TABLE IF NOT EXISTS operation_logs_y2024m03 PARTITION OF operation_logs
    FOR VALUES FROM ('2024-03-01') TO ('2024-04-01');

-- 创建清理过期数据的函数
CREATE OR REPLACE FUNCTION cleanup_expired_cache()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM content_caches WHERE expires_at < CURRENT_TIMESTAMP;
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    -- 记录清理日志
    INSERT INTO operation_logs (user_ksuid, operation, resource_type, resource_ksuid, details, status)
    VALUES ('system', 'cleanup_expired_cache', 'cache', 'system', 
            json_build_object('deleted_count', deleted_count), 'success');
    
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- 创建清理旧操作日志的函数
CREATE OR REPLACE FUNCTION cleanup_old_operation_logs(days_to_keep INTEGER DEFAULT 90)
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
    cutoff_date TIMESTAMP WITH TIME ZONE;
BEGIN
    cutoff_date := CURRENT_TIMESTAMP - INTERVAL '1 day' * days_to_keep;
    
    DELETE FROM operation_logs WHERE created_at < cutoff_date;
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    -- 记录清理日志
    INSERT INTO operation_logs (user_ksuid, operation, resource_type, resource_ksuid, details, status)
    VALUES ('system', 'cleanup_old_operation_logs', 'logs', 'system', 
            json_build_object('deleted_count', deleted_count, 'cutoff_date', cutoff_date), 'success');
    
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- 创建统计视图
CREATE OR REPLACE VIEW operation_stats AS
SELECT 
    operation,
    resource_type,
    status,
    COUNT(*) as count,
    DATE_TRUNC('day', created_at) as date
FROM operation_logs 
WHERE created_at >= CURRENT_TIMESTAMP - INTERVAL '30 days'
GROUP BY operation, resource_type, status, DATE_TRUNC('day', created_at)
ORDER BY date DESC, count DESC;

-- 创建缓存统计视图
CREATE OR REPLACE VIEW cache_stats AS
SELECT 
    content_type,
    COUNT(*) as total_count,
    COUNT(CASE WHEN expires_at > CURRENT_TIMESTAMP THEN 1 END) as active_count,
    COUNT(CASE WHEN expires_at <= CURRENT_TIMESTAMP THEN 1 END) as expired_count,
    AVG(EXTRACT(EPOCH FROM (expires_at - created_at))) as avg_ttl_seconds
FROM content_caches
GROUP BY content_type;

-- 设置表注释
COMMENT ON TABLE operation_logs IS '操作日志表，记录所有用户操作';
COMMENT ON TABLE content_caches IS '内容缓存表，存储临时缓存数据';
COMMENT ON TABLE management_configs IS '管理配置表，存储系统配置参数';

-- 设置列注释
COMMENT ON COLUMN operation_logs.user_ksuid IS '操作用户的KSUID';
COMMENT ON COLUMN operation_logs.operation IS '操作类型';
COMMENT ON COLUMN operation_logs.resource_type IS '资源类型';
COMMENT ON COLUMN operation_logs.resource_ksuid IS '资源KSUID';
COMMENT ON COLUMN operation_logs.details IS '操作详情JSON数据';

COMMENT ON COLUMN content_caches.content_ksuid IS '内容KSUID，唯一标识';
COMMENT ON COLUMN content_caches.content_type IS '内容类型';
COMMENT ON COLUMN content_caches.cache_data IS '缓存数据JSON';
COMMENT ON COLUMN content_caches.expires_at IS '缓存过期时间';

COMMENT ON COLUMN management_configs.config_key IS '配置键，唯一标识';
COMMENT ON COLUMN management_configs.config_value IS '配置值JSON数据';
COMMENT ON COLUMN management_configs.category IS '配置分类';

-- 完成初始化
SELECT 'Database initialization completed successfully' as status;
