package main

import (
	"context"
	"pxpat-backend/cmd"
	"pxpat-backend/internal/finance-cluster/dividend-service/handler"
	"pxpat-backend/internal/finance-cluster/dividend-service/model"
	"pxpat-backend/internal/finance-cluster/dividend-service/repository/impl"
	"pxpat-backend/internal/finance-cluster/dividend-service/routes"
	"pxpat-backend/internal/finance-cluster/dividend-service/service"
	"pxpat-backend/internal/finance-cluster/dividend-service/types"
	"pxpat-backend/pkg/auth"
	configLoader "pxpat-backend/pkg/config"
	"pxpat-backend/pkg/consul"
	"pxpat-backend/pkg/consul/health"
	database2 "pxpat-backend/pkg/database"
	"pxpat-backend/pkg/logger"
	"pxpat-backend/pkg/middleware/cors"

	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog/log"
)

func main() {
	clusterName := "finance"
	serviceName := "dividend"

	// 加载配置
	cfg := configLoader.Load[types.Config](configLoader.LoaderConfig{
		TypeName:    configLoader.Service,
		ClusterName: clusterName,
		ServiceName: serviceName,
		UseRedis:    false,
	})

	// 初始化日志系统
	if err := logger.InitLogger(cfg.Log, serviceName); err != nil {
		log.Fatal().Err(err).Msg("Failed to initialize logger")
	}

	// 获取服务专用日志器
	log.Info().Msg("Dividend service starting...")

	// 初始化数据库
	db, err := database2.ConnectDB(cfg.Database)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to initialize database")
	}
	log.Info().Msg("Database connected successfully")

	// 自动迁移数据库表结构
	if err := db.AutoMigrate(
		&model.CreatorReward{},
		&model.RewardRecord{},
		&model.DividendHistory{},
	); err != nil {
		log.Fatal().Err(err).Msg("Database migration failed")
	}
	log.Info().Msg("Database migration completed")

	// 初始化仓储层
	dividendRepo := impl.NewDividendRepository(db)
	creatorRewardRepo := impl.NewCreatorRewardRepository(db)

	// 初始化服务层
	dividendService := service.NewDividendService(
		dividendRepo,
		creatorRewardRepo,
	)

	// 初始化处理器
	dividendHandler := handler.NewDividendHandler(dividendService)

	// 初始化JWT管理器
	jwtManager := auth.NewJWTManager(cfg.JWT.SecretKey, cfg.JWT.Expiration, cfg.JWT.Issuer)
	log.Info().Msg("JWT manager initialized")

	// 设置Gin模式
	gin.SetMode(cfg.Server.Mode)
	log.Info().Str("mode", cfg.Server.Mode).Msg("Gin mode set")

	// 创建路由
	router := gin.Default()

	// 添加中间件
	router.Use(cors.CORSMiddleware(cfg.Security.CORS))
	log.Info().Msg("Middleware configured")

	// 注册路由
	routes.RegisterRoutes(router, dividendHandler, &jwtManager)
	log.Info().Msg("Routes registered")

	// 初始化Consul管理器
	consulManager, err := consul.NewManager(&cfg.Consul)
	if err != nil {
		log.Fatal().Err(err).Msg("Consul管理器初始化失败")
	}

	// 启动Consul管理器
	ctx := context.Background()
	if err := consulManager.Start(ctx); err != nil {
		log.Fatal().Err(err).Msg("Consul管理器启动失败")
	}

	// 确保在程序退出时停止Consul管理器
	defer func() {
		if err := consulManager.Stop(); err != nil {
			log.Error().Err(err).Msg("停止Consul管理器失败")
		}
	}()

	// 初始化健康检查处理器
	healthHandler := consul.NewHealthHandler(consulManager, "1.0.0")

	// 添加数据库健康检查
	healthHandler.AddChecker(health.NewDatabaseHealthChecker("database", func() error {
		sqlDB, err := db.DB()
		if err != nil {
			return err
		}
		return sqlDB.Ping()
	}))

	// 注册健康检查路由
	healthHandler.RegisterHealthRoutes(router)

	// 启动服务器
	log.Info().Int("port", cfg.Server.Port).Msg("Starting dividend service server")
	cmd.GraceStartAndClose(cfg.Server, router)
}
