#!/bin/bash

# 生成自签名SSL证书脚本
# 仅用于测试环境，生产环境请使用正式的SSL证书

echo "正在生成自签名SSL证书..."

# 生成私钥
openssl genrsa -out pxpat.cc.key 2048

# 生成证书签名请求
openssl req -new -key pxpat.cc.key -out pxpat.cc.csr -subj "/C=CN/ST=Beijing/L=Beijing/O=PXPAT/CN=*.pxpat.cc"

# 生成自签名证书
openssl x509 -req -days 365 -in pxpat.cc.csr -signkey pxpat.cc.key -out pxpat.cc.crt

# 设置权限
chmod 600 pxpat.cc.key
chmod 644 pxpat.cc.crt

# 清理临时文件
rm pxpat.cc.csr

echo "SSL证书生成完成！"
echo "证书文件: pxpat.cc.crt"
echo "私钥文件: pxpat.cc.key"
echo ""
echo "注意: 这是自签名证书，浏览器会显示安全警告。"
echo "生产环境请使用正式的SSL证书。"
