package handler

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog/log"

	"pxpat-backend/internal/content-cluster/content-management-service/dto"
	"pxpat-backend/internal/content-cluster/content-management-service/external/service"
	"pxpat-backend/pkg/errors"
	"pxpat-backend/pkg/ksuid"
	"pxpat-backend/pkg/middleware/tracing"
	"pxpat-backend/pkg/opentelemetry"
	globalTypes "pxpat-backend/pkg/types"
)

// BatchHandler 批量操作处理器
type BatchHandler struct {
	batchService service.BatchOperationService
}

// NewBatchHandler 创建批量操作处理器实例
func NewBatchHandler(batchService service.BatchOperationService) *BatchHandler {
	return &BatchHandler{
		batchService: batchService,
	}
}

// BatchUpdateStatus 批量更新状态
// @Summary 批量更新状态
// @Description 批量更新多个内容的状态
// @Tags 批量操作
// @Accept json
// @Produce json
// @Param request body dto.BatchUpdateStatusRequest true "批量更新状态请求"
// @Success 200 {object} globalTypes.GlobalResponse{data=dto.BatchOperationResponse}
// @Failure 400 {object} globalTypes.GlobalResponse
// @Failure 500 {object} globalTypes.GlobalResponse
// @Router /api/v1/management/batch/status [put]
func (h *BatchHandler) BatchUpdateStatus(c *gin.Context) {
	// 1. 链路追踪
	ctx, span := tracing.StartSpanWithComponent(c.Request.Context(), "handler", "BatchUpdateStatus")
	defer span.End()
	c.Request = c.Request.WithContext(ctx)

	// 2. 获取trace信息和用户信息
	traceID := tracing.GetTraceID(c)
	spanID := tracing.GetSpanID(c)
	userKSUID := ksuid.GetKSUID(c)

	// 3. 添加span属性
	opentelemetry.AddAttribute(span, "method", c.Request.Method)
	opentelemetry.AddAttribute(span, "path", c.Request.URL.Path)
	opentelemetry.AddAttribute(span, "user_ksuid", userKSUID)

	// 4. 记录请求日志
	log.Info().
		Str("trace_id", traceID).
		Str("span_id", spanID).
		Str("user_ksuid", userKSUID).
		Msg("收到批量更新状态请求")

	// 5. 参数绑定
	var req dto.BatchUpdateStatusRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error().
			Str("trace_id", traceID).
			Err(err).
			Msg("参数绑定失败")

		opentelemetry.AddError(span, err)
		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code: errors.INVALID_PARAMETER,
			Msg:  "参数格式错误",
		})
		return
	}

	// 6. 添加请求参数到span
	opentelemetry.AddAttribute(span, "content_count", len(req.ContentKSUIDs))
	opentelemetry.AddAttribute(span, "new_status", req.Status)

	// 7. 调用服务层
	result, gErr := h.batchService.BatchUpdateStatus(ctx, userKSUID, &req)
	if gErr != nil {
		log.Error().
			Str("trace_id", traceID).
			Int("content_count", len(req.ContentKSUIDs)).
			Str("new_status", req.Status).
			Interface("error", gErr).
			Msg("批量更新状态失败")

		opentelemetry.AddError(span, gErr)
		
		// 根据错误类型返回不同的HTTP状态码
		statusCode := http.StatusInternalServerError
		if gErr.Code == errors.INVALID_PARAMETER {
			statusCode = http.StatusBadRequest
		}

		c.JSON(statusCode, globalTypes.GlobalResponse{
			Code: gErr.Code,
			Msg:  gErr.Message,
		})
		return
	}

	// 8. 返回成功响应
	log.Info().
		Str("trace_id", traceID).
		Int("success_count", result.SuccessCount).
		Int("failure_count", result.FailureCount).
		Str("new_status", req.Status).
		Msg("批量更新状态完成")

	c.JSON(http.StatusOK, globalTypes.GlobalResponse{
		Code: errors.SUCCESS,
		Data: result,
	})
}

// BatchDelete 批量删除
// @Summary 批量删除
// @Description 批量删除多个内容
// @Tags 批量操作
// @Accept json
// @Produce json
// @Param request body dto.BatchDeleteRequest true "批量删除请求"
// @Success 200 {object} globalTypes.GlobalResponse{data=dto.BatchOperationResponse}
// @Failure 400 {object} globalTypes.GlobalResponse
// @Failure 500 {object} globalTypes.GlobalResponse
// @Router /api/v1/management/batch/delete [post]
func (h *BatchHandler) BatchDelete(c *gin.Context) {
	// 1. 链路追踪
	ctx, span := tracing.StartSpanWithComponent(c.Request.Context(), "handler", "BatchDelete")
	defer span.End()
	c.Request = c.Request.WithContext(ctx)

	// 2. 获取trace信息和用户信息
	traceID := tracing.GetTraceID(c)
	spanID := tracing.GetSpanID(c)
	userKSUID := ksuid.GetKSUID(c)

	// 3. 添加span属性
	opentelemetry.AddAttribute(span, "method", c.Request.Method)
	opentelemetry.AddAttribute(span, "path", c.Request.URL.Path)
	opentelemetry.AddAttribute(span, "user_ksuid", userKSUID)

	// 4. 记录请求日志
	log.Info().
		Str("trace_id", traceID).
		Str("span_id", spanID).
		Str("user_ksuid", userKSUID).
		Msg("收到批量删除请求")

	// 5. 参数绑定
	var req dto.BatchDeleteRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error().
			Str("trace_id", traceID).
			Err(err).
			Msg("参数绑定失败")

		opentelemetry.AddError(span, err)
		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code: errors.INVALID_PARAMETER,
			Msg:  "参数格式错误",
		})
		return
	}

	// 6. 添加请求参数到span
	opentelemetry.AddAttribute(span, "content_count", len(req.ContentKSUIDs))

	// 7. 调用服务层
	result, gErr := h.batchService.BatchDelete(ctx, userKSUID, &req)
	if gErr != nil {
		log.Error().
			Str("trace_id", traceID).
			Int("content_count", len(req.ContentKSUIDs)).
			Interface("error", gErr).
			Msg("批量删除失败")

		opentelemetry.AddError(span, gErr)
		
		// 根据错误类型返回不同的HTTP状态码
		statusCode := http.StatusInternalServerError
		if gErr.Code == errors.INVALID_PARAMETER {
			statusCode = http.StatusBadRequest
		}

		c.JSON(statusCode, globalTypes.GlobalResponse{
			Code: gErr.Code,
			Msg:  gErr.Message,
		})
		return
	}

	// 8. 返回成功响应
	log.Info().
		Str("trace_id", traceID).
		Int("success_count", result.SuccessCount).
		Int("failure_count", result.FailureCount).
		Msg("批量删除完成")

	c.JSON(http.StatusOK, globalTypes.GlobalResponse{
		Code: errors.SUCCESS,
		Data: result,
	})
}

// BatchPublish 批量发布
// @Summary 批量发布
// @Description 批量发布多个内容
// @Tags 批量操作
// @Accept json
// @Produce json
// @Param request body dto.BatchContentKSUIDsRequest true "批量发布请求"
// @Success 200 {object} globalTypes.GlobalResponse{data=dto.BatchOperationResponse}
// @Failure 400 {object} globalTypes.GlobalResponse
// @Failure 500 {object} globalTypes.GlobalResponse
// @Router /api/v1/management/batch/publish [post]
func (h *BatchHandler) BatchPublish(c *gin.Context) {
	// 1. 链路追踪
	ctx, span := tracing.StartSpanWithComponent(c.Request.Context(), "handler", "BatchPublish")
	defer span.End()
	c.Request = c.Request.WithContext(ctx)

	// 2. 获取trace信息和用户信息
	traceID := tracing.GetTraceID(c)
	spanID := tracing.GetSpanID(c)
	userKSUID := ksuid.GetKSUID(c)

	// 3. 添加span属性
	opentelemetry.AddAttribute(span, "method", c.Request.Method)
	opentelemetry.AddAttribute(span, "path", c.Request.URL.Path)
	opentelemetry.AddAttribute(span, "user_ksuid", userKSUID)

	// 4. 记录请求日志
	log.Info().
		Str("trace_id", traceID).
		Str("span_id", spanID).
		Str("user_ksuid", userKSUID).
		Msg("收到批量发布请求")

	// 5. 参数绑定
	var req dto.BatchContentKSUIDsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error().
			Str("trace_id", traceID).
			Err(err).
			Msg("参数绑定失败")

		opentelemetry.AddError(span, err)
		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code: errors.INVALID_PARAMETER,
			Msg:  "参数格式错误",
		})
		return
	}

	// 6. 添加请求参数到span
	opentelemetry.AddAttribute(span, "content_count", len(req.ContentKSUIDs))

	// 7. 调用服务层
	result, gErr := h.batchService.BatchPublish(ctx, userKSUID, req.ContentKSUIDs)
	if gErr != nil {
		log.Error().
			Str("trace_id", traceID).
			Int("content_count", len(req.ContentKSUIDs)).
			Interface("error", gErr).
			Msg("批量发布失败")

		opentelemetry.AddError(span, gErr)
		
		// 根据错误类型返回不同的HTTP状态码
		statusCode := http.StatusInternalServerError
		if gErr.Code == errors.INVALID_PARAMETER {
			statusCode = http.StatusBadRequest
		}

		c.JSON(statusCode, globalTypes.GlobalResponse{
			Code: gErr.Code,
			Msg:  gErr.Message,
		})
		return
	}

	// 8. 返回成功响应
	log.Info().
		Str("trace_id", traceID).
		Int("success_count", result.SuccessCount).
		Int("failure_count", result.FailureCount).
		Msg("批量发布完成")

	c.JSON(http.StatusOK, globalTypes.GlobalResponse{
		Code: errors.SUCCESS,
		Data: result,
	})
}

// BatchUnpublish 批量取消发布
// @Summary 批量取消发布
// @Description 批量取消发布多个内容
// @Tags 批量操作
// @Accept json
// @Produce json
// @Param request body dto.BatchContentKSUIDsRequest true "批量取消发布请求"
// @Success 200 {object} globalTypes.GlobalResponse{data=dto.BatchOperationResponse}
// @Failure 400 {object} globalTypes.GlobalResponse
// @Failure 500 {object} globalTypes.GlobalResponse
// @Router /api/v1/management/batch/unpublish [post]
func (h *BatchHandler) BatchUnpublish(c *gin.Context) {
	// 1. 链路追踪
	ctx, span := tracing.StartSpanWithComponent(c.Request.Context(), "handler", "BatchUnpublish")
	defer span.End()
	c.Request = c.Request.WithContext(ctx)

	// 2. 获取trace信息和用户信息
	traceID := tracing.GetTraceID(c)
	spanID := tracing.GetSpanID(c)
	userKSUID := ksuid.GetKSUID(c)

	// 3. 添加span属性
	opentelemetry.AddAttribute(span, "method", c.Request.Method)
	opentelemetry.AddAttribute(span, "path", c.Request.URL.Path)
	opentelemetry.AddAttribute(span, "user_ksuid", userKSUID)

	// 4. 记录请求日志
	log.Info().
		Str("trace_id", traceID).
		Str("span_id", spanID).
		Str("user_ksuid", userKSUID).
		Msg("收到批量取消发布请求")

	// 5. 参数绑定
	var req dto.BatchContentKSUIDsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error().
			Str("trace_id", traceID).
			Err(err).
			Msg("参数绑定失败")

		opentelemetry.AddError(span, err)
		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code: errors.INVALID_PARAMETER,
			Msg:  "参数格式错误",
		})
		return
	}

	// 6. 添加请求参数到span
	opentelemetry.AddAttribute(span, "content_count", len(req.ContentKSUIDs))

	// 7. 调用服务层
	result, gErr := h.batchService.BatchUnpublish(ctx, userKSUID, req.ContentKSUIDs)
	if gErr != nil {
		log.Error().
			Str("trace_id", traceID).
			Int("content_count", len(req.ContentKSUIDs)).
			Interface("error", gErr).
			Msg("批量取消发布失败")

		opentelemetry.AddError(span, gErr)

		// 根据错误类型返回不同的HTTP状态码
		statusCode := http.StatusInternalServerError
		if gErr.Code == errors.INVALID_PARAMETER {
			statusCode = http.StatusBadRequest
		}

		c.JSON(statusCode, globalTypes.GlobalResponse{
			Code: gErr.Code,
			Msg:  gErr.Message,
		})
		return
	}

	// 8. 返回成功响应
	log.Info().
		Str("trace_id", traceID).
		Int("success_count", result.SuccessCount).
		Int("failure_count", result.FailureCount).
		Msg("批量取消发布完成")

	c.JSON(http.StatusOK, globalTypes.GlobalResponse{
		Code: errors.SUCCESS,
		Data: result,
	})
}

// BatchArchive 批量归档
// @Summary 批量归档
// @Description 批量归档多个内容
// @Tags 批量操作
// @Accept json
// @Produce json
// @Param request body dto.BatchContentKSUIDsRequest true "批量归档请求"
// @Success 200 {object} globalTypes.GlobalResponse{data=dto.BatchOperationResponse}
// @Failure 400 {object} globalTypes.GlobalResponse
// @Failure 500 {object} globalTypes.GlobalResponse
// @Router /api/v1/management/batch/archive [post]
func (h *BatchHandler) BatchArchive(c *gin.Context) {
	// 1. 链路追踪
	ctx, span := tracing.StartSpanWithComponent(c.Request.Context(), "handler", "BatchArchive")
	defer span.End()
	c.Request = c.Request.WithContext(ctx)

	// 2. 获取trace信息和用户信息
	traceID := tracing.GetTraceID(c)
	spanID := tracing.GetSpanID(c)
	userKSUID := ksuid.GetKSUID(c)

	// 3. 添加span属性
	opentelemetry.AddAttribute(span, "method", c.Request.Method)
	opentelemetry.AddAttribute(span, "path", c.Request.URL.Path)
	opentelemetry.AddAttribute(span, "user_ksuid", userKSUID)

	// 4. 记录请求日志
	log.Info().
		Str("trace_id", traceID).
		Str("span_id", spanID).
		Str("user_ksuid", userKSUID).
		Msg("收到批量归档请求")

	// 5. 参数绑定
	var req dto.BatchContentKSUIDsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error().
			Str("trace_id", traceID).
			Err(err).
			Msg("参数绑定失败")

		opentelemetry.AddError(span, err)
		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code: errors.INVALID_PARAMETER,
			Msg:  "参数格式错误",
		})
		return
	}

	// 6. 添加请求参数到span
	opentelemetry.AddAttribute(span, "content_count", len(req.ContentKSUIDs))

	// 7. 调用服务层
	result, gErr := h.batchService.BatchArchive(ctx, userKSUID, req.ContentKSUIDs)
	if gErr != nil {
		log.Error().
			Str("trace_id", traceID).
			Int("content_count", len(req.ContentKSUIDs)).
			Interface("error", gErr).
			Msg("批量归档失败")

		opentelemetry.AddError(span, gErr)

		// 根据错误类型返回不同的HTTP状态码
		statusCode := http.StatusInternalServerError
		if gErr.Code == errors.INVALID_PARAMETER {
			statusCode = http.StatusBadRequest
		}

		c.JSON(statusCode, globalTypes.GlobalResponse{
			Code: gErr.Code,
			Msg:  gErr.Message,
		})
		return
	}

	// 8. 返回成功响应
	log.Info().
		Str("trace_id", traceID).
		Int("success_count", result.SuccessCount).
		Int("failure_count", result.FailureCount).
		Msg("批量归档完成")

	c.JSON(http.StatusOK, globalTypes.GlobalResponse{
		Code: errors.SUCCESS,
		Data: result,
	})
}

// BatchUpdateCategory 批量更新分类
// @Summary 批量更新分类
// @Description 批量更新多个内容的分类
// @Tags 批量操作
// @Accept json
// @Produce json
// @Param request body dto.BatchUpdateCategoryRequest true "批量更新分类请求"
// @Success 200 {object} globalTypes.GlobalResponse{data=dto.BatchOperationResponse}
// @Failure 400 {object} globalTypes.GlobalResponse
// @Failure 500 {object} globalTypes.GlobalResponse
// @Router /api/v1/management/batch/category [put]
func (h *BatchHandler) BatchUpdateCategory(c *gin.Context) {
	// 1. 链路追踪
	ctx, span := tracing.StartSpanWithComponent(c.Request.Context(), "handler", "BatchUpdateCategory")
	defer span.End()
	c.Request = c.Request.WithContext(ctx)

	// 2. 获取trace信息和用户信息
	traceID := tracing.GetTraceID(c)
	spanID := tracing.GetSpanID(c)
	userKSUID := ksuid.GetKSUID(c)

	// 3. 添加span属性
	opentelemetry.AddAttribute(span, "method", c.Request.Method)
	opentelemetry.AddAttribute(span, "path", c.Request.URL.Path)
	opentelemetry.AddAttribute(span, "user_ksuid", userKSUID)

	// 4. 记录请求日志
	log.Info().
		Str("trace_id", traceID).
		Str("span_id", spanID).
		Str("user_ksuid", userKSUID).
		Msg("收到批量更新分类请求")

	// 5. 参数绑定
	var req dto.BatchUpdateCategoryRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error().
			Str("trace_id", traceID).
			Err(err).
			Msg("参数绑定失败")

		opentelemetry.AddError(span, err)
		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code: errors.INVALID_PARAMETER,
			Msg:  "参数格式错误",
		})
		return
	}

	// 6. 添加请求参数到span
	opentelemetry.AddAttribute(span, "content_count", len(req.ContentKSUIDs))
	opentelemetry.AddAttribute(span, "category_id", req.CategoryID)

	// 7. 调用服务层
	result, gErr := h.batchService.BatchUpdateCategory(ctx, userKSUID, &req)
	if gErr != nil {
		log.Error().
			Str("trace_id", traceID).
			Int("content_count", len(req.ContentKSUIDs)).
			Int("category_id", req.CategoryID).
			Interface("error", gErr).
			Msg("批量更新分类失败")

		opentelemetry.AddError(span, gErr)

		// 根据错误类型返回不同的HTTP状态码
		statusCode := http.StatusInternalServerError
		if gErr.Code == errors.INVALID_PARAMETER {
			statusCode = http.StatusBadRequest
		}

		c.JSON(statusCode, globalTypes.GlobalResponse{
			Code: gErr.Code,
			Msg:  gErr.Message,
		})
		return
	}

	// 8. 返回成功响应
	log.Info().
		Str("trace_id", traceID).
		Int("success_count", result.SuccessCount).
		Int("failure_count", result.FailureCount).
		Int("category_id", req.CategoryID).
		Msg("批量更新分类完成")

	c.JSON(http.StatusOK, globalTypes.GlobalResponse{
		Code: errors.SUCCESS,
		Data: result,
	})
}

// BatchAddTags 批量添加标签
// @Summary 批量添加标签
// @Description 批量为多个内容添加标签
// @Tags 批量操作
// @Accept json
// @Produce json
// @Param request body dto.BatchUpdateTagsRequest true "批量添加标签请求"
// @Success 200 {object} globalTypes.GlobalResponse{data=dto.BatchOperationResponse}
// @Failure 400 {object} globalTypes.GlobalResponse
// @Failure 500 {object} globalTypes.GlobalResponse
// @Router /api/v1/management/batch/tags/add [post]
func (h *BatchHandler) BatchAddTags(c *gin.Context) {
	// 1. 链路追踪
	ctx, span := tracing.StartSpanWithComponent(c.Request.Context(), "handler", "BatchAddTags")
	defer span.End()
	c.Request = c.Request.WithContext(ctx)

	// 2. 获取trace信息和用户信息
	traceID := tracing.GetTraceID(c)
	spanID := tracing.GetSpanID(c)
	userKSUID := ksuid.GetKSUID(c)

	// 3. 添加span属性
	opentelemetry.AddAttribute(span, "method", c.Request.Method)
	opentelemetry.AddAttribute(span, "path", c.Request.URL.Path)
	opentelemetry.AddAttribute(span, "user_ksuid", userKSUID)

	// 4. 记录请求日志
	log.Info().
		Str("trace_id", traceID).
		Str("span_id", spanID).
		Str("user_ksuid", userKSUID).
		Msg("收到批量添加标签请求")

	// 5. 参数绑定
	var req dto.BatchUpdateTagsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error().
			Str("trace_id", traceID).
			Err(err).
			Msg("参数绑定失败")

		opentelemetry.AddError(span, err)
		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code: errors.INVALID_PARAMETER,
			Msg:  "参数格式错误",
		})
		return
	}

	// 6. 添加请求参数到span
	opentelemetry.AddAttribute(span, "content_count", len(req.ContentKSUIDs))
	opentelemetry.AddAttribute(span, "tag_count", len(req.Tags))

	// 7. 调用服务层
	result, gErr := h.batchService.BatchAddTags(ctx, userKSUID, &req)
	if gErr != nil {
		log.Error().
			Str("trace_id", traceID).
			Int("content_count", len(req.ContentKSUIDs)).
			Int("tag_count", len(req.Tags)).
			Interface("error", gErr).
			Msg("批量添加标签失败")

		opentelemetry.AddError(span, gErr)

		// 根据错误类型返回不同的HTTP状态码
		statusCode := http.StatusInternalServerError
		if gErr.Code == errors.INVALID_PARAMETER {
			statusCode = http.StatusBadRequest
		}

		c.JSON(statusCode, globalTypes.GlobalResponse{
			Code: gErr.Code,
			Msg:  gErr.Message,
		})
		return
	}

	// 8. 返回成功响应
	log.Info().
		Str("trace_id", traceID).
		Int("success_count", result.SuccessCount).
		Int("failure_count", result.FailureCount).
		Int("tag_count", len(req.Tags)).
		Msg("批量添加标签完成")

	c.JSON(http.StatusOK, globalTypes.GlobalResponse{
		Code: errors.SUCCESS,
		Data: result,
	})
}

// BatchRemoveTags 批量移除标签
// @Summary 批量移除标签
// @Description 批量为多个内容移除标签
// @Tags 批量操作
// @Accept json
// @Produce json
// @Param request body dto.BatchUpdateTagsRequest true "批量移除标签请求"
// @Success 200 {object} globalTypes.GlobalResponse{data=dto.BatchOperationResponse}
// @Failure 400 {object} globalTypes.GlobalResponse
// @Failure 500 {object} globalTypes.GlobalResponse
// @Router /api/v1/management/batch/tags/remove [post]
func (h *BatchHandler) BatchRemoveTags(c *gin.Context) {
	// 1. 链路追踪
	ctx, span := tracing.StartSpanWithComponent(c.Request.Context(), "handler", "BatchRemoveTags")
	defer span.End()
	c.Request = c.Request.WithContext(ctx)

	// 2. 获取trace信息和用户信息
	traceID := tracing.GetTraceID(c)
	spanID := tracing.GetSpanID(c)
	userKSUID := ksuid.GetKSUID(c)

	// 3. 添加span属性
	opentelemetry.AddAttribute(span, "method", c.Request.Method)
	opentelemetry.AddAttribute(span, "path", c.Request.URL.Path)
	opentelemetry.AddAttribute(span, "user_ksuid", userKSUID)

	// 4. 记录请求日志
	log.Info().
		Str("trace_id", traceID).
		Str("span_id", spanID).
		Str("user_ksuid", userKSUID).
		Msg("收到批量移除标签请求")

	// 5. 参数绑定
	var req dto.BatchUpdateTagsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error().
			Str("trace_id", traceID).
			Err(err).
			Msg("参数绑定失败")

		opentelemetry.AddError(span, err)
		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code: errors.INVALID_PARAMETER,
			Msg:  "参数格式错误",
		})
		return
	}

	// 6. 添加请求参数到span
	opentelemetry.AddAttribute(span, "content_count", len(req.ContentKSUIDs))
	opentelemetry.AddAttribute(span, "tag_count", len(req.Tags))

	// 7. 调用服务层
	result, gErr := h.batchService.BatchRemoveTags(ctx, userKSUID, &req)
	if gErr != nil {
		log.Error().
			Str("trace_id", traceID).
			Int("content_count", len(req.ContentKSUIDs)).
			Int("tag_count", len(req.Tags)).
			Interface("error", gErr).
			Msg("批量移除标签失败")

		opentelemetry.AddError(span, gErr)

		// 根据错误类型返回不同的HTTP状态码
		statusCode := http.StatusInternalServerError
		if gErr.Code == errors.INVALID_PARAMETER {
			statusCode = http.StatusBadRequest
		}

		c.JSON(statusCode, globalTypes.GlobalResponse{
			Code: gErr.Code,
			Msg:  gErr.Message,
		})
		return
	}

	// 8. 返回成功响应
	log.Info().
		Str("trace_id", traceID).
		Int("success_count", result.SuccessCount).
		Int("failure_count", result.FailureCount).
		Int("tag_count", len(req.Tags)).
		Msg("批量移除标签完成")

	c.JSON(http.StatusOK, globalTypes.GlobalResponse{
		Code: errors.SUCCESS,
		Data: result,
	})
}

// GetBatchOperationStatus 获取批量操作状态
// @Summary 获取批量操作状态
// @Description 获取指定批量操作的执行状态
// @Tags 批量操作
// @Accept json
// @Produce json
// @Param operation_id path string true "操作ID"
// @Success 200 {object} globalTypes.GlobalResponse{data=dto.BatchOperationStatusResponse}
// @Failure 400 {object} globalTypes.GlobalResponse
// @Failure 404 {object} globalTypes.GlobalResponse
// @Failure 500 {object} globalTypes.GlobalResponse
// @Router /api/v1/management/batch/operations/{operation_id}/status [get]
func (h *BatchHandler) GetBatchOperationStatus(c *gin.Context) {
	// 1. 链路追踪
	ctx, span := tracing.StartSpanWithComponent(c.Request.Context(), "handler", "GetBatchOperationStatus")
	defer span.End()
	c.Request = c.Request.WithContext(ctx)

	// 2. 获取trace信息
	traceID := tracing.GetTraceID(c)
	spanID := tracing.GetSpanID(c)

	// 3. 获取路径参数
	operationID := c.Param("operation_id")
	if operationID == "" {
		log.Error().
			Str("trace_id", traceID).
			Msg("操作ID参数缺失")

		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code: errors.INVALID_PARAMETER,
			Msg:  "操作ID不能为空",
		})
		return
	}

	// 4. 添加span属性
	opentelemetry.AddAttribute(span, "method", c.Request.Method)
	opentelemetry.AddAttribute(span, "path", c.Request.URL.Path)
	opentelemetry.AddAttribute(span, "operation_id", operationID)

	// 5. 记录请求日志
	log.Info().
		Str("trace_id", traceID).
		Str("span_id", spanID).
		Str("operation_id", operationID).
		Msg("收到获取批量操作状态请求")

	// 6. 调用服务层
	result, gErr := h.batchService.GetBatchOperationStatus(ctx, operationID)
	if gErr != nil {
		log.Error().
			Str("trace_id", traceID).
			Str("operation_id", operationID).
			Interface("error", gErr).
			Msg("获取批量操作状态失败")

		opentelemetry.AddError(span, gErr)

		// 根据错误类型返回不同的HTTP状态码
		statusCode := http.StatusInternalServerError
		if gErr.Code == errors.NOT_FOUND {
			statusCode = http.StatusNotFound
		} else if gErr.Code == errors.INVALID_PARAMETER {
			statusCode = http.StatusBadRequest
		}

		c.JSON(statusCode, globalTypes.GlobalResponse{
			Code: gErr.Code,
			Msg:  gErr.Message,
		})
		return
	}

	// 7. 返回成功响应
	log.Info().
		Str("trace_id", traceID).
		Str("operation_id", operationID).
		Str("status", result.Status).
		Msg("获取批量操作状态成功")

	c.JSON(http.StatusOK, globalTypes.GlobalResponse{
		Code: errors.SUCCESS,
		Data: result,
	})
}

// HealthCheck 健康检查
// @Summary 健康检查
// @Description 检查批量操作服务的健康状态
// @Tags 系统管理
// @Accept json
// @Produce json
// @Success 200 {object} globalTypes.GlobalResponse
// @Failure 500 {object} globalTypes.GlobalResponse
// @Router /api/v1/management/batch/health [get]
func (h *BatchHandler) HealthCheck(c *gin.Context) {
	// 1. 链路追踪
	ctx, span := tracing.StartSpanWithComponent(c.Request.Context(), "handler", "BatchHealthCheck")
	defer span.End()
	c.Request = c.Request.WithContext(ctx)

	// 2. 获取trace信息
	traceID := tracing.GetTraceID(c)

	// 3. 添加span属性
	opentelemetry.AddAttribute(span, "method", c.Request.Method)
	opentelemetry.AddAttribute(span, "path", c.Request.URL.Path)

	// 4. 记录请求日志
	log.Debug().
		Str("trace_id", traceID).
		Msg("收到批量操作服务健康检查请求")

	// 5. 调用服务层
	gErr := h.batchService.HealthCheck(ctx)
	if gErr != nil {
		log.Error().
			Str("trace_id", traceID).
			Interface("error", gErr).
			Msg("批量操作服务健康检查失败")

		opentelemetry.AddError(span, gErr)
		c.JSON(http.StatusInternalServerError, globalTypes.GlobalResponse{
			Code: gErr.Code,
			Msg:  gErr.Message,
		})
		return
	}

	// 6. 返回成功响应
	log.Debug().
		Str("trace_id", traceID).
		Msg("批量操作服务健康检查成功")

	c.JSON(http.StatusOK, globalTypes.GlobalResponse{
		Code: errors.SUCCESS,
		Msg:  "批量操作服务健康",
	})
}
