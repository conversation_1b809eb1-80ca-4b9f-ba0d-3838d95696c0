package dto

import (
	"pxpat-backend/internal/user-cluster/complaint-service/model"
	"pxpat-backend/pkg/errors"
	"time"
)

// CreateIdentityVerificationRequest 创建身份认证请求
type CreateIdentityVerificationRequest struct {
	Type model.IdentityType `json:"type" binding:"required"` // 身份类型

	// 自然人信息
	RealName       string `json:"real_name"`       // 真实姓名
	PhoneNumber    string `json:"phone_number"`    // 手机号
	VerificationCode string `json:"verification_code"` // 验证码
	Email          string `json:"email"`           // 邮箱
	IDCard         string `json:"id_card"`         // 身份证号
	Address        string `json:"address"`         // 联系地址
	PostalCode     string `json:"postal_code"`     // 邮政编码
	FaxNumber      string `json:"fax_number"`      // 传真号
	LandlineNumber string `json:"landline_number"` // 座机号

	// 法人/非法人组织信息
	OrganizationName     string `json:"organization_name"`     // 企业/机构/单位/团体全称
	CertificateNumber    string `json:"certificate_number"`    // 证件编号
	CertificateStartDate string `json:"certificate_start_date"` // 证件有效期开始时间(YYYY-MM-DD)
	CertificateEndDate   string `json:"certificate_end_date"`   // 证件有效期结束时间(YYYY-MM-DD)
	ContactName          string `json:"contact_name"`          // 联系人姓名
	ContactIDCard        string `json:"contact_id_card"`       // 联系人身份证号
	ContactEmail         string `json:"contact_email"`         // 联系邮箱
	ContactPhone         string `json:"contact_phone"`         // 联系人手机号
	ContactAddress       string `json:"contact_address"`       // 联系地址
	ContactPostalCode    string `json:"contact_postal_code"`   // 邮政编码
	ContactFax           string `json:"contact_fax"`           // 传真号
	ContactLandline      string `json:"contact_landline"`      // 座机号
}

// UpdateIdentityVerificationRequest 更新身份认证请求
type UpdateIdentityVerificationRequest struct {
	// 自然人信息
	RealName       string `json:"real_name"`       // 真实姓名
	PhoneNumber    string `json:"phone_number"`    // 手机号
	Email          string `json:"email"`           // 邮箱
	IDCard         string `json:"id_card"`         // 身份证号
	Address        string `json:"address"`         // 联系地址
	PostalCode     string `json:"postal_code"`     // 邮政编码
	FaxNumber      string `json:"fax_number"`      // 传真号
	LandlineNumber string `json:"landline_number"` // 座机号

	// 法人/非法人组织信息
	OrganizationName     string `json:"organization_name"`     // 企业/机构/单位/团体全称
	CertificateNumber    string `json:"certificate_number"`    // 证件编号
	CertificateStartDate string `json:"certificate_start_date"` // 证件有效期开始时间
	CertificateEndDate   string `json:"certificate_end_date"`   // 证件有效期结束时间
	ContactName          string `json:"contact_name"`          // 联系人姓名
	ContactIDCard        string `json:"contact_id_card"`       // 联系人身份证号
	ContactEmail         string `json:"contact_email"`         // 联系邮箱
	ContactPhone         string `json:"contact_phone"`         // 联系人手机号
	ContactAddress       string `json:"contact_address"`       // 联系地址
	ContactPostalCode    string `json:"contact_postal_code"`   // 邮政编码
	ContactFax           string `json:"contact_fax"`           // 传真号
	ContactLandline      string `json:"contact_landline"`      // 座机号
}

// ReviewIdentityVerificationRequest 审核身份认证请求
type ReviewIdentityVerificationRequest struct {
	Status       model.IdentityStatus `json:"status" binding:"required"` // 审核状态
	ReviewNote   string               `json:"review_note"`               // 审核备注
	RejectReason string               `json:"reject_reason"`             // 拒绝原因
}

// GetIdentityVerificationsRequest 获取身份认证列表请求
type GetIdentityVerificationsRequest struct {
	Page      int                  `form:"page" binding:"min=1"`             // 页码
	PageSize  int                  `form:"page_size" binding:"min=1,max=50"` // 每页数量
	Type      model.IdentityType   `form:"type"`                             // 身份类型过滤
	Status    model.IdentityStatus `form:"status"`                           // 状态过滤
	StartDate string               `form:"start_date"`                       // 开始日期
	EndDate   string               `form:"end_date"`                         // 结束日期
}

// IdentityVerificationResponse 身份认证响应
type IdentityVerificationResponse struct {
	VerificationKSUID string                 `json:"verification_ksuid"` // 认证KSUID
	UserKSUID         string                 `json:"user_ksuid"`         // 用户KSUID
	Type              model.IdentityType     `json:"type"`               // 身份类型
	Status            model.IdentityStatus   `json:"status"`             // 认证状态

	// 自然人信息
	RealName       string `json:"real_name"`       // 真实姓名
	PhoneNumber    string `json:"phone_number"`    // 手机号
	Email          string `json:"email"`           // 邮箱
	IDCard         string `json:"id_card"`         // 身份证号(脱敏)
	Address        string `json:"address"`         // 联系地址
	PostalCode     string `json:"postal_code"`     // 邮政编码
	FaxNumber      string `json:"fax_number"`      // 传真号
	LandlineNumber string `json:"landline_number"` // 座机号

	// 法人/非法人组织信息
	OrganizationName   string     `json:"organization_name"`   // 企业/机构/单位/团体全称
	CertificateNumber  string     `json:"certificate_number"`  // 证件编号
	CertificateStartAt *time.Time `json:"certificate_start_at"` // 证件有效期开始时间
	CertificateEndAt   *time.Time `json:"certificate_end_at"`   // 证件有效期结束时间
	ContactName        string     `json:"contact_name"`        // 联系人姓名
	ContactIDCard      string     `json:"contact_id_card"`     // 联系人身份证号(脱敏)
	ContactEmail       string     `json:"contact_email"`       // 联系邮箱
	ContactPhone       string     `json:"contact_phone"`       // 联系人手机号
	ContactAddress     string     `json:"contact_address"`     // 联系地址
	ContactPostalCode  string     `json:"contact_postal_code"` // 邮政编码
	ContactFax         string     `json:"contact_fax"`         // 传真号
	ContactLandline    string     `json:"contact_landline"`    // 座机号

	// 审核信息
	ReviewerKSUID string     `json:"reviewer_ksuid"` // 审核人KSUID
	ReviewedAt    *time.Time `json:"reviewed_at"`    // 审核时间
	ReviewNote    string     `json:"review_note"`    // 审核备注
	RejectReason  string     `json:"reject_reason"`  // 拒绝原因

	CreatedAt time.Time `json:"created_at"` // 创建时间
	UpdatedAt time.Time `json:"updated_at"` // 更新时间
}



// CountryResponse 国家地区响应
type CountryResponse struct {
	Code        string `json:"code"`         // 国家代码
	Name        string `json:"name"`         // 国家名称
	NameEn      string `json:"name_en"`      // 英文名称
	NameLocal   string `json:"name_local"`   // 本地名称
	Continent   string `json:"continent"`    // 所属大洲
	Region      string `json:"region"`       // 地区
	SubRegion   string `json:"sub_region"`   // 子地区
	Flag        string `json:"flag"`         // 国旗emoji
	PhoneCode   string `json:"phone_code"`   // 电话区号
	Currency    string `json:"currency"`     // 货币代码
	TimeZone    string `json:"time_zone"`    // 时区
	SortOrder   int    `json:"sort_order"`   // 排序
}

// TrademarkCategoryResponse 商标类型响应
type TrademarkCategoryResponse struct {
	CategoryNumber int    `json:"category_number"` // 类别号
	Name           string `json:"name"`            // 类别名称
	Description    string `json:"description"`     // 类别描述
	SortOrder      int    `json:"sort_order"`      // 排序
}

// 验证函数
func ValidateCreateIdentityVerificationRequest(req *CreateIdentityVerificationRequest) *errors.Errors {
	if req.Type == "" {
		return errors.NewValidationError("身份类型不能为空")
	}

	switch req.Type {
	case model.IdentityTypeNatural:
		if req.RealName == "" {
			return errors.NewValidationError("真实姓名不能为空")
		}
		if req.PhoneNumber == "" {
			return errors.NewValidationError("手机号不能为空")
		}
		if req.Email == "" {
			return errors.NewValidationError("邮箱不能为空")
		}
		if req.IDCard == "" {
			return errors.NewValidationError("身份证号不能为空")
		}
	case model.IdentityTypeLegalEntity:
		if req.OrganizationName == "" {
			return errors.NewValidationError("企业/机构名称不能为空")
		}
		if req.CertificateNumber == "" {
			return errors.NewValidationError("证件编号不能为空")
		}
		if req.ContactName == "" {
			return errors.NewValidationError("联系人姓名不能为空")
		}
		if req.ContactIDCard == "" {
			return errors.NewValidationError("联系人身份证号不能为空")
		}
		if req.ContactEmail == "" {
			return errors.NewValidationError("联系邮箱不能为空")
		}
		if req.ContactPhone == "" {
			return errors.NewValidationError("联系人手机号不能为空")
		}
	}

	return nil
}

// 转换函数
func IdentityVerificationToResponse(verification *model.IdentityVerification) *IdentityVerificationResponse {
	return &IdentityVerificationResponse{
		VerificationKSUID:  verification.VerificationKSUID,
		UserKSUID:          verification.UserKSUID,
		Type:               verification.Type,
		Status:             verification.Status,
		RealName:           verification.RealName,
		PhoneNumber:        verification.PhoneNumber,
		Email:              verification.Email,
		IDCard:             maskIDCard(verification.IDCard),
		Address:            verification.Address,
		PostalCode:         verification.PostalCode,
		FaxNumber:          verification.FaxNumber,
		LandlineNumber:     verification.LandlineNumber,
		OrganizationName:   verification.OrganizationName,
		CertificateNumber:  verification.CertificateNumber,
		CertificateStartAt: verification.CertificateStartAt,
		CertificateEndAt:   verification.CertificateEndAt,
		ContactName:        verification.ContactName,
		ContactIDCard:      maskIDCard(verification.ContactIDCard),
		ContactEmail:       verification.ContactEmail,
		ContactPhone:       verification.ContactPhone,
		ContactAddress:     verification.ContactAddress,
		ContactPostalCode:  verification.ContactPostalCode,
		ContactFax:         verification.ContactFax,
		ContactLandline:    verification.ContactLandline,
		ReviewerKSUID:      verification.ReviewerKSUID,
		ReviewedAt:         verification.ReviewedAt,
		ReviewNote:         verification.ReviewNote,
		RejectReason:       verification.RejectReason,
		CreatedAt:          verification.CreatedAt,
		UpdatedAt:          verification.UpdatedAt,
	}
}

// maskIDCard 身份证号脱敏处理
func maskIDCard(idCard string) string {
	if len(idCard) < 8 {
		return idCard
	}
	return idCard[:4] + "****" + idCard[len(idCard)-4:]
}

func CountryToResponse(country *model.Country) *CountryResponse {
	return &CountryResponse{
		Code:        country.Code,
		Name:        country.Name,
		NameEn:      country.NameEn,
		NameLocal:   country.NameLocal,
		Continent:   country.Continent,
		Region:      country.Region,
		SubRegion:   country.SubRegion,
		Flag:        country.Flag,
		PhoneCode:   country.PhoneCode,
		Currency:    country.Currency,
		TimeZone:    country.TimeZone,
		SortOrder:   country.SortOrder,
	}
}

func TrademarkCategoryToResponse(category *model.TrademarkCategory) *TrademarkCategoryResponse {
	return &TrademarkCategoryResponse{
		CategoryNumber: category.CategoryNumber,
		Name:           category.Name,
		Description:    category.Description,
		SortOrder:      category.SortOrder,
	}
}

// ReviewIdentityRequest 审核身份认证请求
type ReviewIdentityRequest struct {
	Status      model.IdentityStatus `json:"status" binding:"required" example:"approved"`     // 审核状态
	ReviewNote  string               `json:"review_note" binding:"required" example:"审核通过"` // 审核说明
	RejectReason string              `json:"reject_reason,omitempty" example:"材料不清晰"`       // 拒绝原因
}

// IdentityFilters 身份认证过滤条件
type IdentityFilters struct {
	Page         int                  `json:"page" example:"1"`
	PageSize     int                  `json:"page_size" example:"20"`
	Status       model.IdentityStatus `json:"status,omitempty" example:"pending"`
	Type         model.IdentityType   `json:"type,omitempty" example:"individual"`
	UserKSUID    string               `json:"user_ksuid,omitempty" example:"user123"`
	ReviewerKSUID string              `json:"reviewer_ksuid,omitempty" example:"admin123"`
	StartDate    string               `json:"start_date,omitempty" example:"2024-01-01"`
	EndDate      string               `json:"end_date,omitempty" example:"2024-12-31"`
	SortBy       string               `json:"sort_by,omitempty" example:"created_at"`
	SortOrder    string               `json:"sort_order,omitempty" example:"desc"`
}

// IdentityVerificationListResponse 身份认证列表响应
type IdentityVerificationListResponse struct {
	List       []IdentityVerificationResponse `json:"list"`        // 身份认证列表
	Total      int64                          `json:"total"`       // 总数
	Page       int                            `json:"page"`        // 当前页
	PageSize   int                            `json:"page_size"`   // 每页数量
	TotalPages int                            `json:"total_pages"` // 总页数
}
