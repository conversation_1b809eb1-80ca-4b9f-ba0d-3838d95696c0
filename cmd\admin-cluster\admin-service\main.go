package main

import (
	"fmt"
	"os"
	"os/signal"
	handler2 "pxpat-backend/internal/admin-cluster/admin-service/handler"
	"pxpat-backend/internal/admin-cluster/admin-service/model"
	"pxpat-backend/internal/admin-cluster/admin-service/repository"
	"pxpat-backend/internal/admin-cluster/admin-service/service"
	"pxpat-backend/internal/admin-cluster/admin-service/types"
	"pxpat-backend/pkg/auth"
	configLoader "pxpat-backend/pkg/config"
	DBLoader "pxpat-backend/pkg/database"
	pkgauth "pxpat-backend/pkg/middleware/auth"
	pkgcors "pxpat-backend/pkg/middleware/cors"
	"syscall"

	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog"
	"github.com/rs/zerolog/log"
	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"
)

func main() {
	// 配置日志
	zerolog.TimeFieldFormat = zerolog.TimeFormatUnix
	log.Logger = log.Output(zerolog.ConsoleWriter{Out: os.Stderr})

	clusterName := "admin"
	serviceName := "admin"

	// 加载配置
	cfg := configLoader.Load[types.Config](configLoader.LoaderConfig{
		TypeName:    configLoader.Service,
		ClusterName: clusterName,
		ServiceName: serviceName,
		UseRedis:    false,
	})

	// 连接数据库
	connDB, err := DBLoader.ConnectDB(cfg.Database)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to connect to database")
	}

	// 注册数据库模型
	err = connDB.AutoMigrate(
		// 管理员相关模型
		&model.Admin{},
	)
	if err != nil {
		log.Error().Err(err).Msg("Failed to auto migrate database")
		log.Fatal().Err(err).Msg("Database migration failed")
	}

	// 初始化默认管理员账号
	initDefaultAdmin(connDB)

	// 初始化JWT管理器
	jwtManager := auth.NewJWTManager(cfg.JWT.SecretKey, cfg.JWT.Expiration, "pxpat-admin")

	// 初始化仓库
	adminRepo := repository.NewAdminRepository(connDB)

	// 初始化服务
	adminService := service.NewAdminService(adminRepo, jwtManager, cfg)

	// 创建HTTP路由器
	router := gin.Default()
	// 使用新的CORS中间件
	router.Use(pkgcors.CORSMiddleware(cfg.Security.CORS))

	// 创建处理器
	adminHandler := handler2.NewAdminHandler(adminService)
	healthHandler := handler2.NewHealthHandler()

	// 注册路由
	// 公共路由（无需认证）
	authGroup := router.Group("/api/admin/auth")
	{
		authGroup.POST("/login", adminHandler.Login)
	}

	// 健康检查路由
	router.GET("/health", healthHandler.Check)
	router.GET("/api/health", healthHandler.Check)

	// 需要认证的路由
	adminGroup := router.Group("/api/admin")
	// 使用新的管理员认证中间件
	adminGroup.Use(pkgauth.AdminAuthMiddleware(jwtManager))
	{
		// 认证路由
		adminGroup.POST("/auth/logout", adminHandler.Logout)

		// 管理员信息路由
		adminGroup.GET("/me", adminHandler.GetProfile)
		adminGroup.PUT("/password", adminHandler.ChangePassword)

		// 用户管理路由可以在未来添加
	}

	// 启动HTTP服务器
	httpAddr := fmt.Sprintf(":%d", cfg.Server.Port)
	log.Info().Msgf("Starting HTTP server on %s", httpAddr)

	serverErr := make(chan error, 1)
	go func() {
		if err := router.Run(httpAddr); err != nil {
			serverErr <- err
		}
	}()

	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)

	select {
	case <-quit:
		log.Info().Msg("Shutting down server...")
	case err := <-serverErr:
		log.Fatal().Err(err).Msg("Failed to start HTTP server")
	}

	// 注意：在实际的生产环境中，这里应该添加优雅关闭的逻辑
	// 例如使用 context.WithTimeout 来设置超时，然后调用 server.Shutdown(ctx)
	// 简化版本中暂时省略这部分代码

	log.Info().Msg("Server exiting")
}

// 初始化默认管理员账号
func initDefaultAdmin(connDB *gorm.DB) {
	// 检查是否已存在管理员账号
	var count int64
	connDB.Model(&model.Admin{}).Count(&count)
	if count > 0 {
		log.Info().Msg("Admin account already exists, skipping initialization")
		return
	}

	// 设置默认管理员信息
	username := "admin"
	email := "<EMAIL>"
	password := "admin123" // 仅用于开发环境，生产环境应使用更强密码
	role := "admin"

	// 生成密码哈希
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		log.Error().Err(err).Msg("Failed to hash password for default admin")
		return
	}

	// 创建管理员账号
	admin := model.Admin{
		Username:     username,
		Email:        email,
		PasswordHash: string(hashedPassword),
		Role:         role,
		Status:       "active",
	}

	if err := connDB.Create(&admin).Error; err != nil {
		log.Error().Err(err).Msg("Failed to create default admin account")
		return
	}

	log.Info().
		Str("username", username).
		Str("email", email).
		Str("role", role).
		Msg("Default admin account created successfully")
}
