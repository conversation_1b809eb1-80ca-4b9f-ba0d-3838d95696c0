package service

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/redis/go-redis/v9"
	"github.com/rs/zerolog/log"

	"pxpat-backend/internal/content-cluster/content-management-service/client"
	"pxpat-backend/internal/content-cluster/content-management-service/repository"
	"pxpat-backend/internal/content-cluster/content-management-service/types"
	"pxpat-backend/internal/content-cluster/content-management-service/utils"
	"pxpat-backend/pkg/errors"
)

// ContentManagementService 内容管理服务接口
type ContentManagementService interface {
	// 基础内容查询
	GetBaseContents(ctx context.Context, filters *types.ContentFilters) (*types.BaseContentList, *errors.Errors)
	GetContentWithDetails(ctx context.Context, contentKSUID string) (*types.ContentWithDetails, *errors.Errors)
	
	// 用户内容管理
	GetUserAllContents(ctx context.Context, userKSUID string, filters *types.ContentFilters) (*types.BaseContentList, *errors.Errors)
	GetUserContentsByType(ctx context.Context, userKSUID string, contentType string, filters *types.ContentFilters) (*types.ContentWithDetailsList, *errors.Errors)
	
	// 内容操作
	UpdateContentStatus(ctx context.Context, operatorKSUID string, contentKSUID string, status string) *errors.Errors
	DeleteContent(ctx context.Context, operatorKSUID string, contentKSUID string) *errors.Errors
	
	// 批量操作
	BatchUpdateContentStatus(ctx context.Context, operatorKSUID string, contentKSUIDs []string, status string) *errors.Errors
	BatchDeleteContents(ctx context.Context, operatorKSUID string, contentKSUIDs []string) *errors.Errors
	
	// 按类型查询（返回详细信息）
	GetVideoContents(ctx context.Context, filters *types.ContentFilters) (*types.ContentWithDetailsList, *errors.Errors)
	GetNovelContents(ctx context.Context, filters *types.ContentFilters) (*types.ContentWithDetailsList, *errors.Errors)
	GetMusicContents(ctx context.Context, filters *types.ContentFilters) (*types.ContentWithDetailsList, *errors.Errors)
	
	// 健康检查
	HealthCheck(ctx context.Context) *errors.Errors
}

// contentManagementService 内容管理服务实现
type contentManagementService struct {
	// 服务客户端
	videoClient       client.VideoServiceClient
	novelClient       client.NovelServiceClient       // 预留
	musicClient       client.MusicServiceClient       // 预留
	interactionClient client.InteractionServiceClient
	
	// 数据访问层
	cacheRepo     repository.ContentCacheRepository
	operationRepo repository.OperationLogRepository
	configRepo    repository.ConfigRepository
	
	// 工具组件
	converter  utils.ContentConverter
	aggregator utils.DataAggregator
	validator  utils.ContentValidator
	
	// 基础设施
	redis  *redis.Client
	logger *log.Logger
}

// NewContentManagementService 创建内容管理服务实例
func NewContentManagementService(
	videoClient client.VideoServiceClient,
	interactionClient client.InteractionServiceClient,
	cacheRepo repository.ContentCacheRepository,
	operationRepo repository.OperationLogRepository,
	configRepo repository.ConfigRepository,
	converter utils.ContentConverter,
	aggregator utils.DataAggregator,
	validator utils.ContentValidator,
	redis *redis.Client,
) ContentManagementService {
	return &contentManagementService{
		videoClient:       videoClient,
		interactionClient: interactionClient,
		cacheRepo:         cacheRepo,
		operationRepo:     operationRepo,
		configRepo:        configRepo,
		converter:         converter,
		aggregator:        aggregator,
		validator:         validator,
		redis:            redis,
		logger:           &log.Logger,
	}
}

// GetBaseContents 获取基础内容列表（包含交互统计）
func (s *contentManagementService) GetBaseContents(ctx context.Context, filters *types.ContentFilters) (*types.BaseContentList, *errors.Errors) {
	log.Info().
		Interface("filters", filters).
		Msg("开始获取基础内容列表")

	// 参数验证
	if gErr := s.validator.ValidateContentFilters(filters); gErr != nil {
		log.Error().
			Interface("filters", filters).
			Interface("error", gErr).
			Msg("内容过滤器验证失败")
		return nil, gErr
	}

	// 设置默认值
	if filters.Page <= 0 {
		filters.Page = 1
	}
	if filters.Limit <= 0 {
		filters.Limit = 20
	}
	if filters.Limit > 100 {
		filters.Limit = 100 // 限制最大页面大小
	}

	// 尝试从缓存获取
	cacheKey := s.generateCacheKey("base_contents", filters)
	var cachedResult types.BaseContentList
	if err := s.redis.Get(ctx, cacheKey).Scan(&cachedResult); err == nil {
		log.Debug().
			Str("cache_key", cacheKey).
			Msg("从缓存获取基础内容列表成功")
		return &cachedResult, nil
	}

	// 缓存未命中，从各服务获取数据
	var allContents []*types.BaseContent
	var wg sync.WaitGroup
	var mu sync.Mutex
	var serviceErrors []error

	// 并发获取各服务的内容
	if s.shouldIncludeContentType(filters.ContentTypes, "video") {
		wg.Add(1)
		go func() {
			defer wg.Done()
			videoContents, err := s.videoClient.GetContentsByFilters(filters)
			if err != nil {
				log.Error().Err(err).Msg("获取视频内容失败")
				mu.Lock()
				serviceErrors = append(serviceErrors, fmt.Errorf("video service error: %w", err))
				mu.Unlock()
				return
			}

			// 转换为基础内容
			baseContents := s.converter.BatchConvertVideosToBase(videoContents.Contents)
			
			mu.Lock()
			allContents = append(allContents, baseContents...)
			mu.Unlock()
		}()
	}

	// 如果启用了小说服务
	if s.novelClient != nil && s.shouldIncludeContentType(filters.ContentTypes, "novel") {
		wg.Add(1)
		go func() {
			defer wg.Done()
			// 这里预留小说服务的调用
			log.Debug().Msg("小说服务暂未实现")
		}()
	}

	// 如果启用了音乐服务
	if s.musicClient != nil && s.shouldIncludeContentType(filters.ContentTypes, "music") {
		wg.Add(1)
		go func() {
			defer wg.Done()
			// 这里预留音乐服务的调用
			log.Debug().Msg("音乐服务暂未实现")
		}()
	}

	// 等待所有请求完成
	wg.Wait()

	// 检查是否有服务错误
	if len(serviceErrors) > 0 {
		log.Error().
			Interface("errors", serviceErrors).
			Msg("获取内容时发生服务错误")
		// 如果所有服务都失败，返回错误
		if len(allContents) == 0 {
			return nil, errors.NewInternalError("所有内容服务都不可用")
		}
		// 否则继续处理，但记录警告
		log.Warn().
			Interface("errors", serviceErrors).
			Int("content_count", len(allContents)).
			Msg("部分服务失败，但仍有内容可返回")
	}

	// 批量获取交互统计数据
	if len(allContents) > 0 {
		s.enrichWithInteractionStats(ctx, allContents)
	}

	// 排序和分页
	sortedContents := s.sortBaseContents(allContents, filters.SortBy, filters.SortOrder)
	paginatedContents, totalPages := s.paginateBaseContents(sortedContents, filters.Page, filters.Limit)

	result := &types.BaseContentList{
		Contents:   paginatedContents,
		Total:      len(allContents),
		Page:       filters.Page,
		Limit:      filters.Limit,
		TotalPages: totalPages,
	}

	// 缓存结果（5分钟）
	s.redis.Set(ctx, cacheKey, result, 5*time.Minute)

	log.Info().
		Int("total_contents", len(allContents)).
		Int("page_contents", len(paginatedContents)).
		Int("page", filters.Page).
		Int("total_pages", totalPages).
		Msg("获取基础内容列表成功")

	return result, nil
}

// enrichWithInteractionStats 批量获取交互统计并填充到内容中
func (s *contentManagementService) enrichWithInteractionStats(ctx context.Context, contents []*types.BaseContent) {
	if len(contents) == 0 {
		return
	}

	log.Debug().
		Int("content_count", len(contents)).
		Msg("开始批量获取交互统计")

	// 提取所有内容KSUID
	contentKSUIDs := make([]string, len(contents))
	for i, content := range contents {
		contentKSUIDs[i] = content.ContentKSUID
	}

	// 批量获取交互统计
	interactionStats, err := s.interactionClient.BatchGetContentInteractionStats(contentKSUIDs)
	if err != nil {
		log.Error().
			Err(err).
			Int("content_count", len(contentKSUIDs)).
			Msg("批量获取交互统计失败")
		return // 不影响主流程，只是统计数据为0
	}

	// 填充统计数据
	enrichedCount := 0
	for _, content := range contents {
		if stats, exists := interactionStats[content.ContentKSUID]; exists {
			content.LikeCount = stats.LikeCount
			content.FavoriteCount = stats.FavoriteCount
			enrichedCount++
		}
	}

	log.Debug().
		Int("total_contents", len(contents)).
		Int("enriched_count", enrichedCount).
		Msg("交互统计填充完成")
}

// shouldIncludeContentType 检查是否应该包含指定的内容类型
func (s *contentManagementService) shouldIncludeContentType(contentTypes []string, targetType string) bool {
	if len(contentTypes) == 0 {
		return true // 如果没有指定类型，包含所有类型
	}

	for _, contentType := range contentTypes {
		if contentType == targetType {
			return true
		}
	}
	return false
}

// generateCacheKey 生成缓存键
func (s *contentManagementService) generateCacheKey(prefix string, filters *types.ContentFilters) string {
	// 简化的缓存键生成，实际项目中可能需要更复杂的逻辑
	return fmt.Sprintf("cms:%s:%d:%d:%s:%s",
		prefix,
		filters.Page,
		filters.Limit,
		filters.Status,
		fmt.Sprintf("%v", filters.ContentTypes))
}

// sortBaseContents 对基础内容进行排序
func (s *contentManagementService) sortBaseContents(contents []*types.BaseContent, sortBy, sortOrder string) []*types.BaseContent {
	if len(contents) == 0 {
		return contents
	}

	// 设置默认排序
	if sortBy == "" {
		sortBy = "created_at"
	}
	if sortOrder == "" {
		sortOrder = "desc"
	}

	// 这里可以实现具体的排序逻辑
	// 为了简化，暂时返回原始顺序
	log.Debug().
		Str("sort_by", sortBy).
		Str("sort_order", sortOrder).
		Int("content_count", len(contents)).
		Msg("内容排序完成")

	return contents
}

// paginateBaseContents 对基础内容进行分页
func (s *contentManagementService) paginateBaseContents(contents []*types.BaseContent, page, limit int) ([]*types.BaseContent, int) {
	total := len(contents)
	if total == 0 {
		return contents, 0
	}

	totalPages := (total + limit - 1) / limit

	start := (page - 1) * limit
	if start >= total {
		return []*types.BaseContent{}, totalPages
	}

	end := start + limit
	if end > total {
		end = total
	}

	return contents[start:end], totalPages
}

// GetContentWithDetails 获取带详细信息的内容（包含交互统计）
func (s *contentManagementService) GetContentWithDetails(ctx context.Context, contentKSUID string) (*types.ContentWithDetails, *errors.Errors) {
	log.Info().
		Str("content_ksuid", contentKSUID).
		Msg("开始获取内容详情")

	// 参数验证
	if contentKSUID == "" {
		return nil, errors.NewValidationError("内容KSUID不能为空")
	}

	// 先从缓存获取内容类型
	contentType, err := s.getContentType(ctx, contentKSUID)
	if err != nil {
		log.Error().
			Err(err).
			Str("content_ksuid", contentKSUID).
			Msg("获取内容类型失败")
		return nil, errors.NewInternalError("获取内容类型失败")
	}

	var result *types.ContentWithDetails

	switch contentType {
	case "video":
		videoContent, err := s.videoClient.GetContentByKSUID(contentKSUID)
		if err != nil {
			log.Error().
				Err(err).
				Str("content_ksuid", contentKSUID).
				Msg("获取视频内容失败")
			return nil, errors.NewNotFoundError("视频内容不存在")
		}
		result = s.converter.ConvertVideoToDetails(videoContent)

	case "novel":
		if s.novelClient == nil {
			return nil, errors.NewServiceUnavailableError("小说服务不可用")
		}
		// 预留小说服务调用
		return nil, errors.NewNotImplementedError("小说服务暂未实现")

	case "music":
		if s.musicClient == nil {
			return nil, errors.NewServiceUnavailableError("音乐服务不可用")
		}
		// 预留音乐服务调用
		return nil, errors.NewNotImplementedError("音乐服务暂未实现")

	default:
		return nil, errors.NewValidationError("不支持的内容类型")
	}

	// 获取交互统计数据
	interactionStats, err := s.interactionClient.GetContentInteractionStats(contentKSUID)
	if err != nil {
		log.Error().
			Err(err).
			Str("content_ksuid", contentKSUID).
			Msg("获取交互统计失败")
		// 不影响主流程，继续返回内容，只是统计数据为0
	} else {
		// 更新交互统计
		result.BaseContent.LikeCount = interactionStats.LikeCount
		result.BaseContent.FavoriteCount = interactionStats.FavoriteCount
	}

	log.Info().
		Str("content_ksuid", contentKSUID).
		Str("content_type", contentType).
		Msg("获取内容详情成功")

	return result, nil
}

// getContentType 获取内容类型（通过KSUID前缀或缓存）
func (s *contentManagementService) getContentType(ctx context.Context, contentKSUID string) (string, error) {
	// 方法1: 从缓存获取
	cacheKey := fmt.Sprintf("content_type:%s", contentKSUID)
	contentType, err := s.redis.Get(ctx, cacheKey).Result()
	if err == nil {
		return contentType, nil
	}

	// 方法2: 通过并发查询检测内容类型（兜底方案）
	return s.detectContentType(ctx, contentKSUID)
}

// detectContentType 通过并发查询检测内容类型
func (s *contentManagementService) detectContentType(ctx context.Context, contentKSUID string) (string, error) {
	var wg sync.WaitGroup
	var mu sync.Mutex
	var detectedType string

	// 查询视频服务
	wg.Add(1)
	go func() {
		defer wg.Done()
		if _, err := s.videoClient.GetContentByKSUID(contentKSUID); err == nil {
			mu.Lock()
			detectedType = "video"
			mu.Unlock()
		}
	}()

	// 查询小说服务
	if s.novelClient != nil {
		wg.Add(1)
		go func() {
			defer wg.Done()
			// 预留小说服务查询
			log.Debug().Msg("小说服务类型检测暂未实现")
		}()
	}

	// 查询音乐服务
	if s.musicClient != nil {
		wg.Add(1)
		go func() {
			defer wg.Done()
			// 预留音乐服务查询
			log.Debug().Msg("音乐服务类型检测暂未实现")
		}()
	}

	wg.Wait()

	if detectedType == "" {
		return "", fmt.Errorf("content not found: %s", contentKSUID)
	}

	// 缓存检测结果（24小时）
	cacheKey := fmt.Sprintf("content_type:%s", contentKSUID)
	s.redis.Set(ctx, cacheKey, detectedType, 24*time.Hour)

	return detectedType, nil
}

// GetUserAllContents 获取用户的所有内容（基础信息）
func (s *contentManagementService) GetUserAllContents(ctx context.Context, userKSUID string, filters *types.ContentFilters) (*types.BaseContentList, *errors.Errors) {
	log.Info().
		Str("user_ksuid", userKSUID).
		Interface("filters", filters).
		Msg("开始获取用户所有内容")

	// 参数验证
	if userKSUID == "" {
		return nil, errors.NewValidationError("用户KSUID不能为空")
	}

	// 设置用户过滤器
	if filters == nil {
		filters = &types.ContentFilters{}
	}
	filters.UserKSUID = userKSUID

	// 先尝试从缓存获取
	cacheKey := fmt.Sprintf("user_contents:%s:%s", userKSUID, s.generateCacheKey("", filters))
	var cachedResult types.BaseContentList
	if err := s.redis.Get(ctx, cacheKey).Scan(&cachedResult); err == nil {
		log.Debug().
			Str("cache_key", cacheKey).
			Msg("从缓存获取用户内容成功")
		return &cachedResult, nil
	}

	// 缓存未命中，从各服务获取数据
	var allContents []*types.BaseContent
	var wg sync.WaitGroup
	var mu sync.Mutex

	// 获取视频内容
	wg.Add(1)
	go func() {
		defer wg.Done()
		videoContents, err := s.videoClient.GetUserContents(userKSUID, filters)
		if err != nil {
			log.Error().
				Err(err).
				Str("user_ksuid", userKSUID).
				Msg("获取用户视频内容失败")
			return
		}

		baseContents := s.converter.BatchConvertVideosToBase(videoContents.Contents)
		mu.Lock()
		allContents = append(allContents, baseContents...)
		mu.Unlock()
	}()

	// 获取小说内容
	if s.novelClient != nil {
		wg.Add(1)
		go func() {
			defer wg.Done()
			// 预留小说服务调用
			log.Debug().Msg("用户小说内容获取暂未实现")
		}()
	}

	// 获取音乐内容
	if s.musicClient != nil {
		wg.Add(1)
		go func() {
			defer wg.Done()
			// 预留音乐服务调用
			log.Debug().Msg("用户音乐内容获取暂未实现")
		}()
	}

	wg.Wait()

	// 批量获取交互统计数据
	if len(allContents) > 0 {
		s.enrichWithInteractionStats(ctx, allContents)
	}

	// 排序和分页
	sortedContents := s.sortBaseContents(allContents, filters.SortBy, filters.SortOrder)
	paginatedContents, totalPages := s.paginateBaseContents(sortedContents, filters.Page, filters.Limit)

	result := &types.BaseContentList{
		Contents:   paginatedContents,
		Total:      len(allContents),
		Page:       filters.Page,
		Limit:      filters.Limit,
		TotalPages: totalPages,
	}

	// 缓存结果（5分钟）
	s.redis.Set(ctx, cacheKey, result, 5*time.Minute)

	log.Info().
		Str("user_ksuid", userKSUID).
		Int("total_contents", len(allContents)).
		Int("page_contents", len(paginatedContents)).
		Msg("获取用户所有内容成功")

	return result, nil
}

// GetUserContentsByType 获取用户指定类型的内容（带详细信息）
func (s *contentManagementService) GetUserContentsByType(ctx context.Context, userKSUID string, contentType string, filters *types.ContentFilters) (*types.ContentWithDetailsList, *errors.Errors) {
	log.Info().
		Str("user_ksuid", userKSUID).
		Str("content_type", contentType).
		Interface("filters", filters).
		Msg("开始获取用户指定类型内容")

	// 参数验证
	if userKSUID == "" {
		return nil, errors.NewValidationError("用户KSUID不能为空")
	}
	if contentType == "" {
		return nil, errors.NewValidationError("内容类型不能为空")
	}

	// 设置默认过滤器
	if filters == nil {
		filters = &types.ContentFilters{}
	}
	filters.UserKSUID = userKSUID

	switch contentType {
	case "video":
		videoContents, err := s.videoClient.GetUserContents(userKSUID, filters)
		if err != nil {
			log.Error().
				Err(err).
				Str("user_ksuid", userKSUID).
				Msg("获取用户视频内容失败")
			return nil, errors.NewInternalError("获取用户视频内容失败")
		}

		var detailsContents []*types.ContentWithDetails
		for _, content := range videoContents.Contents {
			detailsContents = append(detailsContents, s.converter.ConvertVideoToDetails(content))
		}

		// 批量获取交互统计
		if len(detailsContents) > 0 {
			contentKSUIDs := make([]string, len(detailsContents))
			for i, content := range detailsContents {
				contentKSUIDs[i] = content.ContentKSUID
			}

			interactionStats, err := s.interactionClient.BatchGetContentInteractionStats(contentKSUIDs)
			if err == nil {
				for _, content := range detailsContents {
					if stats, exists := interactionStats[content.ContentKSUID]; exists {
						content.LikeCount = stats.LikeCount
						content.FavoriteCount = stats.FavoriteCount
					}
				}
			}
		}

		return &types.ContentWithDetailsList{
			Contents:    detailsContents,
			Total:       len(detailsContents),
			Page:        filters.Page,
			Limit:       filters.Limit,
			TotalPages:  (len(detailsContents) + filters.Limit - 1) / filters.Limit,
			ContentType: contentType,
		}, nil

	case "novel":
		if s.novelClient == nil {
			return nil, errors.NewServiceUnavailableError("小说服务不可用")
		}
		// 预留小说服务调用
		return nil, errors.NewNotImplementedError("小说服务暂未实现")

	case "music":
		if s.musicClient == nil {
			return nil, errors.NewServiceUnavailableError("音乐服务不可用")
		}
		// 预留音乐服务调用
		return nil, errors.NewNotImplementedError("音乐服务暂未实现")

	default:
		return nil, errors.NewValidationError("不支持的内容类型")
	}
}

// UpdateContentStatus 更新内容状态
func (s *contentManagementService) UpdateContentStatus(ctx context.Context, operatorKSUID string, contentKSUID string, status string) *errors.Errors {
	log.Info().
		Str("operator_ksuid", operatorKSUID).
		Str("content_ksuid", contentKSUID).
		Str("status", status).
		Msg("开始更新内容状态")

	// 参数验证
	if operatorKSUID == "" {
		return errors.NewValidationError("操作者KSUID不能为空")
	}
	if contentKSUID == "" {
		return errors.NewValidationError("内容KSUID不能为空")
	}
	if status == "" {
		return errors.NewValidationError("状态不能为空")
	}

	// 验证状态值
	if gErr := s.validator.ValidateContentStatus(status); gErr != nil {
		return gErr
	}

	// 获取内容类型
	contentType, err := s.getContentType(ctx, contentKSUID)
	if err != nil {
		log.Error().
			Err(err).
			Str("content_ksuid", contentKSUID).
			Msg("获取内容类型失败")
		return errors.NewNotFoundError("内容不存在")
	}

	// 根据内容类型调用相应服务
	switch contentType {
	case "video":
		if err := s.videoClient.UpdateContentStatus(contentKSUID, status); err != nil {
			log.Error().
				Err(err).
				Str("content_ksuid", contentKSUID).
				Str("status", status).
				Msg("更新视频内容状态失败")
			return errors.NewInternalError("更新视频内容状态失败")
		}

	case "novel":
		if s.novelClient == nil {
			return errors.NewServiceUnavailableError("小说服务不可用")
		}
		// 预留小说服务调用
		return errors.NewNotImplementedError("小说服务暂未实现")

	case "music":
		if s.musicClient == nil {
			return errors.NewServiceUnavailableError("音乐服务不可用")
		}
		// 预留音乐服务调用
		return errors.NewNotImplementedError("音乐服务暂未实现")

	default:
		return errors.NewValidationError("不支持的内容类型")
	}

	// 记录操作日志
	s.logOperation(ctx, operatorKSUID, "update_content_status", "content", contentKSUID,
		fmt.Sprintf("更新内容状态为: %s", status), nil, map[string]interface{}{"status": status})

	// 清除相关缓存
	s.clearContentCache(ctx, contentKSUID)

	log.Info().
		Str("operator_ksuid", operatorKSUID).
		Str("content_ksuid", contentKSUID).
		Str("status", status).
		Msg("更新内容状态成功")

	return nil
}

// DeleteContent 删除内容
func (s *contentManagementService) DeleteContent(ctx context.Context, operatorKSUID string, contentKSUID string) *errors.Errors {
	log.Info().
		Str("operator_ksuid", operatorKSUID).
		Str("content_ksuid", contentKSUID).
		Msg("开始删除内容")

	// 参数验证
	if operatorKSUID == "" {
		return errors.NewValidationError("操作者KSUID不能为空")
	}
	if contentKSUID == "" {
		return errors.NewValidationError("内容KSUID不能为空")
	}

	// 获取内容类型
	contentType, err := s.getContentType(ctx, contentKSUID)
	if err != nil {
		log.Error().
			Err(err).
			Str("content_ksuid", contentKSUID).
			Msg("获取内容类型失败")
		return errors.NewNotFoundError("内容不存在")
	}

	// 根据内容类型调用相应服务
	switch contentType {
	case "video":
		if err := s.videoClient.DeleteContent(contentKSUID); err != nil {
			log.Error().
				Err(err).
				Str("content_ksuid", contentKSUID).
				Msg("删除视频内容失败")
			return errors.NewInternalError("删除视频内容失败")
		}

	case "novel":
		if s.novelClient == nil {
			return errors.NewServiceUnavailableError("小说服务不可用")
		}
		// 预留小说服务调用
		return errors.NewNotImplementedError("小说服务暂未实现")

	case "music":
		if s.musicClient == nil {
			return errors.NewServiceUnavailableError("音乐服务不可用")
		}
		// 预留音乐服务调用
		return errors.NewNotImplementedError("音乐服务暂未实现")

	default:
		return errors.NewValidationError("不支持的内容类型")
	}

	// 记录操作日志
	s.logOperation(ctx, operatorKSUID, "delete_content", "content", contentKSUID,
		"删除内容", nil, nil)

	// 清除相关缓存
	s.clearContentCache(ctx, contentKSUID)

	log.Info().
		Str("operator_ksuid", operatorKSUID).
		Str("content_ksuid", contentKSUID).
		Msg("删除内容成功")

	return nil
}

// BatchUpdateContentStatus 批量更新内容状态
func (s *contentManagementService) BatchUpdateContentStatus(ctx context.Context, operatorKSUID string, contentKSUIDs []string, status string) *errors.Errors {
	log.Info().
		Str("operator_ksuid", operatorKSUID).
		Int("content_count", len(contentKSUIDs)).
		Str("status", status).
		Msg("开始批量更新内容状态")

	// 参数验证
	if operatorKSUID == "" {
		return errors.NewValidationError("操作者KSUID不能为空")
	}
	if len(contentKSUIDs) == 0 {
		return errors.NewValidationError("内容KSUID列表不能为空")
	}
	if status == "" {
		return errors.NewValidationError("状态不能为空")
	}

	// 验证状态值
	if gErr := s.validator.ValidateContentStatus(status); gErr != nil {
		return gErr
	}

	// 按内容类型分组
	contentsByType, gErr := s.groupContentsByType(ctx, contentKSUIDs)
	if gErr != nil {
		return gErr
	}

	var wg sync.WaitGroup
	var mu sync.Mutex
	var batchErrors []error

	// 并发更新各服务的内容
	for contentType, ksUIDs := range contentsByType {
		wg.Add(1)
		go func(cType string, ksUIDs []string) {
			defer wg.Done()

			var err error
			switch cType {
			case "video":
				err = s.videoClient.BatchUpdateStatus(ksUIDs, status)
			case "novel":
				if s.novelClient != nil {
					// 预留小说服务调用
					log.Debug().Msg("批量更新小说状态暂未实现")
				} else {
					err = fmt.Errorf("novel service unavailable")
				}
			case "music":
				if s.musicClient != nil {
					// 预留音乐服务调用
					log.Debug().Msg("批量更新音乐状态暂未实现")
				} else {
					err = fmt.Errorf("music service unavailable")
				}
			}

			if err != nil {
				mu.Lock()
				batchErrors = append(batchErrors, fmt.Errorf("failed to update %s contents: %w", cType, err))
				mu.Unlock()
			}
		}(contentType, ksUIDs)
	}

	wg.Wait()

	if len(batchErrors) > 0 {
		log.Error().
			Interface("errors", batchErrors).
			Msg("批量更新内容状态失败")
		return errors.NewInternalError("批量更新内容状态失败")
	}

	// 记录操作日志
	s.logOperation(ctx, operatorKSUID, "batch_update_content_status", "content", "",
		fmt.Sprintf("批量更新%d个内容状态为: %s", len(contentKSUIDs), status),
		nil, map[string]interface{}{
			"content_ksuids": contentKSUIDs,
			"status": status,
		})

	// 清除相关缓存
	for _, contentKSUID := range contentKSUIDs {
		s.clearContentCache(ctx, contentKSUID)
	}

	log.Info().
		Str("operator_ksuid", operatorKSUID).
		Int("content_count", len(contentKSUIDs)).
		Str("status", status).
		Msg("批量更新内容状态成功")

	return nil
}

// BatchDeleteContents 批量删除内容
func (s *contentManagementService) BatchDeleteContents(ctx context.Context, operatorKSUID string, contentKSUIDs []string) *errors.Errors {
	log.Info().
		Str("operator_ksuid", operatorKSUID).
		Int("content_count", len(contentKSUIDs)).
		Msg("开始批量删除内容")

	// 参数验证
	if operatorKSUID == "" {
		return errors.NewValidationError("操作者KSUID不能为空")
	}
	if len(contentKSUIDs) == 0 {
		return errors.NewValidationError("内容KSUID列表不能为空")
	}

	// 按内容类型分组
	contentsByType, gErr := s.groupContentsByType(ctx, contentKSUIDs)
	if gErr != nil {
		return gErr
	}

	var wg sync.WaitGroup
	var mu sync.Mutex
	var batchErrors []error

	// 并发删除各服务的内容
	for contentType, ksUIDs := range contentsByType {
		wg.Add(1)
		go func(cType string, ksUIDs []string) {
			defer wg.Done()

			var err error
			switch cType {
			case "video":
				err = s.videoClient.BatchDeleteContents(ksUIDs)
			case "novel":
				if s.novelClient != nil {
					// 预留小说服务调用
					log.Debug().Msg("批量删除小说内容暂未实现")
				} else {
					err = fmt.Errorf("novel service unavailable")
				}
			case "music":
				if s.musicClient != nil {
					// 预留音乐服务调用
					log.Debug().Msg("批量删除音乐内容暂未实现")
				} else {
					err = fmt.Errorf("music service unavailable")
				}
			}

			if err != nil {
				mu.Lock()
				batchErrors = append(batchErrors, fmt.Errorf("failed to delete %s contents: %w", cType, err))
				mu.Unlock()
			}
		}(contentType, ksUIDs)
	}

	wg.Wait()

	if len(batchErrors) > 0 {
		log.Error().
			Interface("errors", batchErrors).
			Msg("批量删除内容失败")
		return errors.NewInternalError("批量删除内容失败")
	}

	// 记录操作日志
	s.logOperation(ctx, operatorKSUID, "batch_delete_content", "content", "",
		fmt.Sprintf("批量删除%d个内容", len(contentKSUIDs)),
		nil, map[string]interface{}{
			"content_ksuids": contentKSUIDs,
		})

	// 清除相关缓存
	for _, contentKSUID := range contentKSUIDs {
		s.clearContentCache(ctx, contentKSUID)
	}

	log.Info().
		Str("operator_ksuid", operatorKSUID).
		Int("content_count", len(contentKSUIDs)).
		Msg("批量删除内容成功")

	return nil
}

// groupContentsByType 按内容类型分组（需要先检测每个内容的类型）
func (s *contentManagementService) groupContentsByType(ctx context.Context, contentKSUIDs []string) (map[string][]string, *errors.Errors) {
	contentsByType := make(map[string][]string)

	// 并发检测每个内容的类型
	var wg sync.WaitGroup
	var mu sync.Mutex
	var detectionErrors []error

	for _, contentKSUID := range contentKSUIDs {
		wg.Add(1)
		go func(ksuid string) {
			defer wg.Done()

			contentType, err := s.getContentType(ctx, ksuid)
			if err != nil {
				mu.Lock()
				detectionErrors = append(detectionErrors, fmt.Errorf("failed to detect type for %s: %w", ksuid, err))
				mu.Unlock()
				return
			}

			mu.Lock()
			contentsByType[contentType] = append(contentsByType[contentType], ksuid)
			mu.Unlock()
		}(contentKSUID)
	}

	wg.Wait()

	if len(detectionErrors) > 0 {
		log.Error().
			Interface("errors", detectionErrors).
			Msg("检测内容类型失败")
		return nil, errors.NewInternalError("检测内容类型失败")
	}

	return contentsByType, nil
}

// logOperation 记录操作日志
func (s *contentManagementService) logOperation(ctx context.Context, operatorKSUID, operationType, targetType, targetKSUID, description string, beforeData, afterData interface{}) {
	// 这里可以异步记录操作日志，避免影响主流程
	go func() {
		if err := s.operationRepo.Create(ctx, &repository.OperationLogCreateRequest{
			OperatorKSUID: operatorKSUID,
			OperationType: operationType,
			TargetType:    targetType,
			TargetKSUID:   targetKSUID,
			Description:   description,
			BeforeData:    beforeData,
			AfterData:     afterData,
		}); err != nil {
			log.Error().
				Err(err).
				Str("operator_ksuid", operatorKSUID).
				Str("operation_type", operationType).
				Msg("记录操作日志失败")
		}
	}()
}

// clearContentCache 清除内容相关缓存
func (s *contentManagementService) clearContentCache(ctx context.Context, contentKSUID string) {
	// 清除内容类型缓存
	s.redis.Del(ctx, fmt.Sprintf("content_type:%s", contentKSUID))

	// 清除其他相关缓存（可以根据需要扩展）
	// 这里可以使用模式匹配删除相关缓存
	log.Debug().
		Str("content_ksuid", contentKSUID).
		Msg("清除内容缓存完成")
}

// GetVideoContents 获取视频内容列表（带详细信息）
func (s *contentManagementService) GetVideoContents(ctx context.Context, filters *types.ContentFilters) (*types.ContentWithDetailsList, *errors.Errors) {
	log.Info().
		Interface("filters", filters).
		Msg("开始获取视频内容列表")

	// 参数验证
	if gErr := s.validator.ValidateContentFilters(filters); gErr != nil {
		return nil, gErr
	}

	// 从视频服务获取内容
	videoContents, err := s.videoClient.GetContentsByFilters(filters)
	if err != nil {
		log.Error().
			Err(err).
			Interface("filters", filters).
			Msg("获取视频内容失败")
		return nil, errors.NewInternalError("获取视频内容失败")
	}

	// 转换为详细内容
	var detailsContents []*types.ContentWithDetails
	for _, content := range videoContents.Contents {
		detailsContents = append(detailsContents, s.converter.ConvertVideoToDetails(content))
	}

	// 批量获取交互统计
	if len(detailsContents) > 0 {
		contentKSUIDs := make([]string, len(detailsContents))
		for i, content := range detailsContents {
			contentKSUIDs[i] = content.ContentKSUID
		}

		interactionStats, err := s.interactionClient.BatchGetContentInteractionStats(contentKSUIDs)
		if err == nil {
			for _, content := range detailsContents {
				if stats, exists := interactionStats[content.ContentKSUID]; exists {
					content.LikeCount = stats.LikeCount
					content.FavoriteCount = stats.FavoriteCount
				}
			}
		}
	}

	result := &types.ContentWithDetailsList{
		Contents:    detailsContents,
		Total:       len(detailsContents),
		Page:        filters.Page,
		Limit:       filters.Limit,
		TotalPages:  (len(detailsContents) + filters.Limit - 1) / filters.Limit,
		ContentType: "video",
	}

	log.Info().
		Int("content_count", len(detailsContents)).
		Msg("获取视频内容列表成功")

	return result, nil
}

// GetNovelContents 获取小说内容列表（带详细信息）
func (s *contentManagementService) GetNovelContents(ctx context.Context, filters *types.ContentFilters) (*types.ContentWithDetailsList, *errors.Errors) {
	log.Info().
		Interface("filters", filters).
		Msg("开始获取小说内容列表")

	if s.novelClient == nil {
		return nil, errors.NewServiceUnavailableError("小说服务不可用")
	}

	// 预留小说服务实现
	return nil, errors.NewNotImplementedError("小说服务暂未实现")
}

// GetMusicContents 获取音乐内容列表（带详细信息）
func (s *contentManagementService) GetMusicContents(ctx context.Context, filters *types.ContentFilters) (*types.ContentWithDetailsList, *errors.Errors) {
	log.Info().
		Interface("filters", filters).
		Msg("开始获取音乐内容列表")

	if s.musicClient == nil {
		return nil, errors.NewServiceUnavailableError("音乐服务不可用")
	}

	// 预留音乐服务实现
	return nil, errors.NewNotImplementedError("音乐服务暂未实现")
}

// HealthCheck 健康检查
func (s *contentManagementService) HealthCheck(ctx context.Context) *errors.Errors {
	log.Debug().Msg("开始健康检查")

	var healthErrors []error

	// 检查视频服务
	if err := s.videoClient.HealthCheck(); err != nil {
		healthErrors = append(healthErrors, fmt.Errorf("video service unhealthy: %w", err))
	}

	// 检查交互服务
	if err := s.interactionClient.HealthCheck(); err != nil {
		healthErrors = append(healthErrors, fmt.Errorf("interaction service unhealthy: %w", err))
	}

	// 检查小说服务（如果启用）
	if s.novelClient != nil {
		// 预留小说服务健康检查
		log.Debug().Msg("小说服务健康检查暂未实现")
	}

	// 检查音乐服务（如果启用）
	if s.musicClient != nil {
		// 预留音乐服务健康检查
		log.Debug().Msg("音乐服务健康检查暂未实现")
	}

	// 检查Redis连接
	if err := s.redis.Ping(ctx).Err(); err != nil {
		healthErrors = append(healthErrors, fmt.Errorf("redis unhealthy: %w", err))
	}

	if len(healthErrors) > 0 {
		log.Error().
			Interface("errors", healthErrors).
			Msg("健康检查失败")
		return errors.NewServiceUnavailableError("部分服务不健康")
	}

	log.Debug().Msg("健康检查通过")
	return nil
}
