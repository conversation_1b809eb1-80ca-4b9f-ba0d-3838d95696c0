package main

import (
	"context"
	"flag"
	"os"
	"os/signal"
	"syscall"
	"time"

	"pxpat-backend/cmd"
	"pxpat-backend/internal/finance-cluster/token-service/blockchain"
	"pxpat-backend/internal/finance-cluster/token-service/cache"
	"pxpat-backend/internal/finance-cluster/token-service/config"
	"pxpat-backend/internal/finance-cluster/token-service/database"
	"pxpat-backend/internal/finance-cluster/token-service/handler"
	"pxpat-backend/internal/finance-cluster/token-service/repository"
	"pxpat-backend/internal/finance-cluster/token-service/routes"
	"pxpat-backend/internal/finance-cluster/token-service/service"
	"pxpat-backend/internal/finance-cluster/token-service/types"
	"pxpat-backend/pkg/auth"
	pkgcache "pxpat-backend/pkg/cache"
	configLoader "pxpat-backend/pkg/config"
	"pxpat-backend/pkg/consul"
	"pxpat-backend/pkg/consul/health"
	database2 "pxpat-backend/pkg/database"
	"pxpat-backend/pkg/logger"
	"pxpat-backend/pkg/middleware/cors"

	"github.com/ethereum/go-ethereum/common"
	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog/log"
)

// convertBlockchainConfig 转换区块链配置
func convertBlockchainConfig(typesConfig *types.BlockchainConfig) *config.BlockchainConfig {
	return &config.BlockchainConfig{
		Network:             typesConfig.Network,
		RPCURL:              typesConfig.RPCURL,
		ChainID:             typesConfig.ChainID,
		StartBlock:          0,     // 设置默认值
		ConfirmBlocks:       12,    // 设置默认值
		BatchSize:           1000,  // 设置默认值
		BSCScanAPIKey:       "",    // 设置默认值
		UseBSCScanForEvents: false, // 设置默认值
		Contracts: config.ContractsConfig{
			PXTToken:        typesConfig.Contracts.PXTToken,
			PATToken:        typesConfig.Contracts.PATToken,
			StakingPool:     typesConfig.Contracts.StakingPool,
			DAO:             typesConfig.Contracts.DAO,
			ContentRegistry: typesConfig.Contracts.ContentRegistry,
		},
	}
}

// convertTransferConfig 转换转账配置
func convertTransferConfig(typesConfig *types.TransferConfig) config.TransferConfig {
	return config.TransferConfig{
		SystemPrivateKey: typesConfig.SystemPrivateKey,
		SystemAddress:    "",          // 需要从配置中获取
		GasLimit:         100000,      // 设置默认值
		GasPrice:         20000000000, // 设置默认值
	}
}

// convertSyncConfig 转换同步配置
func convertSyncConfig(typesConfig *types.SyncConfig) *config.SyncConfig {
	return &config.SyncConfig{
		Enabled:                     typesConfig.Enabled,
		SyncInterval:                typesConfig.SyncInterval,
		MaxRetries:                  typesConfig.MaxRetries,
		RetryDelay:                  typesConfig.RetryDelay,
		ConcurrentWorkers:           typesConfig.ConcurrentWorkers,
		EnableDailyFullSync:         typesConfig.EnableDailyFullSync,
		DailyFullSyncHour:           typesConfig.DailyFullSyncHour,
		DailyFullSyncMinute:         typesConfig.DailyFullSyncMinute,
		TransferSyncIntervalSeconds: typesConfig.TransferSyncIntervalSeconds,
		UserSyncIntervalSeconds:     typesConfig.UserSyncIntervalSeconds,
		TransferBatchSize:           typesConfig.TransferBatchSize,
		ContentBatchSize:            typesConfig.ContentBatchSize,
		BurnBatchSize:               typesConfig.BurnBatchSize,
		MaxSyncDurationSeconds:      typesConfig.MaxSyncDurationSeconds,
		EnableMetrics:               typesConfig.EnableMetrics,
		MetricsRetentionDays:        typesConfig.MetricsRetentionDays,
		LogRetentionDays:            typesConfig.LogRetentionDays,
	}
}

func main() {
	// 解析命令行参数
	var (
		migrate = flag.Bool("migrate", false, "Run database migration")
		action  = flag.String("action", "up", "Migration action: up, down, reset")
		force   = flag.Bool("force", false, "Force migration (for reset)")
	)
	flag.Parse()

	clusterName := "finance"
	serviceName := "token"

	// 加载配置
	cfg := configLoader.Load[types.Config](configLoader.LoaderConfig{
		TypeName:    configLoader.Service,
		ClusterName: clusterName,
		ServiceName: serviceName,
		UseRedis:    true,
	})

	// 初始化日志系统
	if err := logger.InitLogger(cfg.Log, serviceName); err != nil {
		log.Fatal().Err(err).Msg("Failed to initialize logger")
	}

	// 获取服务专用日志器
	log.Info().Msg("Token service starting...")

	// 如果是迁移模式，执行迁移后退出
	if *migrate {
		runMigration(cfg, *action, *force)
		return
	}

	// 初始化数据库
	db, err := database2.ConnectDB(cfg.Database)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to initialize database")
	}
	log.Info().Msg("Database connected successfully")

	// 执行数据库迁移
	log.Info().Msg("Running database migrations...")
	if err := database.AutoMigrate(db); err != nil {
		log.Fatal().Err(err).Msg("Failed to run database migrations")
	}
	log.Info().Msg("Database migration completed")

	// 创建视图和函数
	log.Info().Msg("Creating database views and functions...")
	if err := database.CreateViews(db); err != nil {
		log.Warn().Err(err).Msg("Failed to create views (continuing anyway)")
	}
	if err := database.CreateFunctions(db); err != nil {
		log.Warn().Err(err).Msg("Failed to create functions (continuing anyway)")
	}

	// 初始化Redis缓存
	rdb, err := database2.ConnectRedis(cfg.Redis)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to initialize Redis")
	}
	log.Info().Msg("Redis connected successfully")

	_, err = pkgcache.NewManager(rdb)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to initialize cache manager")
	}
	log.Info().Msg("Cache manager initialized")

	// 初始化token-service专用缓存管理器
	redisConfig := cache.RedisConfig{
		Host:                cfg.Redis.Host,
		Port:                cfg.Redis.Port,
		Password:            cfg.Redis.Password,
		DB:                  cfg.Redis.DB,
		PoolSize:            cfg.Redis.PoolSize,
		MinIdleConns:        cfg.Redis.MinIdleConns,
		DialTimeoutSeconds:  cfg.Redis.DialTimeout.String(),
		ReadTimeoutSeconds:  cfg.Redis.ReadTimeout.String(),
		WriteTimeoutSeconds: cfg.Redis.WriteTimeout.String(),
		PoolTimeoutSeconds:  cfg.Redis.PoolTimeout.String(),
	}
	redisClient, err := cache.NewRedisClient(redisConfig, log.Logger)
	if err != nil {
		log.Warn().Err(err).Msg("Failed to connect to Redis, continuing without cache")
		redisClient = nil
	}

	// 初始化缓存管理器
	var cacheManager *cache.CacheManager
	if redisClient != nil {
		cacheConfig := cache.CacheConfig{
			LeaderboardTTL: 5 * time.Second,  // 5秒缓存，极致实时性
			UserStatsTTL:   15 * time.Second, // 15秒缓存
			OverviewTTL:    3 * time.Second,  // 3秒缓存，极致实时性
			DefaultTTL:     30 * time.Second,
		}
		cacheManager = cache.NewCacheManager(redisClient, cacheConfig, log.Logger)
		log.Info().Msg("Redis cache initialized successfully")
	} else {
		log.Info().Msg("Running without Redis cache")
	}

	// 转换配置
	blockchainConfig := convertBlockchainConfig(&cfg.Blockchain)
	transferConfig := convertTransferConfig(&cfg.Transfer)
	syncConfig := convertSyncConfig(&cfg.Sync)

	// 初始化区块链客户端
	multiClient, err := blockchain.NewMultiContractClient(blockchainConfig, log.Logger)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to create multi-contract client")
	}
	defer multiClient.Close()

	// 初始化Repository层
	stakingRepo := repository.NewStakingRepository(db)
	eventRepo := repository.NewEventRepository(db)
	tokenRepo := repository.NewTokenRepository(db)
	governanceRepo := repository.NewGovernanceRepository(db)
	contentRepo := repository.NewContentRepository(db)

	// 初始化转账服务
	transferService, err := service.NewTransferService(
		cfg.Blockchain.RPCURL,
		transferConfig,
		log.Logger,
	)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to initialize transfer service")
	}

	// 解析合约地址
	patTokenAddr := common.HexToAddress(cfg.Blockchain.Contracts.PATToken)
	pxtTokenAddr := common.HexToAddress(cfg.Blockchain.Contracts.PXTToken)
	contentRegistryAddr := common.HexToAddress(cfg.Blockchain.Contracts.ContentRegistry)

	// 初始化Service层
	stakingService := service.NewStakingService(stakingRepo, eventRepo, cacheManager, log.Logger)
	leaderboardService := service.NewLeaderboardService(stakingRepo, cacheManager, log.Logger)
	tokenService := service.NewTokenService(tokenRepo, multiClient, cacheManager, log.Logger)
	governanceService := service.NewGovernanceService(governanceRepo, stakingRepo, multiClient, cacheManager, log.Logger)
	transactionService := service.NewTransactionService(multiClient, cacheManager, log.Logger)
	contentService := service.NewContentService(contentRepo, multiClient, transferService, patTokenAddr, pxtTokenAddr, contentRegistryAddr, log.Logger)
	syncService := service.NewSyncService(multiClient, blockchainConfig, tokenRepo, governanceRepo, stakingRepo, cacheManager, log.Logger, nil)

	// 初始化高级功能服务
	analyticsService := service.NewAnalyticsService(tokenRepo, stakingRepo, governanceRepo, cacheManager, log.Logger)
	rankingService := service.NewRankingService(stakingRepo, tokenRepo, governanceRepo, cacheManager, log.Logger)
	websocketService := service.NewWebSocketService(log.Logger)
	notificationService := service.NewNotificationService(websocketService, log.Logger)
	_ = service.NewEventNotifier(notificationService, rankingService, log.Logger) // 暂时不使用

	// 初始化区块链管理器
	blockchainManager := service.NewBlockchainManager(
		multiClient,
		blockchainConfig,
		stakingRepo,
		eventRepo,
		tokenRepo,
		governanceRepo,
		cacheManager,
		log.Logger,
		syncConfig, // 🌙 传入同步配置
	)

	// 初始化高级功能处理器
	analyticsHandler := handler.NewAnalyticsHandler(analyticsService, websocketService, log.Logger)
	contentHandler := handler.NewContentHandler(contentService, log.Logger)

	// 初始化HTTP处理器 (暂时不使用，由routes处理)
	_ = handler.NewHTTPHandler(
		stakingService,
		leaderboardService,
		tokenService,
		governanceService,
		transactionService,
		analyticsHandler,
		websocketService,
		syncService,
		log.Logger,
	)

	// 初始化JWT管理器 (暂时不使用)
	_ = auth.NewJWTManager(cfg.JWT.SecretKey, cfg.JWT.Expiration, cfg.JWT.Issuer)
	log.Info().Msg("JWT manager initialized")

	// 设置Gin模式
	gin.SetMode(cfg.Server.Mode)
	log.Info().Str("mode", cfg.Server.Mode).Msg("Gin mode set")

	// 创建路由
	router := gin.Default()

	// 添加中间件
	router.Use(cors.CORSMiddleware(cfg.Security.CORS))
	log.Info().Msg("Middleware configured")

	// 注册路由
	routes.RegisterRoutes(
		router,
		stakingService,
		leaderboardService,
		tokenService,
		governanceService,
		transactionService,
		analyticsService,
		rankingService,
		websocketService,
		syncService,
		log.Logger,
	)

	// 注册内容路由
	v1 := router.Group("/api/v1")
	handler.RegisterContentRoutes(v1, contentHandler)
	log.Info().Msg("Routes registered")

	// 初始化Consul管理器
	consulManager, err := consul.NewManager(&cfg.Consul)
	if err != nil {
		log.Fatal().Err(err).Msg("Consul管理器初始化失败")
	}

	// 启动Consul管理器
	consulCtx := context.Background()
	if err := consulManager.Start(consulCtx); err != nil {
		log.Fatal().Err(err).Msg("Consul管理器启动失败")
	}

	// 初始化健康检查处理器
	healthHandler := consul.NewHealthHandler(consulManager, "1.0.0")

	// 添加数据库健康检查
	healthHandler.AddChecker(health.NewDatabaseHealthChecker("database", func() error {
		sqlDB, err := db.DB()
		if err != nil {
			return err
		}
		return sqlDB.Ping()
	}))

	// 注册健康检查路由
	healthHandler.RegisterHealthRoutes(router)

	// 启动区块链管理器
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	go func() {
		log.Info().Msg("Starting blockchain manager...")
		if err := blockchainManager.Start(ctx); err != nil {
			log.Error().Err(err).Msg("Blockchain manager failed")
		}
	}()

	// 启动自动同步服务
	go func() {
		log.Info().Msg("Starting auto sync service...")
		syncService.StartAutoSync(ctx)
	}()

	// 启动服务器
	log.Info().Int("port", cfg.Server.Port).Msg("Starting token service server")

	// 在单独的goroutine中启动服务器，以便可以处理关闭信号
	go func() {
		cmd.GraceStartAndClose(cfg.Server, router)
	}()

	// 等待中断信号
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	log.Info().Msg("Shutting down server...")

	// 停止区块链管理器
	if err := blockchainManager.Stop(); err != nil {
		log.Error().Err(err).Msg("Failed to stop blockchain manager")
	}

	// 停止Consul管理器
	if err := consulManager.Stop(); err != nil {
		log.Error().Err(err).Msg("停止Consul管理器失败")
	}

	log.Info().Msg("Server exited")
}

// runMigration 执行数据库迁移
func runMigration(cfg *types.Config, action string, force bool) {
	// 连接数据库
	db, err := database2.ConnectDB(cfg.Database)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to connect to database")
	}

	// 执行迁移操作
	switch action {
	case "up":
		log.Info().Msg("Running migrations...")
		if err := database.AutoMigrate(db); err != nil {
			log.Fatal().Err(err).Msg("Failed to run migrations")
		}

		log.Info().Msg("Creating views and functions...")
		if err := database.CreateViews(db); err != nil {
			log.Error().Err(err).Msg("Failed to create views")
		}
		if err := database.CreateFunctions(db); err != nil {
			log.Error().Err(err).Msg("Failed to create functions")
		}

		log.Info().Msg("Migrations completed successfully!")

	case "down":
		if !force {
			log.Fatal().Msg("Down migration requires --force flag")
		}

		log.Warn().Msg("Dropping all tables...")
		if err := database.DropTables(db); err != nil {
			log.Fatal().Err(err).Msg("Failed to drop tables")
		}

		log.Info().Msg("Tables dropped successfully!")

	case "reset":
		if !force {
			log.Fatal().Msg("Reset requires --force flag")
		}

		log.Warn().Msg("Resetting database...")

		// 先删除所有表
		if err := database.DropTables(db); err != nil {
			log.Error().Err(err).Msg("Failed to drop tables")
		}

		// 重新创建
		if err := database.AutoMigrate(db); err != nil {
			log.Fatal().Err(err).Msg("Failed to run migrations")
		}

		if err := database.CreateViews(db); err != nil {
			log.Error().Err(err).Msg("Failed to create views")
		}
		if err := database.CreateFunctions(db); err != nil {
			log.Error().Err(err).Msg("Failed to create functions")
		}

		log.Info().Msg("Database reset completed!")

	default:
		log.Fatal().Str("action", action).Msg("Unknown migration action. Available actions: up, down, reset")
	}
}
